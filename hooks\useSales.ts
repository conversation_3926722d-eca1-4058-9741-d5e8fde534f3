import { useQuery } from '@tanstack/react-query';
import { api } from '../lib/api';
import { 
  Sale, 
  SaleWithDetails,
  ApiResponse 
} from '../types/sanity';

// Query parameters interface
export interface SaleQueryParams {
  isActive?: boolean;
  discountType?: 'percentage' | 'fixed_amount';
  validFrom?: string;
  validTo?: string;
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: 'title' | 'createdAt' | 'validityFrom' | 'discountAmount';
  sortOrder?: 'asc' | 'desc';
  language?: 'tr' | 'en';
}

// API functions
const fetchSales = async (params?: SaleQueryParams): Promise<Sale[]> => {
  const response = await api.get('/sales', params);
  return response.data;
};

const fetchSaleById = async (id: string): Promise<Sale | null> => {
  const response = await api.get(`/sales/${id}`);
  return response.data;
};

const fetchSaleByCouponCode = async (couponCode: string): Promise<Sale | null> => {
  const response = await api.get(`/sales/coupon/${couponCode}`);
  return response.data;
};

const fetchActiveSales = async (): Promise<Sale[]> => {
  const response = await api.get('/sales/active');
  return response.data;
};

const fetchExpiredSales = async (): Promise<Sale[]> => {
  const response = await api.get('/sales/expired');
  return response.data;
};

const fetchUpcomingSales = async (): Promise<Sale[]> => {
  const response = await api.get('/sales/upcoming');
  return response.data;
};

const validateCouponCode = async (couponCode: string, userId?: string): Promise<{ valid: boolean; sale?: Sale; message?: string }> => {
  const response = await api.post('/sales/validate-coupon', { couponCode, userId });
  return response.data;
};

// React Query hooks
export const useSales = (params?: SaleQueryParams) => {
  return useQuery<Sale[], Error>({
    queryKey: ['sales', params],
    queryFn: () => fetchSales(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    refetchOnWindowFocus: false,
  });
};

export const useSale = (id: string) => {
  return useQuery<Sale | null, Error>({
    queryKey: ['sale', id],
    queryFn: () => fetchSaleById(id),
    staleTime: 5 * 60 * 1000,
    retry: 2,
    enabled: !!id,
  });
};

export const useSaleByCouponCode = (couponCode: string) => {
  return useQuery<Sale | null, Error>({
    queryKey: ['sale', 'coupon', couponCode],
    queryFn: () => fetchSaleByCouponCode(couponCode),
    staleTime: 2 * 60 * 1000, // 2 minutes for coupon validation
    retry: 1,
    enabled: !!couponCode && couponCode.length >= 3,
  });
};

export const useActiveSales = () => {
  return useQuery<Sale[], Error>({
    queryKey: ['sales', 'active'],
    queryFn: fetchActiveSales,
    staleTime: 3 * 60 * 1000, // 3 minutes for active sales
    retry: 2,
  });
};

export const useExpiredSales = () => {
  return useQuery<Sale[], Error>({
    queryKey: ['sales', 'expired'],
    queryFn: fetchExpiredSales,
    staleTime: 10 * 60 * 1000, // 10 minutes for expired sales
    retry: 2,
  });
};

export const useUpcomingSales = () => {
  return useQuery<Sale[], Error>({
    queryKey: ['sales', 'upcoming'],
    queryFn: fetchUpcomingSales,
    staleTime: 5 * 60 * 1000,
    retry: 2,
  });
};

export const useCouponValidation = (couponCode: string, userId?: string) => {
  return useQuery<{ valid: boolean; sale?: Sale; message?: string }, Error>({
    queryKey: ['coupon', 'validate', couponCode, userId],
    queryFn: () => validateCouponCode(couponCode, userId),
    staleTime: 1 * 60 * 1000, // 1 minute for coupon validation
    retry: 1,
    enabled: !!couponCode && couponCode.length >= 3,
  });
};

// Utility hooks
export const useDiscountTypes = () => {
  const types = [
    { value: 'percentage', label: 'Yüzde İndirim', symbol: '%' },
    { value: 'fixed_amount', label: 'Sabit Tutar İndirim', symbol: '₺' },
  ] as const;

  return types;
};

// Helper functions
export const getLocalizedSaleTitle = (
  title: { tr: string; en?: string } | undefined,
  language: 'tr' | 'en' = 'tr'
): string => {
  if (!title) return '';
  return language === 'en' && title.en ? title.en : title.tr;
};

export const getLocalizedSaleDescription = (
  description: { tr?: string; en?: string } | undefined,
  language: 'tr' | 'en' = 'tr'
): string => {
  if (!description) return '';
  return language === 'en' && description.en ? description.en : description.tr || '';
};

export const isSaleActive = (sale: Sale): boolean => {
  if (!sale.isActive) return false;
  
  const now = new Date();
  const validFrom = new Date(sale.validityFrom);
  const validUntil = new Date(sale.validityUntil);
  
  return now >= validFrom && now <= validUntil;
};

export const isSaleExpired = (sale: Sale): boolean => {
  const now = new Date();
  const validUntil = new Date(sale.validityUntil);
  
  return now > validUntil;
};

export const isSaleUpcoming = (sale: Sale): boolean => {
  const now = new Date();
  const validFrom = new Date(sale.validityFrom);
  
  return now < validFrom;
};

export const getSaleStatus = (sale: Sale): 'active' | 'expired' | 'upcoming' | 'inactive' => {
  if (!sale.isActive) return 'inactive';
  if (isSaleExpired(sale)) return 'expired';
  if (isSaleUpcoming(sale)) return 'upcoming';
  return 'active';
};

export const getSaleStatusColor = (status: string): string => {
  const statusColors: Record<string, string> = {
    active: '#10b981',
    expired: '#6b7280',
    upcoming: '#3b82f6',
    inactive: '#ef4444',
  };
  return statusColors[status] || '#6b7280';
};

export const formatDiscountAmount = (sale: Sale): string => {
  if (sale.discountType === 'percentage') {
    return `%${sale.discountAmount}`;
  } else {
    return `${sale.discountAmount} ${sale.currency}`;
  }
};

export const calculateDiscountForAmount = (sale: Sale, amount: number): number => {
  if (sale.discountType === 'percentage') {
    return (amount * sale.discountAmount) / 100;
  } else {
    return Math.min(sale.discountAmount, amount);
  }
};

export const getRemainingUsage = (sale: Sale): number => {
  if (!sale.usageLimit) return Infinity;
  return Math.max(0, sale.usageLimit - sale.usedCount);
};

export const canUseSale = (sale: Sale, userId?: string): { canUse: boolean; reason?: string } => {
  if (!sale.isActive) {
    return { canUse: false, reason: 'Kampanya aktif değil' };
  }
  
  if (isSaleExpired(sale)) {
    return { canUse: false, reason: 'Kampanya süresi dolmuş' };
  }
  
  if (isSaleUpcoming(sale)) {
    return { canUse: false, reason: 'Kampanya henüz başlamamış' };
  }
  
  if (sale.usageLimit && sale.usedCount >= sale.usageLimit) {
    return { canUse: false, reason: 'Kampanya kullanım limiti dolmuş' };
  }
  
  // Check if user is in target users (if specified)
  if (sale.targetUsers && sale.targetUsers.length > 0 && userId) {
    const isTargetUser = sale.targetUsers.some(user => user._ref === userId);
    if (!isTargetUser) {
      return { canUse: false, reason: 'Bu kampanya sizin için geçerli değil' };
    }
  }
  
  return { canUse: true };
};

export const formatSaleValidity = (sale: Sale): string => {
  const validFrom = new Date(sale.validityFrom).toLocaleDateString('tr-TR');
  const validUntil = new Date(sale.validityUntil).toLocaleDateString('tr-TR');
  
  return `${validFrom} - ${validUntil}`;
};

export const getSaleProgress = (sale: Sale): number => {
  if (!sale.usageLimit) return 0;
  return (sale.usedCount / sale.usageLimit) * 100;
};

// Filter and sort helpers
export const filterSalesByStatus = (sales: Sale[], status: 'active' | 'expired' | 'upcoming' | 'inactive'): Sale[] => {
  return sales.filter(sale => getSaleStatus(sale) === status);
};

export const filterSalesBySearch = (sales: Sale[], searchTerm: string, language: 'tr' | 'en' = 'tr'): Sale[] => {
  if (!searchTerm) return sales;
  
  const term = searchTerm.toLowerCase();
  return sales.filter(sale => 
    getLocalizedSaleTitle(sale.title, language).toLowerCase().includes(term) ||
    getLocalizedSaleDescription(sale.description, language).toLowerCase().includes(term) ||
    sale.couponCode.toLowerCase().includes(term)
  );
};

export const sortSales = (
  sales: Sale[], 
  sortBy: 'title' | 'createdAt' | 'validityFrom' | 'discountAmount' = 'createdAt',
  sortOrder: 'asc' | 'desc' = 'desc',
  language: 'tr' | 'en' = 'tr'
): Sale[] => {
  return [...sales].sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'title':
        comparison = getLocalizedSaleTitle(a.title, language).localeCompare(
          getLocalizedSaleTitle(b.title, language), 'tr'
        );
        break;
      case 'validityFrom':
        comparison = new Date(a.validityFrom).getTime() - new Date(b.validityFrom).getTime();
        break;
      case 'discountAmount':
        comparison = a.discountAmount - b.discountAmount;
        break;
      case 'createdAt':
      default:
        comparison = new Date(a._createdAt).getTime() - new Date(b._createdAt).getTime();
        break;
    }
    
    return sortOrder === 'desc' ? -comparison : comparison;
  });
};
