import { NextApiRequest, NextApiResponse } from 'next'
import Stripe from 'stripe'
import { createClient } from '@sanity/client'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY! as string, {
  apiVersion: '2025-05-28.basil',
})

const client = createClient({
  projectId: process.env.SANITY_PROJECT_ID!,
  dataset: process.env.SANITY_DATASET!,
  apiVersion: '2023-06-10',
  token: process.env.SANITY_WRITE_TOKEN!,
  useCdn: false,
})

export const config = {
  api: {
    bodyParser: false,
  },
}

import { buffer } from 'micro'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST'])
    return res.status(405).end('Method Not Allowed')
  }

  const sig = req.headers['stripe-signature']!;
  const buf = await buffer(req);
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(buf, sig, webhookSecret);
  } catch (err: unknown) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Webhook signature verification failed.', errorMessage);
    return res.status(400).end(`Webhook Error: ${errorMessage}`);
  }

  if (event.type === 'payment_intent.succeeded') {
    const paymentIntent = event.data.object as Stripe.PaymentIntent;
    const metadata = paymentIntent.metadata;
    const userId = metadata.userId;
    const drawId = metadata.drawId;
    const numbers = metadata.numbers.split(',').map((n: string) => Number(n));

    try {
      // 1. drawTicket belgesini oluştur
      const doc = {
        _type: 'drawTicket',
        owner: { _type: 'reference', _ref: userId },
        draw: { _type: 'reference', _ref: drawId },
        numbers,
        createdAt: new Date().toISOString(),
      };
      await client.create(doc);
      console.log('Çekiliş bileti başarıyla kaydedildi.');

      // 2. Çekiliş belgesindeki usedNumbers alanını güncelle
      await client
        .patch(drawId) // draw belgesinin ID'si
        .setIfMissing({ usedNumbers: [] }) // usedNumbers alanı yoksa boş array olarak başlat
        .insert('after', 'usedNumbers[-1]', numbers) // Yeni numaraları existing array'e ekle
        .inc({ ticketsSold: 1 }) // ticketsSold alanını 1 artır
        .commit();

      console.log(`Çekiliş ID: ${drawId} için kullanılan numaralar ve satılan bilet adedi güncellendi.`);

    } catch (e) {
      console.error('Sanity kaydı veya usedNumbers güncellemesi başarısız:', e);
    }
  }

  res.status(200).end();
} 