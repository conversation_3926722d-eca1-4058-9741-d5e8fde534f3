"use client";

import { SignIn<PERSON><PERSON>on, SignedIn, User<PERSON><PERSON>on, useUser } from "@clerk/nextjs";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import Form from "next/form";
import { PackageIcon } from "lucide-react";
import { ClerkLoaded } from "@clerk/nextjs";
import { Button } from "@/components/ui/button";
import { useBasketStore } from "@/app/(store)/store";
import { client } from "@/sanity/lib/client";

function Header() {
  const { user, isLoaded } = useUser();
  const itemCount = useBasketStore((state) =>
    state.items.reduce((acc, item) => acc + item.quantity, 0)
  );

  // Sanity admin kontrolü
  const [isAdmin, setIsAdmin] = useState(false);
  useEffect(() => {
    if (!user) return;
    // Clerk ID ile Sanity'den kullanıcıyı çek
    client.fetch(`*[_type == "user" && clerkId == $clerkId][0]{isAdmin}`, { clerkId: user.id })
      .then((data) => setIsAdmin(!!data?.isAdmin));
  }, [user]);

  // Çekilişler için state
  const [giveaways, setGiveaways] = useState<any[]>([]);
  const [drawLoading, setDrawLoading] = useState(false);
  const [drawResult, setDrawResult] = useState<any>(null);
  const [drawError, setDrawError] = useState<string | null>(null);
  const [showDrawPanel, setShowDrawPanel] = useState(false);

  // Kazanan uyarısı için state
  const [showWinnerAlert, setShowWinnerAlert] = useState(false);
  useEffect(() => {
    if (!user) return;
    // Kullanıcının kazanan olduğu çekiliş var mı kontrol et
    fetch("/api/giveaways/all")
      .then((res) => res.json())
      .then((data) => {
        const giveaways = data.giveaways || [];
        const isWinner = giveaways.some((g: any) =>
          (g.winners || []).some((w: any) =>
            (w.user?._ref === user.id || w.user?._id === user.id || w.user?.clerkId === user.id)
          )
        );
        setShowWinnerAlert(isWinner);
      });
  }, [user]);

  useEffect(() => {
    if (!isAdmin) return;
    // Tüm çekilişleri çek
    fetch("/api/giveaways/all")
      .then((res) => res.json())
      .then((data) => {
        setGiveaways(data.giveaways || []);
      });
  }, [isAdmin]);

  useEffect(() => {
    if (showWinnerAlert) {
      const timer = setTimeout(() => setShowWinnerAlert(false), 300000); // 5 dakika
      return () => clearTimeout(timer);
    }
  }, [showWinnerAlert]);

  const handleDraw = async (giveaway: any) => {
    setDrawLoading(true);
    setDrawError(null);
    setDrawResult(null);
    try {
      if (giveaway.ticketsSold < giveaway.totalTickets) {
        setDrawError("Tüm biletler satılmadan çekiliş yapılamaz.");
        setDrawLoading(false);
        return;
      }
      const winnerCount = giveaway.prizes?.length || 1;
      const res = await fetch("/api/giveaways/drawWinners", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          giveawayId: giveaway._id,
          winnerCount,
        }),
      });
      const data = await res.json();
      if (res.ok) {
        setDrawResult(data);
      } else {
        setDrawError(data.error || "Çekiliş yapılamadı.");
      }
    } catch (e: any) {
      setDrawError("Çekiliş sırasında hata oluştu.");
    } finally {
      setDrawLoading(false);
    }
  };

  const createClerkPasskey = async () => {
    try {
      const response = await user?.createPasskey();
      console.log(response);
    } catch (err) {
      console.error("Error creating passkey:", JSON.stringify(err, null, 2));
    }
  };

  return (
    <header className="flex flex-wrap items-center shadow-2xl bg-gradient-to-br from-blue-400 to-red-600 md:p-8 justify-between p-2 py-8 rounded-lg">
      {/* Top row of header */}
      <div className="flex flex-wrap w-full items-center justify-between px-8">
        <Link
          href="/"
          className="text-2xl font-bold text-color-full-500 hover:text-color-full-500 hover:opacity-50 cursor-pointer mx-auto sm:mx-0"
        >
          Trendfy_X
        </Link>

        <Form
          action="/search"
          className="sm:w-auto sm:flex-1 sm:mx-4 mt-4 sm:mt-0 py-2 px-16 max-w-full"
        >
          <input
            type="text"
            name="query"
            placeholder="Search for products"
            className="bg-gray-100 text-gray-800 rounded-lg focus:outline-none focus:ring-4 focus:ring-purple-500 focus:ring-offset-4 border w-full px-16 max-w-4xl"
          />
        </Form>

        <div className="flex items-center space-x-4 mt-4 sm:mt-0 flex-1 sm:flex-none">
          <Link
            href="/basket"
            className="flex-1 relative flex justify-center sm:justify-start sm:flex-none items-center space-x-2 bg-purple-700 hover:bg-purple-900 text-white font-bold py-2 px-4 rounded-full"
          >
            <PackageIcon className="w-6 h-6" />
            <span className="absolute top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-semibold">
              {itemCount}
            </span>
            <span>My Basket</span>
          </Link>

          <ClerkLoaded>
            {/* Sign in button */}
            <SignedIn>
              <Link
                href="/orders"
                className="flex-1 relative flex justify-center sm:justify-start sm:flex-none items-center space-x-2 bg-purple-700 hover:bg-purple-900 text-white font-bold py-2 px-4 rounded-full"
              >
                <PackageIcon className="w-6 h-6" />
                <span>My Orders</span>
              </Link>
            </SignedIn>

            {isAdmin && (
              <div className="mr-4">
                <button
                  onClick={() => setShowDrawPanel((v) => !v)}
                  className="bg-blue-700 hover:bg-blue-900 text-white font-bold py-2 px-4 rounded"
                >
                  Çekiliş Yap Paneli
                </button>
                {showDrawPanel && (
                  <div className="bg-white border rounded shadow-lg p-4 mt-2 max-w-md">
                    <h3 className="font-bold mb-2">Tüm Çekilişler</h3>
                    <ul className="space-y-2">
                      {giveaways.length === 0 && <li>Çekiliş bulunamadı.</li>}
                      {giveaways.map((g: any) => (
                        <li key={g._id} className="flex items-center justify-between p-2 border rounded">
                          <div>
                            <span className="font-semibold">{g.title}</span>
                            <span className="ml-2 text-xs text-gray-500">({g.ticketsSold}/{g.totalTickets} bilet satıldı)</span>
                            <span className={`ml-2 px-2 py-1 rounded text-xs font-bold ${g.ticketsSold === g.totalTickets ? 'bg-green-100 text-green-700' : 'bg-yellow-100 text-yellow-700'}`}>{g.ticketsSold === g.totalTickets ? 'Satış Tamamlandı' : 'Satış Devam Ediyor'}</span>
                          </div>
                          <button
                            disabled={g.status === 'completed' || g.ticketsSold < g.totalTickets || drawLoading}
                            onClick={() => handleDraw(g)}
                            className={`ml-2 px-3 py-1 rounded ${g.status === 'completed' ? 'bg-gray-400 text-gray-700 cursor-not-allowed' : g.ticketsSold < g.totalTickets ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : 'bg-green-600 text-white hover:bg-green-800'}`}
                          >
                            {g.status === 'completed' ? 'Çekiliş Yapıldı' : (drawLoading ? 'Çekiliş...' : 'Çekilişi Yap')}
                          </button>
                        </li>
                      ))}
                    </ul>
                    {drawError && <div className="text-red-600 mt-2">{drawError}</div>}
                    {drawResult && (
                      <div className="bg-green-100 text-green-800 rounded p-2 mt-2">
                        <div>Kazanan Numaralar: {drawResult.winningNumbers?.join(", ")}</div>
                        <div>Kazananlar: {drawResult.winners?.map((w: any) => (w.userEmail ? w.userEmail.split('@')[0] : (w.userName || 'Kazanan'))).join(", ")}</div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {user?.passkeys.length === 0 && (
              <Button
                onClick={createClerkPasskey}
                className="bg-white hover:bg-blue-700 hover:text-white animate-pulse text-blue-500 font-bold py-2 px-4 rounded border border-blue-300"
              >
                Create Passkey
              </Button>
            )}
          </ClerkLoaded>

          <ClerkLoaded>
            {user ? (
              <div className="flex justify-center items-center space-x-2 ">
                <UserButton />
                <div className="hidden sm:block text-sm text-gray-900">
                  <p className="text-gray-600 text-1xl font-bold">Hoşgeldiniz</p>
                  <p className="text-3xl font-bold">{user.fullName}</p>
                </div>
              </div>
            ) : (
              <SignInButton mode="modal" />
            )}
          </ClerkLoaded>

          <a
            href="/wallet"
            className="text-blue-600 font-semibold hover:underline"
          >
            Cüzdanım
          </a>
        </div>
      </div>

      {showWinnerAlert && (
        <div className="fixed top-4 left-1/2 transform -translate-x-1/2 bg-green-600 text-white px-6 py-3 rounded-lg shadow-lg z-50 font-bold text-lg animate-bounce">
          Tebrikler! Kazandınız!
        </div>
      )}
    </header>
  );
}

export default Header;
