/**
 * Integration test for Clerk user synchronization with Sanity
 */

import { getUserByClerkId } from '@/sanity/lib/users/getUser'

// Mock Sanity dependencies
jest.mock('@/sanity/lib/live', () => ({
  sanityFetch: jest.fn(),
}))

jest.mock('@/sanity/lib/client', () => ({
  client: {
    patch: jest.fn(() => ({
      set: jest.fn(() => ({
        commit: jest.fn(),
      })),
    })),
    create: jest.fn(),
  },
}))

import { sanityFetch } from '@/sanity/lib/live'
import { client } from '@/sanity/lib/client'

const mockSanityFetch = sanityFetch as jest.MockedFunction<typeof sanityFetch>
const mockClient = client as jest.Mocked<typeof client>

describe('Clerk User Synchronization Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('getUserByClerkId', () => {
    it('should create a new user when not found in Sanity', async () => {
      // Arrange
      const clerkUserData = {
        id: 'clerk_test_123',
        email: '<EMAIL>',
        name: 'Test User'
      }

      const expectedNewUser = {
        _id: 'user_123',
        _type: 'user',
        clerkId: 'clerk_test_123',
        name: 'Test User',
        email: '<EMAIL>',
        isAdminApproved: false
      }

      // Mock Sanity fetch to return no existing user
      mockSanityFetch.mockResolvedValue({ data: null })
      
      // Mock client create to return new user
      mockClient.create.mockResolvedValue(expectedNewUser as any)

      // Act
      const result = await getUserByClerkId(clerkUserData)

      // Assert
      expect(mockSanityFetch).toHaveBeenCalledWith({
        query: expect.stringContaining('*[_type == "user" && clerkId == $clerkUserId][0]'),
        params: { clerkUserId: 'clerk_test_123' }
      })

      expect(mockClient.create).toHaveBeenCalledWith({
        _type: 'user',
        clerkId: 'clerk_test_123',
        name: 'Test User',
        email: '<EMAIL>',
        isAdminApproved: false
      })

      expect(result).toEqual(expectedNewUser)
    })

    it('should return existing user when found in Sanity', async () => {
      // Arrange
      const clerkUserData = {
        id: 'clerk_test_123',
        email: '<EMAIL>',
        name: 'Test User'
      }

      const existingUser = {
        _id: 'user_123',
        clerkId: 'clerk_test_123',
        name: 'Test User',
        email: '<EMAIL>',
        isAdminApproved: true
      }

      // Mock Sanity fetch to return existing user
      mockSanityFetch.mockResolvedValue({ data: existingUser })

      // Act
      const result = await getUserByClerkId(clerkUserData)

      // Assert
      expect(mockSanityFetch).toHaveBeenCalledWith({
        query: expect.stringContaining('*[_type == "user" && clerkId == $clerkUserId][0]'),
        params: { clerkUserId: 'clerk_test_123' }
      })

      expect(mockClient.create).not.toHaveBeenCalled()
      expect(result).toEqual(existingUser)
    })

    it('should update user info when email or name has changed', async () => {
      // Arrange
      const clerkUserData = {
        id: 'clerk_test_123',
        email: '<EMAIL>',
        name: 'New Name'
      }

      const existingUser = {
        _id: 'user_123',
        clerkId: 'clerk_test_123',
        name: 'Old Name',
        email: '<EMAIL>',
        isAdminApproved: true
      }

      // Mock Sanity fetch to return existing user with old info
      mockSanityFetch.mockResolvedValue({ data: existingUser })

      const mockPatch = {
        set: jest.fn().mockReturnValue({
          commit: jest.fn().mockResolvedValue(undefined)
        })
      }
      mockClient.patch.mockReturnValue(mockPatch as any)

      // Act
      const result = await getUserByClerkId(clerkUserData)

      // Assert
      expect(mockClient.patch).toHaveBeenCalledWith('user_123')
      expect(mockPatch.set).toHaveBeenCalledWith({
        name: 'New Name',
        email: '<EMAIL>'
      })

      expect(result?.name).toBe('New Name')
      expect(result?.email).toBe('<EMAIL>')
    })

    it('should handle errors gracefully', async () => {
      // Arrange
      const clerkUserData = {
        id: 'clerk_test_123',
        email: '<EMAIL>',
        name: 'Test User'
      }

      // Mock Sanity fetch to throw error
      mockSanityFetch.mockRejectedValue(new Error('Sanity connection failed'))

      // Act
      const result = await getUserByClerkId(clerkUserData)

      // Assert
      expect(result).toBeNull()
    })

    it('should return null for empty clerk user ID', async () => {
      // Arrange
      const clerkUserData = {
        id: '',
        email: '<EMAIL>',
        name: 'Test User'
      }

      // Act
      const result = await getUserByClerkId(clerkUserData)

      // Assert
      expect(result).toBeNull()
      expect(mockSanityFetch).not.toHaveBeenCalled()
    })
  })
})
