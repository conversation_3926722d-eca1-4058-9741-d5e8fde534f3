import { NextRequest, NextResponse } from 'next/server'
import { client } from '@/sanity/lib/client'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ auctionId: string }> }
) {
  try {
    const { auctionId } = await params;

    if (!auctionId) {
      return NextResponse.json({
        success: false,
        error: 'Auction ID is required'
      }, { status: 400 });
    }

    console.log(`🔍 Fetching auction: ${auctionId}`);

    // Fetch auction details by _id or id field
    const auction = await client.fetch(`
      *[_type == "auction" && (_id == $auctionId || id == $auctionId)][0] {
        _id,
        id,
        name,
        description,
        image,
        startingBid,
        currentBid,
        bidIncrementAmount,
        startTime,
        endTime,
        status,
        product-> {
          name,
          price,
          currency
        },
        bidHistory[] {
          bidAmount,
          bidTime,
          bidder-> {
            _id,
            name,
            email
          }
        }
      }
    `, { auctionId });

    if (!auction) {
      return NextResponse.json({
        success: false,
        error: 'Auction not found'
      }, { status: 404 });
    }

    // Get highest bidder from bidHistory
    let highestBidder = null;
    if (auction.bidHistory && auction.bidHistory.length > 0) {
      const sortedBids = auction.bidHistory.sort((a: any, b: any) => b.bidAmount - a.bidAmount);
      highestBidder = sortedBids[0]?.bidder;
    }

    console.log('✅ Auction fetched successfully:', {
      _id: auction._id,
      id: auction.id,
      name: (typeof auction.name === 'string'
        ? auction.name
        : auction.name?.tr || auction.name?.en) ||
        (typeof auction.product?.name === 'string'
          ? auction.product.name
          : auction.product?.name?.tr || auction.product?.name?.en),
      startingBid: auction.startingBid,
      currentBid: auction.currentBid,
      bidsCount: auction.bidHistory?.length || 0
    });

    return NextResponse.json({
      success: true,
      data: {
        ...auction,
        highestBidder
      }
    });

  } catch (error) {
    console.error('❌ Error fetching auction:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
