"use client";

import { useBasketStore } from "@/app/(store)/store";
import { Button } from "@/components/ui/button";
import { SignInButton, useAuth, useUser } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import AddToBasketButton from "@/components/AddToBasketButton";
import Image from "next/image";
import { urlForImage } from "@/sanity/lib/image";
import Loader from "@/components/Loader";
import { createCheckoutSession } from "@/actions/createCheckoutSession";
import { formatCurrency } from "@/lib/formatCurrency";
import { Product } from "@/sanity.types";

function BasketPage() {
    const items = useBasketStore((state) => state.items);
    const {isSignedIn} = useAuth();
    const {user} = useUser();
    const router = useRouter();
    const [isClient, setIsClient] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        setIsClient(true);
    }, []);

    if(items.length === 0) {
        return (
            <div className="flex flex-col items-center justify-center h-screen">
                <h1 className="text-2xl font-bold">Your basket is empty</h1>
                <p className="text-sm text-gray-500 mb-4">
                    Add items to your basket to get started.
                </p>
                <Button onClick={() => router.push("/")} className="bg-blue-500 hover:bg-blue-600">
                    Continue Shopping
                </Button>
            </div>
        )
    }

    const getImageUrl = (image: Product['image']) => {
        if (!image || !image.asset?._ref) return null;
        return urlForImage(image).url();
    };

    const handleCheckout = async () => {
        if (!isSignedIn || !user) return;
        setIsLoading(true);

        try {
            const metadata = {
                orderNumber: crypto.randomUUID(),
                customer: user.id,
                customerEmail: user.emailAddresses[0].emailAddress,
                customerName: user.fullName ?? user.firstName ?? "Guest",
                clerkUserId: user.id,
                customerPhone: "",
            };

            const checkoutUrl = await createCheckoutSession(items, metadata);
            if (checkoutUrl) {
                window.location.href = checkoutUrl;
            }
        } catch (error) {
            console.error(error);
        } finally {
            setIsLoading(false);
        }
    }

    if (!isClient) {
        return <Loader/>
    }

    return (
        <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-gradient-to-br from-purple-50 to-indigo-100">
            <div className="bg-white p-4 sm:p-8 rounded-xl shadow-2xl w-full max-w-4xl border border-gray-200">
                <h1 className="text-3xl font-extrabold text-indigo-800 tracking-tight mb-8 text-center">Shopping Basket</h1>
            
                <div className="flex flex-col lg:flex-row gap-8">
                    <div className="flex-1">
                        <div className="bg-white rounded-lg shadow-xl border border-gray-200">
                            <ul className="divide-y divide-gray-200">
                                {items.map((item) => {
                                    const imageUrl = getImageUrl(item.product.image);
                                    return (
                                        <li key={item.product._id} className="p-5 border-b border-blue-200 bg-blue-50 hover:shadow-md transition-shadow">
                                            <div className="flex items-center">
                                                {imageUrl && (
                                                    <div className="relative h-32 w-32 flex-shrink-0 mr-4">
                                                        <Image
                                                            src={imageUrl}
                                                            alt={item.product.name || "Product image"}
                                                            fill
                                                            className="object-cover rounded-lg"
                                                        />
                                                    </div>
                                                )}
                                                <div className="ml-6 flex-1">
                                                    <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between">
                                                        <h2 className="text-xl font-semibold text-indigo-700">
                                                            {item.product.name}
                                                        </h2>
                                                        <p className="text-xl font-semibold text-green-700 mt-2 sm:mt-0">
                                                            {formatCurrency(item.product.price || 0)}
                                                        </p>
                                                    </div>
                                                    <p className="text-sm text-gray-600 mt-1 mb-4">
                                                        {item.product.description}
                                                    </p>
                                                    <div className="mt-4 flex items-center justify-between">
                                                        <div className="flex items-center">
                                                    <AddToBasketButton product={item.product} disabled={item.product.stock != null && item.product.stock <= 0}/>
                                                        </div>
                                                        <p className="text-sm text-gray-600">
                                                            Quantity: {item.quantity}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                    );
                                })}
                            </ul>
                        </div>
                    </div>

                    <div className="w-full lg:w-96">
                        <div className="bg-white p-6 rounded-xl shadow-2xl sticky top-4 border border-gray-200">
                            <h2 className="text-2xl font-bold text-indigo-800 mb-4">Order Summary</h2>
                            <div className="space-y-4">
                                <div className="flex justify-between">
                                    <p className="text-gray-600">Subtotal</p>
                                    <p className="font-medium text-gray-800">
                                        {formatCurrency(useBasketStore.getState().getTotalPrice())}
                                    </p>
                                </div>
                                <div className="border-t border-gray-300 pt-4">
                                    <div className="flex justify-between">
                                        <p className="text-xl font-bold text-indigo-800">Total</p>
                                        <p className="text-xl font-bold text-green-700">
                                            {formatCurrency(useBasketStore.getState().getTotalPrice())}
                                        </p>
                                    </div>
                                </div>
                                {isSignedIn && user ? (
                                    <Button 
                                        onClick={handleCheckout}
                                        disabled={isLoading}
                                        className="w-full bg-indigo-600 hover:bg-indigo-700 text-white py-3 px-4 rounded-lg disabled:bg-gray-400 font-semibold text-lg"
                                    >
                                        {isLoading ? "Processing..." : "Checkout"}
                                    </Button>
                                ) : (
                                    <SignInButton mode="modal">
                                        <Button className="w-full bg-indigo-600 hover:bg-indigo-700 text-white py-3 px-4 rounded-lg font-semibold text-lg">
                                            Sign in to checkout
                                        </Button>
                                    </SignInButton>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default BasketPage;