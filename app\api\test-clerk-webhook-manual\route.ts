import { NextRequest, NextResponse } from 'next/server'
import { client } from '@/sanity/lib/client'

export async function POST(req: NextRequest) {
  try {
    console.log('🧪 Manual Clerk webhook test')
    
    // Simulate Clerk user.created event
    const mockClerkUser = {
      id: `test_clerk_${Date.now()}`,
      email_addresses: [{ email_address: '<EMAIL>' }],
      first_name: 'Test',
      last_name: 'User',
      image_url: 'https://example.com/test.jpg'
    }
    
    console.log('👤 Mock Clerk user:', mockClerkUser)
    
    // Create user in Sanity directly (simulating webhook)
    const user = {
      _type: 'user',
      _id: mockClerkUser.id,
      clerkId: mockClerkUser.id,
      name: `${mockClerkUser.first_name} ${mockClerkUser.last_name}`.trim(),
      email: mockClerkUser.email_addresses[0].email_address,
      isAdminApproved: false,
      imageUrl: mockClerkUser.image_url,
    }
    
    console.log('💾 Creating user in Sanity:', user)
    
    const result = await client.createIfNotExists(user)
    console.log('✅ User created in Sanity:', result)
    
    return NextResponse.json({
      success: true,
      message: 'Manual webhook test completed',
      mockClerkUser: mockClerkUser,
      sanityUser: result
    })
    
  } catch (error) {
    console.error('❌ Manual webhook test error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function GET() {
  try {
    // Test current webhook endpoint
    const testPayload = {
      type: 'user.created',
      data: {
        id: `test_clerk_${Date.now()}`,
        email_addresses: [{ email_address: '<EMAIL>' }],
        first_name: 'Webhook',
        last_name: 'Test',
        image_url: 'https://example.com/webhook-test.jpg'
      }
    }
    
    console.log('🔗 Testing webhook endpoint with payload:', testPayload)
    
    // Make request to our own webhook endpoint
    const response = await fetch(`${process.env.NEXT_PUBLIC_PUBLIC_BASE_URL}/api/clerk-webhook`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'svix-id': 'test-id',
        'svix-timestamp': Math.floor(Date.now() / 1000).toString(),
        'svix-signature': 'test-signature'
      },
      body: JSON.stringify(testPayload)
    })
    
    const result = await response.text()
    console.log('📡 Webhook response:', { status: response.status, body: result })
    
    return NextResponse.json({
      success: true,
      webhookTest: {
        status: response.status,
        response: result,
        payload: testPayload
      }
    })
    
  } catch (error) {
    console.error('❌ Webhook test error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
