import { headers } from "next/headers";
import { NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";
import { client } from "@/sanity/lib/client";
import crypto from "crypto";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

interface Metadata {
    orderNumber: string;
    customerEmail: string;
    customerName: string;
    clerkUserId: string;
    cart: string; // Assuming cart details are stringified JSON
}

// Helper function to generate order number
function generateOrderNumber(): string {
    // Generate a UUID and take first 8 characters
    return `ORD-${crypto.randomUUID().substring(0, 8).toUpperCase()}`;
}

export async function POST(request: Request) {
    const body = await request.text();
    const headersList = await headers();
    const signature = headersList.get("stripe-signature");

    if (!signature) {
        return NextResponse.json({ error: "No signature" }, { status: 400 });
    }

    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

    if (!webhookSecret) {
        console.error("Stripe webhook secret is not set");
        return NextResponse.json(
            { error: "No webhook secret" },
            { status: 500 }
        );
    }

    let event: Stripe.Event;

    try {
        event = stripe.webhooks.constructEvent(
            body,
            signature,
            webhookSecret
        );
    } catch (error: any) {
        console.error("Stripe webhook error", error);
        return NextResponse.json(
            { error: `Webhook Error: ${error.message}` },
            { status: 400 }
        );
    }

    if (event.type === "checkout.session.completed") {
        const session = event.data.object as Stripe.Checkout.Session;

        try {
            const order = await createOrderInSanity(session);
            console.log("Order created in Sanity", order);
        } catch (error) {
            console.error("Error creating order in Sanity", error);
            return NextResponse.json(
                { error: "Error creating order in Sanity" },
                { status: 500 }
            );
        }
    }

    return NextResponse.json({ received: true }, { status: 200 });
}

async function createOrderInSanity(session: Stripe.Checkout.Session) {
    const {
        id,
        amount_total,
        currency,
        metadata,
    } = session;

    const shipping_details = (session as any).shipping_details;

    if (!metadata) {
        throw new Error("Metadata is missing from the Stripe session.");
    }

    const { customerEmail, customerName, clerkUserId } = metadata;

    const lineItemsWithProductData = await stripe.checkout.sessions.listLineItems(
        id, {
        expand: ["data.price.product"],
    });

    const orderItems = lineItemsWithProductData.data.map((item) => {
        const product = item.price?.product as Stripe.Product;
        return {
            _key: crypto.randomUUID(),
            product: {
                _type: "reference",
                _ref: product.metadata.sanity_id,
            },
            quantity: item.quantity || 0,
            price: (item.price?.unit_amount ?? 0) / 100,
        };
    }).filter(item => item.product._ref);

    if (orderItems.length === 0) {
        throw new Error("No valid products found in Stripe session to create a Sanity order.");
    }

    const orderPayload = {
        _type: 'order',
        orderNumber: metadata.orderNumber,
        customerName,
        customerEmail,
        clerkUserId,
        orderItems,
        totalAmount: (amount_total ?? 0) / 100,
        paymentIntentId: id,
        orderStatus: 'pending',
        paymentStatus: 'paid',
        shippingAddress: shipping_details ? {
            street: shipping_details.address?.line1,
            city: shipping_details.address?.city,
            state: shipping_details.address?.state,
            postalCode: shipping_details.address?.postal_code,
            country: shipping_details.address?.country,
        } : undefined,
        createdAt: new Date().toISOString(),
    };

    // Create a transaction
    const transaction = client.transaction();

    // Add order creation to transaction
    transaction.create(orderPayload);

    // Add stock updates to transaction
    for (const item of orderItems) {
        if (item.product._ref && item.quantity > 0) {
            transaction.patch(item.product._ref, {
                dec: { stock: item.quantity }
            });
        }
    }

    // Commit all changes atomically
    return transaction.commit();
}

    




