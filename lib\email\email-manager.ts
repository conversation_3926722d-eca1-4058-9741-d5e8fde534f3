// Email Template and Sending System
import nodemailer from 'nodemailer';
import { ErrorTracker } from '../sentry/config';

// Email template types
export type EmailTemplateType = 
  | 'welcome'
  | 'auction_won'
  | 'auction_outbid'
  | 'giveaway_won'
  | 'order_confirmation'
  | 'order_shipped'
  | 'payment_confirmation'
  | 'wallet_topup'
  | 'password_reset'
  | 'email_verification'
  | 'account_approved'
  | 'weekly_digest'
  | 'monthly_report';

// Email template interface
export interface EmailTemplate {
  id: string;
  type: EmailTemplateType;
  subject: {
    tr: string;
    en: string;
  };
  htmlContent: {
    tr: string;
    en: string;
  };
  textContent: {
    tr: string;
    en: string;
  };
  variables: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Email sending options
export interface EmailOptions {
  to: string | string[];
  cc?: string | string[];
  bcc?: string | string[];
  replyTo?: string;
  priority?: 'high' | 'normal' | 'low';
  attachments?: Array<{
    filename: string;
    content: Buffer | string;
    contentType?: string;
  }>;
}

// Email sending result
export interface EmailResult {
  success: boolean;
  messageId?: string;
  error?: string;
  recipients: string[];
}

// Email Manager class
export class EmailManager {
  private static instance: EmailManager;
  private transporter: nodemailer.Transporter;
  private templates: Map<string, EmailTemplate> = new Map();

  private constructor() {
    this.initializeTransporter();
    this.loadTemplates();
  }

  static getInstance(): EmailManager {
    if (!EmailManager.instance) {
      EmailManager.instance = new EmailManager();
    }
    return EmailManager.instance;
  }

  // Initialize email transporter
  private initializeTransporter(): void {
    this.transporter = nodemailer.createTransporter({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
      pool: true,
      maxConnections: 5,
      maxMessages: 100,
    });
  }

  // Load email templates
  private async loadTemplates(): Promise<void> {
    // In a real implementation, these would be loaded from database
    const templates: EmailTemplate[] = [
      {
        id: 'welcome',
        type: 'welcome',
        subject: {
          tr: 'Hoş Geldiniz! {{userName}}',
          en: 'Welcome! {{userName}}'
        },
        htmlContent: {
          tr: this.getWelcomeTemplateHTML('tr'),
          en: this.getWelcomeTemplateHTML('en')
        },
        textContent: {
          tr: this.getWelcomeTemplateText('tr'),
          en: this.getWelcomeTemplateText('en')
        },
        variables: ['userName', 'verificationLink'],
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 'auction_won',
        type: 'auction_won',
        subject: {
          tr: 'Tebrikler! Açık Artırmayı Kazandınız - {{productName}}',
          en: 'Congratulations! You Won the Auction - {{productName}}'
        },
        htmlContent: {
          tr: this.getAuctionWonTemplateHTML('tr'),
          en: this.getAuctionWonTemplateHTML('en')
        },
        textContent: {
          tr: this.getAuctionWonTemplateText('tr'),
          en: this.getAuctionWonTemplateText('en')
        },
        variables: ['userName', 'productName', 'winningBid', 'auctionLink', 'orderLink'],
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      // Add more templates...
    ];

    templates.forEach(template => {
      this.templates.set(template.type, template);
    });
  }

  // Send email using template
  async sendTemplateEmail(
    templateType: EmailTemplateType,
    variables: Record<string, string>,
    options: EmailOptions,
    language: 'tr' | 'en' = 'tr'
  ): Promise<EmailResult> {
    try {
      const template = this.templates.get(templateType);
      if (!template || !template.isActive) {
        throw new Error(`Template not found or inactive: ${templateType}`);
      }

      // Replace variables in template
      const subject = this.replaceVariables(template.subject[language], variables);
      const htmlContent = this.replaceVariables(template.htmlContent[language], variables);
      const textContent = this.replaceVariables(template.textContent[language], variables);

      // Prepare email options
      const mailOptions = {
        from: `"${process.env.EMAIL_FROM_NAME}" <${process.env.EMAIL_FROM_ADDRESS}>`,
        to: Array.isArray(options.to) ? options.to.join(', ') : options.to,
        cc: options.cc ? (Array.isArray(options.cc) ? options.cc.join(', ') : options.cc) : undefined,
        bcc: options.bcc ? (Array.isArray(options.bcc) ? options.bcc.join(', ') : options.bcc) : undefined,
        replyTo: options.replyTo,
        subject,
        html: htmlContent,
        text: textContent,
        priority: options.priority || 'normal',
        attachments: options.attachments,
      };

      // Send email
      const result = await this.transporter.sendMail(mailOptions);

      console.log(`📧 Email sent: ${templateType} to ${options.to}`);

      return {
        success: true,
        messageId: result.messageId,
        recipients: Array.isArray(options.to) ? options.to : [options.to],
      };

    } catch (error) {
      console.error(`Failed to send email: ${templateType}`, error);
      
      ErrorTracker.captureException(error as Error, {
        email: {
          templateType,
          recipients: Array.isArray(options.to) ? options.to : [options.to],
          language,
        },
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        recipients: Array.isArray(options.to) ? options.to : [options.to],
      };
    }
  }

  // Send custom email
  async sendCustomEmail(
    subject: string,
    htmlContent: string,
    textContent: string,
    options: EmailOptions
  ): Promise<EmailResult> {
    try {
      const mailOptions = {
        from: `"${process.env.EMAIL_FROM_NAME}" <${process.env.EMAIL_FROM_ADDRESS}>`,
        to: Array.isArray(options.to) ? options.to.join(', ') : options.to,
        cc: options.cc ? (Array.isArray(options.cc) ? options.cc.join(', ') : options.cc) : undefined,
        bcc: options.bcc ? (Array.isArray(options.bcc) ? options.bcc.join(', ') : options.bcc) : undefined,
        replyTo: options.replyTo,
        subject,
        html: htmlContent,
        text: textContent,
        priority: options.priority || 'normal',
        attachments: options.attachments,
      };

      const result = await this.transporter.sendMail(mailOptions);

      console.log(`📧 Custom email sent to ${options.to}`);

      return {
        success: true,
        messageId: result.messageId,
        recipients: Array.isArray(options.to) ? options.to : [options.to],
      };

    } catch (error) {
      console.error('Failed to send custom email', error);
      
      ErrorTracker.captureException(error as Error, {
        email: {
          type: 'custom',
          recipients: Array.isArray(options.to) ? options.to : [options.to],
        },
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        recipients: Array.isArray(options.to) ? options.to : [options.to],
      };
    }
  }

  // Replace variables in template content
  private replaceVariables(content: string, variables: Record<string, string>): string {
    let result = content;
    
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, value);
    });

    return result;
  }

  // Verify email configuration
  async verifyConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      console.log('✅ Email server connection verified');
      return true;
    } catch (error) {
      console.error('❌ Email server connection failed:', error);
      ErrorTracker.captureException(error as Error, { context: 'email_verification' });
      return false;
    }
  }

  // Get template HTML content
  private getWelcomeTemplateHTML(language: 'tr' | 'en'): string {
    if (language === 'tr') {
      return `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Hoş Geldiniz</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #007bff; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .button { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
            .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Hoş Geldiniz!</h1>
            </div>
            <div class="content">
              <h2>Merhaba {{userName}},</h2>
              <p>Platformumuza hoş geldiniz! Hesabınız başarıyla oluşturuldu.</p>
              <p>Hesabınızı aktifleştirmek için aşağıdaki bağlantıya tıklayın:</p>
              <p><a href="{{verificationLink}}" class="button">Hesabı Aktifleştir</a></p>
              <p>Herhangi bir sorunuz varsa bizimle iletişime geçmekten çekinmeyin.</p>
            </div>
            <div class="footer">
              <p>&copy; 2024 Platform Adı. Tüm hakları saklıdır.</p>
            </div>
          </div>
        </body>
        </html>
      `;
    } else {
      return `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Welcome</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #007bff; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .button { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
            .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Welcome!</h1>
            </div>
            <div class="content">
              <h2>Hello {{userName}},</h2>
              <p>Welcome to our platform! Your account has been successfully created.</p>
              <p>Please click the link below to activate your account:</p>
              <p><a href="{{verificationLink}}" class="button">Activate Account</a></p>
              <p>If you have any questions, please don't hesitate to contact us.</p>
            </div>
            <div class="footer">
              <p>&copy; 2024 Platform Name. All rights reserved.</p>
            </div>
          </div>
        </body>
        </html>
      `;
    }
  }

  // Get template text content
  private getWelcomeTemplateText(language: 'tr' | 'en'): string {
    if (language === 'tr') {
      return `
        Hoş Geldiniz!
        
        Merhaba {{userName}},
        
        Platformumuza hoş geldiniz! Hesabınız başarıyla oluşturuldu.
        
        Hesabınızı aktifleştirmek için aşağıdaki bağlantıya tıklayın:
        {{verificationLink}}
        
        Herhangi bir sorunuz varsa bizimle iletişime geçmekten çekinmeyin.
        
        © 2024 Platform Adı. Tüm hakları saklıdır.
      `;
    } else {
      return `
        Welcome!
        
        Hello {{userName}},
        
        Welcome to our platform! Your account has been successfully created.
        
        Please click the link below to activate your account:
        {{verificationLink}}
        
        If you have any questions, please don't hesitate to contact us.
        
        © 2024 Platform Name. All rights reserved.
      `;
    }
  }

  // Get auction won template HTML
  private getAuctionWonTemplateHTML(language: 'tr' | 'en'): string {
    if (language === 'tr') {
      return `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Açık Artırma Kazandınız!</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #28a745; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .button { display: inline-block; padding: 12px 24px; background: #28a745; color: white; text-decoration: none; border-radius: 4px; margin: 10px 5px; }
            .highlight { background: #fff3cd; padding: 15px; border-radius: 4px; margin: 15px 0; }
            .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🏆 Tebrikler!</h1>
              <h2>Açık Artırmayı Kazandınız!</h2>
            </div>
            <div class="content">
              <h2>Merhaba {{userName}},</h2>
              <p>Harika haber! <strong>{{productName}}</strong> için verdiğiniz teklif kazandı!</p>
              
              <div class="highlight">
                <h3>Kazanan Teklif Detayları:</h3>
                <p><strong>Ürün:</strong> {{productName}}</p>
                <p><strong>Kazanan Teklif:</strong> {{winningBid}}</p>
              </div>
              
              <p>Siparişiniz otomatik olarak oluşturulmuştur. Ödeme işlemini tamamlamak için aşağıdaki bağlantıları kullanabilirsiniz:</p>
              
              <p>
                <a href="{{auctionLink}}" class="button">Açık Artırmayı Görüntüle</a>
                <a href="{{orderLink}}" class="button">Siparişi Görüntüle</a>
              </p>
              
              <p>Teşekkür ederiz ve iyi alışverişler dileriz!</p>
            </div>
            <div class="footer">
              <p>&copy; 2024 Platform Adı. Tüm hakları saklıdır.</p>
            </div>
          </div>
        </body>
        </html>
      `;
    } else {
      return `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>You Won the Auction!</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #28a745; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .button { display: inline-block; padding: 12px 24px; background: #28a745; color: white; text-decoration: none; border-radius: 4px; margin: 10px 5px; }
            .highlight { background: #fff3cd; padding: 15px; border-radius: 4px; margin: 15px 0; }
            .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🏆 Congratulations!</h1>
              <h2>You Won the Auction!</h2>
            </div>
            <div class="content">
              <h2>Hello {{userName}},</h2>
              <p>Great news! Your bid for <strong>{{productName}}</strong> has won!</p>
              
              <div class="highlight">
                <h3>Winning Bid Details:</h3>
                <p><strong>Product:</strong> {{productName}}</p>
                <p><strong>Winning Bid:</strong> {{winningBid}}</p>
              </div>
              
              <p>Your order has been automatically created. You can complete the payment process using the links below:</p>
              
              <p>
                <a href="{{auctionLink}}" class="button">View Auction</a>
                <a href="{{orderLink}}" class="button">View Order</a>
              </p>
              
              <p>Thank you and happy shopping!</p>
            </div>
            <div class="footer">
              <p>&copy; 2024 Platform Name. All rights reserved.</p>
            </div>
          </div>
        </body>
        </html>
      `;
    }
  }

  // Get auction won template text
  private getAuctionWonTemplateText(language: 'tr' | 'en'): string {
    if (language === 'tr') {
      return `
        🏆 Tebrikler! Açık Artırmayı Kazandınız!
        
        Merhaba {{userName}},
        
        Harika haber! {{productName}} için verdiğiniz teklif kazandı!
        
        Kazanan Teklif Detayları:
        - Ürün: {{productName}}
        - Kazanan Teklif: {{winningBid}}
        
        Siparişiniz otomatik olarak oluşturulmuştur. Ödeme işlemini tamamlamak için:
        
        Açık Artırmayı Görüntüle: {{auctionLink}}
        Siparişi Görüntüle: {{orderLink}}
        
        Teşekkür ederiz ve iyi alışverişler dileriz!
        
        © 2024 Platform Adı. Tüm hakları saklıdır.
      `;
    } else {
      return `
        🏆 Congratulations! You Won the Auction!
        
        Hello {{userName}},
        
        Great news! Your bid for {{productName}} has won!
        
        Winning Bid Details:
        - Product: {{productName}}
        - Winning Bid: {{winningBid}}
        
        Your order has been automatically created. To complete the payment process:
        
        View Auction: {{auctionLink}}
        View Order: {{orderLink}}
        
        Thank you and happy shopping!
        
        © 2024 Platform Name. All rights reserved.
      `;
    }
  }
}

// Email sending utilities
export const sendEmail = {
  welcome: async (userEmail: string, userName: string, verificationLink: string, language: 'tr' | 'en' = 'tr') => {
    const emailManager = EmailManager.getInstance();
    return await emailManager.sendTemplateEmail(
      'welcome',
      { userName, verificationLink },
      { to: userEmail },
      language
    );
  },

  auctionWon: async (
    userEmail: string, 
    userName: string, 
    productName: string, 
    winningBid: string,
    auctionLink: string,
    orderLink: string,
    language: 'tr' | 'en' = 'tr'
  ) => {
    const emailManager = EmailManager.getInstance();
    return await emailManager.sendTemplateEmail(
      'auction_won',
      { userName, productName, winningBid, auctionLink, orderLink },
      { to: userEmail },
      language
    );
  },

  // Add more email sending utilities...
};

export default EmailManager;
