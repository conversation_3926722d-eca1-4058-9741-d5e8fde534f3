import { NextRequest, NextResponse } from 'next/server';
import { client } from '@/sanity/lib/client';
import { Category } from '@/types/sanity';

const CATEGORY_BY_SLUG_QUERY = `
  *[_type == "category" && slug.current == $slug][0] {
    _id,
    _type,
    _createdAt,
    _updatedAt,
    _rev,
    title,
    slug,
    image {
      asset->{
        _id,
        url
      },
      alt,
      hotspot,
      crop
    },
    description,
    parentCategory->{
      _id,
      title,
      slug
    },
    tags,
    flags,
    seoTitle,
    seoDescription
  }
`;

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params;

    if (!slug) {
      return NextResponse.json(
        { 
          error: { message: 'Category slug is required' },
          success: false 
        },
        { status: 400 }
      );
    }

    const category = await client.fetch<Category | null>(CATEGORY_BY_SLUG_QUERY, { slug });

    if (!category) {
      return NextResponse.json(
        { 
          error: { message: 'Category not found' },
          success: false 
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      data: category,
      success: true,
    });

  } catch (error) {
    console.error('Error fetching category by slug:', error);
    return NextResponse.json(
      { 
        error: { 
          message: 'Failed to fetch category',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        success: false 
      },
      { status: 500 }
    );
  }
}
