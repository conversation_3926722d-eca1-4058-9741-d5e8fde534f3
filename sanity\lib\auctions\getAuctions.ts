// lib/getAuctions.ts
// lib/auctions/getAuctions.ts
import { client } from '../client';
import type { Auction } from '@/sanity/schemaTypes/auctionType';

export async function getActiveAuctions(): Promise<Auction[]> {
  console.log("Fetching active auctions...");
  try {
    const auctions = await client.fetch(`*[_type == "auction" && status == "active"]{ _id, id, name, image, currentBid, endTime, status }`);
    console.log("Fetched active auctions:", auctions);
    return auctions;
  } catch (error) {
    console.error("Error fetching active auctions:", error);
    throw error;
  }
}

export async function getAuctionById(id: string): Promise<Auction | null> {
  console.log(`Fetching auction by ID: ${id}...`);
  try {
    const auction = await client.fetch(
      `*[_type == "auction" && id == $id][0]{ _id, id, name, image, currentBid, endTime, status, bidders[]->{_id, name, clerkId, isAdminApproved}, bidHistory[]{_key, bidAmount, bidTime, bidder->{_id, name, clerkId}}}`,
      { id }
    );
    console.log(`Fetched auction for ID ${id}:`, auction);
    return auction;
  } catch (error) {
    console.error(`Error fetching auction by ID ${id}:`, error);
    throw error;
  }
}