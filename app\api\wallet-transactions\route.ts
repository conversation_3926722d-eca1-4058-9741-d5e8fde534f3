import { NextRequest, NextResponse } from 'next/server';
import { client } from '@/sanity/lib/client';
import { WalletTransaction, WalletTransactionQueryParams } from '@/types/sanity';

// Enhanced GROQ query for wallet transactions
const WALLET_TRANSACTIONS_QUERY = `
  *[_type == "walletTransaction" 
    $userFilter
    $typeFilter
    $statusFilter
    $dateFilter
    $amountFilter
  ] | order($sortBy $sortOrder) [$start...$end] {
    _id,
    _type,
    _createdAt,
    _updatedAt,
    _rev,
    transactionId,
    user->{
      _id,
      name,
      email,
      clerkId
    },
    amount,
    currency,
    type,
    description,
    relatedOrder->{
      _id,
      orderNumber,
      totalAmount
    },
    relatedTopUpRequest->{
      _id,
      requestId,
      amount
    },
    status,
    balanceBefore,
    balanceAfter,
    createdAt,
    processedAt,
    processedBy->{
      _id,
      name,
      email
    },
    notes
  }
`;

const COUNT_QUERY = `
  count(*[_type == "walletTransaction" 
    $userFilter
    $typeFilter
    $statusFilter
    $dateFilter
    $amountFilter
  ])
`;

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Check for special endpoints
    const type = searchParams.get('type');
    
    if (type === 'pending') {
      const pendingTransactions = await client.fetch(`
        *[_type == "walletTransaction" && status == "pending"] | order(createdAt desc) {
          _id, transactionId, user->{_id, name, email}, amount, currency, type, status, createdAt
        }
      `);
      return NextResponse.json({ data: pendingTransactions, success: true });
    }

    // Parse query parameters for regular transaction fetching
    const params: WalletTransactionQueryParams = {
      userId: searchParams.get('userId') || undefined,
      type: searchParams.get('type') as any || undefined,
      status: searchParams.get('status') as any || undefined,
      dateFrom: searchParams.get('dateFrom') || undefined,
      dateTo: searchParams.get('dateTo') || undefined,
      minAmount: searchParams.get('minAmount') ? Number(searchParams.get('minAmount')) : undefined,
      maxAmount: searchParams.get('maxAmount') ? Number(searchParams.get('maxAmount')) : undefined,
      page: Number(searchParams.get('page')) || 1,
      limit: Number(searchParams.get('limit')) || 20,
      sortBy: (searchParams.get('sortBy') as any) || 'createdAt',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
    };

    // Build dynamic filters
    let userFilter = '';
    let typeFilter = '';
    let statusFilter = '';
    let dateFilter = '';
    let amountFilter = '';

    if (params.userId) {
      userFilter = `&& user._ref == "${params.userId}"`;
    }

    if (params.type) {
      typeFilter = `&& type == "${params.type}"`;
    }

    if (params.status) {
      statusFilter = `&& status == "${params.status}"`;
    }

    if (params.dateFrom || params.dateTo) {
      const dateConditions = [];
      if (params.dateFrom) dateConditions.push(`createdAt >= "${params.dateFrom}"`);
      if (params.dateTo) dateConditions.push(`createdAt <= "${params.dateTo}"`);
      dateFilter = `&& (${dateConditions.join(' && ')})`;
    }

    if (params.minAmount !== undefined || params.maxAmount !== undefined) {
      const amountConditions = [];
      if (params.minAmount !== undefined) amountConditions.push(`amount >= ${params.minAmount}`);
      if (params.maxAmount !== undefined) amountConditions.push(`amount <= ${params.maxAmount}`);
      amountFilter = `&& (${amountConditions.join(' && ')})`;
    }

    // Calculate pagination
    const start = (params.page - 1) * params.limit;
    const end = start + params.limit;

    // Build final queries
    const finalTransactionsQuery = WALLET_TRANSACTIONS_QUERY
      .replace('$userFilter', userFilter)
      .replace('$typeFilter', typeFilter)
      .replace('$statusFilter', statusFilter)
      .replace('$dateFilter', dateFilter)
      .replace('$amountFilter', amountFilter)
      .replace('$sortBy', params.sortBy || 'createdAt')
      .replace('$sortOrder', params.sortOrder || 'desc')
      .replace('$start', start.toString())
      .replace('$end', end.toString());

    const finalCountQuery = COUNT_QUERY
      .replace('$userFilter', userFilter)
      .replace('$typeFilter', typeFilter)
      .replace('$statusFilter', statusFilter)
      .replace('$dateFilter', dateFilter)
      .replace('$amountFilter', amountFilter);

    // Execute queries
    const [transactions, total] = await Promise.all([
      client.fetch<WalletTransaction[]>(finalTransactionsQuery),
      client.fetch<number>(finalCountQuery)
    ]);

    // Return response
    return NextResponse.json({
      data: transactions,
      pagination: {
        page: params.page,
        limit: params.limit,
        total,
        totalPages: Math.ceil(total / params.limit),
        hasNext: end < total,
        hasPrev: params.page > 1,
      },
      success: true,
    });

  } catch (error) {
    console.error('Error fetching wallet transactions:', error);
    return NextResponse.json(
      { 
        error: { 
          message: 'Failed to fetch wallet transactions',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        success: false 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const transactionData = await request.json();
    
    // Get user's current wallet balance
    const user = await client.fetch(
      `*[_type == "user" && _id == $userId][0]{walletBalance}`,
      { userId: transactionData.user._ref }
    );

    if (!user) {
      return NextResponse.json(
        { 
          error: { message: 'Kullanıcı bulunamadı' },
          success: false 
        },
        { status: 404 }
      );
    }

    const balanceBefore = user.walletBalance;
    let balanceAfter = balanceBefore;

    // Calculate balance after transaction
    if (['deposit', 'refund', 'bonus'].includes(transactionData.type)) {
      balanceAfter = balanceBefore + transactionData.amount;
    } else if (['withdrawal', 'penalty', 'commission'].includes(transactionData.type)) {
      balanceAfter = balanceBefore - transactionData.amount;
      
      // Check if user has sufficient balance for withdrawal
      if (balanceAfter < 0) {
        return NextResponse.json(
          { 
            error: { message: 'Yetersiz bakiye' },
            success: false 
          },
          { status: 400 }
        );
      }
    }

    // Create transaction
    const transaction = await client.create({
      _type: 'walletTransaction',
      ...transactionData,
      balanceBefore,
      balanceAfter,
      createdAt: new Date().toISOString(),
      status: 'pending', // Start as pending
    });

    // If transaction is automatically approved, update user's wallet balance
    if (transactionData.status === 'completed') {
      await client
        .patch(transactionData.user._ref)
        .set({ walletBalance: balanceAfter })
        .commit();

      // Update transaction status and processed date
      await client
        .patch(transaction._id)
        .set({ 
          status: 'completed',
          processedAt: new Date().toISOString()
        })
        .commit();
    }

    return NextResponse.json({
      data: transaction,
      success: true,
    });

  } catch (error) {
    console.error('Error creating wallet transaction:', error);
    return NextResponse.json(
      { 
        error: { 
          message: 'Failed to create wallet transaction',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        success: false 
      },
      { status: 500 }
    );
  }
}
