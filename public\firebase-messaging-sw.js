// Firebase Messaging Service Worker
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js');

// Firebase configuration
const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "your-auth-domain",
  projectId: "your-project-id",
  storageBucket: "your-storage-bucket",
  messagingSenderId: "your-messaging-sender-id",
  appId: "your-app-id"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);

// Retrieve Firebase Messaging object
const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage(function(payload) {
  console.log('[firebase-messaging-sw.js] Received background message ', payload);
  
  // Customize notification here
  const notificationTitle = payload.notification.title || 'Yeni Bildirim';
  const notificationOptions = {
    body: payload.notification.body || 'Yeni bir bildiriminiz var',
    icon: '/icon-192x192.png',
    badge: '/badge-72x72.png',
    image: payload.notification.image,
    data: payload.data,
    actions: [
      {
        action: 'open',
        title: 'Aç',
        icon: '/icon-open.png'
      },
      {
        action: 'close',
        title: 'Kapat',
        icon: '/icon-close.png'
      }
    ],
    requireInteraction: true,
    silent: false,
    vibrate: [200, 100, 200],
    tag: payload.data?.type || 'general'
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});

// Handle notification click
self.addEventListener('notificationclick', function(event) {
  console.log('[firebase-messaging-sw.js] Notification click received.');

  event.notification.close();

  if (event.action === 'close') {
    return;
  }

  // Handle different notification types
  const notificationData = event.notification.data;
  let targetUrl = '/';

  if (notificationData) {
    switch (notificationData.type) {
      case 'auction_bid':
      case 'auction_won':
      case 'auction_outbid':
        targetUrl = `/auctions/${notificationData.relatedId}`;
        break;
      case 'giveaway_won':
        targetUrl = `/giveaways/${notificationData.relatedId}`;
        break;
      case 'order_status':
        targetUrl = `/orders/${notificationData.relatedId}`;
        break;
      case 'wallet_update':
        targetUrl = '/wallet';
        break;
      default:
        targetUrl = '/notifications';
    }
  }

  // Open the app and navigate to the appropriate page
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true }).then(function(clientList) {
      // Check if there's already a window/tab open with the target URL
      for (let i = 0; i < clientList.length; i++) {
        const client = clientList[i];
        if (client.url.includes(targetUrl) && 'focus' in client) {
          return client.focus();
        }
      }

      // If no window/tab is already open, open a new one
      if (clients.openWindow) {
        return clients.openWindow(targetUrl);
      }
    })
  );
});

// Handle notification close
self.addEventListener('notificationclose', function(event) {
  console.log('[firebase-messaging-sw.js] Notification closed.');
  
  // Track notification close event
  const notificationData = event.notification.data;
  if (notificationData && notificationData.trackingId) {
    // Send analytics event for notification close
    fetch('/api/analytics/notification-close', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        trackingId: notificationData.trackingId,
        action: 'close',
        timestamp: new Date().toISOString(),
      }),
    }).catch(error => {
      console.error('Failed to track notification close:', error);
    });
  }
});

// Handle push subscription change
self.addEventListener('pushsubscriptionchange', function(event) {
  console.log('[firebase-messaging-sw.js] Push subscription changed.');
  
  event.waitUntil(
    // Re-subscribe and update the server
    self.registration.pushManager.subscribe({
      userVisibleOnly: true,
      applicationServerKey: 'your-vapid-key'
    }).then(function(newSubscription) {
      // Send the new subscription to your server
      return fetch('/api/push/update-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newSubscription),
      });
    })
  );
});

// Custom message handling for different notification types
function handleNotificationByType(payload) {
  const { data, notification } = payload;
  
  switch (data?.type) {
    case 'auction_bid':
      return {
        title: '🔨 ' + notification.title,
        options: {
          ...getBaseNotificationOptions(notification),
          badge: '/auction-badge.png',
          vibrate: [100, 50, 100],
          actions: [
            { action: 'view_auction', title: 'Açık Artırmayı Gör' },
            { action: 'place_bid', title: 'Teklif Ver' }
          ]
        }
      };
      
    case 'auction_won':
      return {
        title: '🏆 ' + notification.title,
        options: {
          ...getBaseNotificationOptions(notification),
          badge: '/winner-badge.png',
          vibrate: [200, 100, 200, 100, 200],
          actions: [
            { action: 'view_order', title: 'Siparişi Gör' },
            { action: 'share_win', title: 'Paylaş' }
          ]
        }
      };
      
    case 'giveaway_won':
      return {
        title: '🎁 ' + notification.title,
        options: {
          ...getBaseNotificationOptions(notification),
          badge: '/gift-badge.png',
          vibrate: [300, 100, 300],
          actions: [
            { action: 'claim_prize', title: 'Ödülü Al' },
            { action: 'share_win', title: 'Paylaş' }
          ]
        }
      };
      
    default:
      return {
        title: notification.title,
        options: getBaseNotificationOptions(notification)
      };
  }
}

function getBaseNotificationOptions(notification) {
  return {
    body: notification.body,
    icon: '/icon-192x192.png',
    badge: '/badge-72x72.png',
    image: notification.image,
    requireInteraction: true,
    silent: false,
  };
}
