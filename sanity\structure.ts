import type {StructureResolver} from 'sanity/structure'

// https://www.sanity.io/docs/structure-builder-cheat-sheet
export const structure: StructureResolver = (S) =>
  S.list()
    .title('shoper ecommerce builder')
    .items([
      S.documentTypeListItem('category').title('Categories'),
      S.documentTypeListItem('product').title('Products'),
      S.documentTypeListItem('order').title('Orders'),
      S.documentTypeListItem('sales').title('Sales'),
      S.documentTypeListItem('user').title('Users'),
      S.documentTypeListItem('auction').title("Auctions"),
  

      S.divider(),
      ...S.documentTypeListItems().filter(
        (item) => item.getId() && !['category', 'product', 'order', 'sales', 'user', 'auction'].includes(item.getId()!),
      ),
    ])
