import { client } from '../client';

export async function findWinnerByNumber(giveawayId: string, winningNumber: string) {
  const giveaway = await client.fetch(`*[_type == "giveaway" && _id == $giveawayId][0]`, { giveawayId });
  if (!giveaway || !giveaway.participants) return null;
  for (const participant of giveaway.participants) {
    if (participant.tickets) {
      for (const ticket of participant.tickets) {
        if (ticket.ticketNumber === winningNumber) {
          return {
            user: participant.user,
            ticket,
          };
        }
      }
    }
  }
  return null;
} 