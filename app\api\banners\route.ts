import { NextResponse } from 'next/server';

export async function GET() {
  const banners = [
    {
      _id: '1',
      title: '<PERSON>z <PERSON>ndirimleri Başladı!',
      description: 'Sezonun en trend ürünlerinde %50\'ye varan indirimleri kaçırmayın.',
      type: 'product',
      image: 'https://via.placeholder.com/800x600/FF5733/FFFFFF?text=Yaz+İndirimi',
      buttonText: 'Hemen Alışveriş Yap',
      buttonLink: '/products',
      backgroundColor: '#FF5733',
      textColor: '#FFFFFF',
      isActive: true,
      order: 1,
    },
    {
      _id: '2',
      title: 'Büyük Çekiliş: iPhone 15 Kazanma Şansı!',
      description: '<PERSON>ekilişe katıl, yeni iPhone 15 senin olsun!',
      type: 'giveaway',
      image: 'https://via.placeholder.com/800x600/33C7FF/FFFFFF?text=iPhone+Çekilişi',
      buttonText: 'Çekilişe Katıl',
      buttonLink: '/giveaway',
      backgroundColor: '#33C7FF',
      textColor: '#FFFFFF',
      isActive: true,
      order: 2,
    },
    {
      _id: '3',
      title: 'Eski Telefonunu Takas Et, Yenisini Al!',
      description: 'Değerli eşyalarını getir, yeni ürünlerle takas et.',
      type: 'exchange',
      image: 'https://via.placeholder.com/800x600/8A2BE2/FFFFFF?text=Takas+Kampanyası',
      buttonText: 'Takas Tekliflerini Gör',
      buttonLink: '/exchange',
      backgroundColor: '#8A2BE2',
      textColor: '#FFFFFF',
      isActive: true,
      order: 3,
    },
    {
      _id: '4',
      title: 'Sanat Eseri Açık Artırması!',
      description: 'Nadide sanat eserleri için teklifini ver, koleksiyonuna ekle.',
      type: 'auction',
      image: 'https://via.placeholder.com/800x600/E2A733/FFFFFF?text=Açık+Artırma',
      buttonText: 'Açık Artırmaya Katıl',
      buttonLink: '/auction',
      backgroundColor: '#E2A733',
      textColor: '#FFFFFF',
      isActive: true,
      order: 4,
    },
  ];

  return NextResponse.json({ banners });
} 