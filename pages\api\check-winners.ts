import { NextApiRequest, NextApiResponse } from 'next'
import { createClient } from '@sanity/client'

const client = createClient({
  projectId: process.env.SANITY_PROJECT_ID!,
  dataset: process.env.SANITY_DATASET!,
  apiVersion: '2023-06-10',
  token: process.env.SANITY_WRITE_TOKEN!,
  useCdn: false,
})

interface DrawTicket {
  numbers: number[];
  // Add other properties if needed
}

async function checkWinners(drawId: string) {
  // 1. Çekiliş sonucunu ve ilgili çek<PERSON> (draw) belgesini getir
  const drawResult = await client.fetch(
    `*[_type == "drawResult" && references($drawId)][0]{..., draw->{winningConditionTypes}}`,
    { drawId }
  )
  if (!drawResult || !drawResult.winningNumber || !drawResult.draw || !drawResult.draw.winningConditionTypes) return []

  const winningNumberStr = drawResult.winningNumber.toString()
  const winningConditions = drawResult.draw.winningConditionTypes as string[]

  // 2. Tüm çekiliş biletlerini (ilgili çekilişe ait olanları) al
  const tickets = await client.fetch(
    `*[_type == "drawTicket" && references($drawId)]`,
    { drawId }
  )

  // 3. Kazananları belirle
  const winners = tickets.filter((ticket: DrawTicket) => {
    return ticket.numbers.some((num: number) => {
      const numStr = num.toString()
      let isWinner = false;

      if (winningConditions.includes('lastDigit')) {
        if (numStr.endsWith(winningNumberStr.slice(-1))) {
          isWinner = true;
        }
      }
      if (winningConditions.includes('lastThreeDigits')) {
        if (numStr.length >= 3 && winningNumberStr.length >= 3 && numStr.endsWith(winningNumberStr.slice(-3))) {
          isWinner = true;
        }
      }
      if (winningConditions.includes('lastFourDigits')) {
        if (numStr.length >= 4 && winningNumberStr.length >= 4 && numStr.endsWith(winningNumberStr.slice(-4))) {
          isWinner = true;
        }
      }
      if (winningConditions.includes('lastFiveDigits')) {
        if (numStr.length >= 5 && winningNumberStr.length >= 5 && numStr.endsWith(winningNumberStr.slice(-5))) {
          isWinner = true;
        }
      }
      return isWinner
    })
  })

  return winners
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET'])
    return res.status(405).json({ error: 'Sadece GET metodu desteklenir' })
  }

  const { drawId } = req.query // drawDate yerine drawId

  if (!drawId || typeof drawId !== 'string') {
    return res.status(400).json({ error: 'Geçersiz veya eksik drawId parametresi.' })
  }

  try {
    const winners = await checkWinners(drawId);
    return res.status(200).json({ winners });
  } catch (error) {
    console.error('Kazananları belirlerken hata oluştu:', error);
    return res.status(500).json({ error: 'Sunucu hatası' });
  }
} 