"use client";
import React, { useState } from "react";

interface WalletTopUpFormProps {
  userId: string;
}



export default function WalletTopUpForm({ userId }: WalletTopUpFormProps) {
  const [amount, setAmount] = useState(100);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);
    console.log('userId:', userId, 'amount:', amount);
    try {
      const res = await fetch("/api/wallet/topup", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ userId, amount }),
      });
      const data = await res.json();
      if (data.success) {
        setSuccess(true);
        setAmount(100);
      } else {
        setError(data.error || "Talep oluşturulamadı.");
      }
    } catch (err) {
      setError("Bir hata oluştu.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="bg-blue-50 rounded-lg p-4 mb-6">
      <h3 className="font-semibold mb-2">Cüzdanıma Para Yükle</h3>
      {success && <div className="text-green-700 mb-2">Talebiniz alındı, admin onayından sonra bakiyenize yansıyacaktır.</div>}
      {error && <div className="text-red-700 mb-2">{error}</div>}
      <div className="flex items-center gap-2 mb-2">
        <input
          type="number"
          min={1}
          value={amount}
          onChange={e => setAmount(Number(e.target.value))}
          className="border p-2 rounded w-32"
          required
        />
        <span>TL</span>
      </div>
      <button
        type="submit"
        disabled={loading}
        className="bg-gradient-to-r from-green-500 to-blue-600 text-white font-bold py-2 px-6 rounded-lg disabled:opacity-60"
      >
        {loading ? "Gönderiliyor..." : "Yükleme Talebi Oluştur"}
      </button>
    </form>
  );
} 