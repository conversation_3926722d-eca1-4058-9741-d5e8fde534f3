// Firebase Cloud Messaging Configuration and Utilities
import { initializeApp, getApps } from 'firebase/app';
import { getMessaging, getToken, onMessage, MessagePayload } from 'firebase/messaging';
import { getFirestore, doc, setDoc, updateDoc, arrayUnion } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

// Initialize Firebase
const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];
const db = getFirestore(app);

// FCM Token Management
export class FCMTokenManager {
  private static instance: FCMTokenManager;
  private messaging: any = null;

  private constructor() {
    if (typeof window !== 'undefined') {
      this.messaging = getMessaging(app);
    }
  }

  static getInstance(): FCMTokenManager {
    if (!FCMTokenManager.instance) {
      FCMTokenManager.instance = new FCMTokenManager();
    }
    return FCMTokenManager.instance;
  }

  // Get FCM token for current device
  async getToken(): Promise<string | null> {
    if (!this.messaging) return null;

    try {
      const token = await getToken(this.messaging, {
        vapidKey: process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY,
      });
      
      if (token) {
        console.log('FCM Token:', token);
        return token;
      } else {
        console.log('No registration token available.');
        return null;
      }
    } catch (error) {
      console.error('An error occurred while retrieving token:', error);
      return null;
    }
  }

  // Save token to user document
  async saveTokenToUser(userId: string, token: string): Promise<void> {
    try {
      const userRef = doc(db, 'users', userId);
      await updateDoc(userRef, {
        fcmTokens: arrayUnion(token),
        lastTokenUpdate: new Date().toISOString(),
      });
      console.log('Token saved to user document');
    } catch (error) {
      console.error('Error saving token to user:', error);
    }
  }

  // Setup foreground message listener
  setupForegroundListener(callback: (payload: MessagePayload) => void): void {
    if (!this.messaging) return;

    onMessage(this.messaging, (payload) => {
      console.log('Message received in foreground:', payload);
      callback(payload);
    });
  }

  // Request notification permission
  async requestPermission(): Promise<boolean> {
    if (!('Notification' in window)) {
      console.log('This browser does not support notifications');
      return false;
    }

    const permission = await Notification.requestPermission();
    
    if (permission === 'granted') {
      console.log('Notification permission granted');
      return true;
    } else {
      console.log('Notification permission denied');
      return false;
    }
  }

  // Initialize FCM for user
  async initializeForUser(userId: string): Promise<string | null> {
    const hasPermission = await this.requestPermission();
    if (!hasPermission) return null;

    const token = await this.getToken();
    if (token) {
      await this.saveTokenToUser(userId, token);
      return token;
    }

    return null;
  }
}

// Notification Types
export interface NotificationData {
  type: 'auction_bid' | 'auction_won' | 'auction_outbid' | 'giveaway_won' | 'order_status' | 'wallet_update' | 'general';
  title: string;
  body: string;
  userId?: string;
  relatedId?: string;
  actionUrl?: string;
  imageUrl?: string;
  priority?: 'high' | 'normal' | 'low';
  data?: Record<string, any>;
}

// Server-side notification sender (for API routes)
export class FCMNotificationSender {
  private static instance: FCMNotificationSender;
  private serverKey: string;

  private constructor() {
    this.serverKey = process.env.FIREBASE_SERVER_KEY || '';
  }

  static getInstance(): FCMNotificationSender {
    if (!FCMNotificationSender.instance) {
      FCMNotificationSender.instance = new FCMNotificationSender();
    }
    return FCMNotificationSender.instance;
  }

  // Send notification to specific token
  async sendToToken(token: string, notification: NotificationData): Promise<boolean> {
    try {
      const response = await fetch('https://fcm.googleapis.com/fcm/send', {
        method: 'POST',
        headers: {
          'Authorization': `key=${this.serverKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          to: token,
          notification: {
            title: notification.title,
            body: notification.body,
            image: notification.imageUrl,
            click_action: notification.actionUrl,
          },
          data: {
            type: notification.type,
            relatedId: notification.relatedId || '',
            ...notification.data,
          },
          priority: notification.priority || 'high',
          time_to_live: 86400, // 24 hours
        }),
      });

      const result = await response.json();
      
      if (result.success === 1) {
        console.log('Notification sent successfully');
        return true;
      } else {
        console.error('Failed to send notification:', result);
        return false;
      }
    } catch (error) {
      console.error('Error sending notification:', error);
      return false;
    }
  }

  // Send notification to multiple tokens
  async sendToMultipleTokens(tokens: string[], notification: NotificationData): Promise<{ success: number; failure: number }> {
    const results = await Promise.allSettled(
      tokens.map(token => this.sendToToken(token, notification))
    );

    const success = results.filter(result => result.status === 'fulfilled' && result.value).length;
    const failure = results.length - success;

    return { success, failure };
  }

  // Send notification to user (fetch tokens from Firestore)
  async sendToUser(userId: string, notification: NotificationData): Promise<boolean> {
    try {
      // In a real implementation, you'd fetch user tokens from your database
      // For now, we'll use a placeholder
      const userTokens = await this.getUserTokens(userId);
      
      if (userTokens.length === 0) {
        console.log('No tokens found for user:', userId);
        return false;
      }

      const results = await this.sendToMultipleTokens(userTokens, notification);
      console.log(`Notification sent to user ${userId}: ${results.success} success, ${results.failure} failures`);
      
      return results.success > 0;
    } catch (error) {
      console.error('Error sending notification to user:', error);
      return false;
    }
  }

  // Get user tokens from database (placeholder - implement based on your database)
  private async getUserTokens(userId: string): Promise<string[]> {
    try {
      // This should fetch from your actual user database
      // For now, returning empty array
      return [];
    } catch (error) {
      console.error('Error fetching user tokens:', error);
      return [];
    }
  }
}

// Notification Templates
export const NotificationTemplates = {
  auctionBid: (productName: string, bidAmount: string): NotificationData => ({
    type: 'auction_bid',
    title: 'Yeni Teklif!',
    body: `${productName} için ${bidAmount} teklif verildi`,
    priority: 'high',
  }),

  auctionWon: (productName: string): NotificationData => ({
    type: 'auction_won',
    title: 'Tebrikler! Açık Artırmayı Kazandınız! 🏆',
    body: `${productName} için verdiğiniz teklif kazandı!`,
    priority: 'high',
  }),

  auctionOutbid: (productName: string, newBidAmount: string): NotificationData => ({
    type: 'auction_outbid',
    title: 'Teklifiniz Aşıldı! ⚠️',
    body: `${productName} için teklifiniz aşıldı. Yeni teklif: ${newBidAmount}`,
    priority: 'high',
  }),

  giveawayWon: (giveawayName: string, prizeName: string): NotificationData => ({
    type: 'giveaway_won',
    title: 'Çekilişi Kazandınız! 🎁',
    body: `${giveawayName} çekilişinde ${prizeName} kazandınız!`,
    priority: 'high',
  }),

  orderStatusUpdate: (orderNumber: string, status: string): NotificationData => ({
    type: 'order_status',
    title: 'Sipariş Durumu Güncellendi',
    body: `${orderNumber} numaralı siparişiniz ${status} durumuna geçti`,
    priority: 'normal',
  }),

  walletUpdate: (amount: string, type: 'deposit' | 'withdrawal'): NotificationData => ({
    type: 'wallet_update',
    title: 'Cüzdan Güncellendi 💰',
    body: type === 'deposit' 
      ? `Cüzdanınıza ${amount} yüklendi`
      : `Cüzdanınızdan ${amount} düşüldü`,
    priority: 'normal',
  }),
};

// React Hook for FCM
export const useFCM = (userId?: string) => {
  const [token, setToken] = React.useState<string | null>(null);
  const [isSupported, setIsSupported] = React.useState(false);

  React.useEffect(() => {
    const checkSupport = () => {
      setIsSupported('serviceWorker' in navigator && 'Notification' in window);
    };

    checkSupport();
  }, []);

  React.useEffect(() => {
    if (!isSupported || !userId) return;

    const initializeFCM = async () => {
      const fcmManager = FCMTokenManager.getInstance();
      const fcmToken = await fcmManager.initializeForUser(userId);
      setToken(fcmToken);

      // Setup foreground message listener
      fcmManager.setupForegroundListener((payload) => {
        // Handle foreground notifications
        if (payload.notification) {
          new Notification(payload.notification.title || '', {
            body: payload.notification.body,
            icon: '/icon-192x192.png',
            badge: '/badge-72x72.png',
          });
        }
      });
    };

    initializeFCM();
  }, [isSupported, userId]);

  return { token, isSupported };
};

export default FCMTokenManager;
