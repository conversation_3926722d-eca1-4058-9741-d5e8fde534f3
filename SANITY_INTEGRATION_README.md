# Sanity CMS TypeScript Entegrasyonu

Bu proje, Sanity CMS ile gelişmiş TypeScript entegrasyonu, çoklu dil desteği ve React Query kullanımını içerir.

## 🚀 Özellikler

### ✅ Tamamlanan Geliştirmeler

- **Gelişmiş Sanity Şemaları**: Çoklu dil desteği, varyantlar, SEO, flags
- **TypeScript Entegrasyonu**: Tam tip güvenliği ve IntelliSense desteği
- **React Query Hooks**: Optimized data fetching ve caching
- **API Endpoints**: RESTful API yapısı
- **Çoklu Dil Desteği**: Türkçe/İngilizce içerik yönetimi

## 📁 Dosya Yapısı

```
├── sanity/schemaTypes/
│   ├── productType.ts          # Gelişmiş ürün şeması
│   └── categoryType.ts         # Gelişmiş kategori şeması
├── types/
│   └── sanity.ts              # TypeScript interface'leri
├── hooks/
│   ├── useProducts.ts         # Ürün React Query hooks
│   └── useCategories.ts       # Kategori React Query hooks
├── lib/
│   └── api.ts                 # API client
├── app/api/
│   ├── products/              # Ürün API endpoints
│   └── categories/            # Kategori API endpoints
└── examples/
    └── product-usage.tsx      # Kullanım örnekleri
```

## 🛠️ Kurulum

### 1. Gerekli Paketler

```bash
npm install @tanstack/react-query nanoid
```

### 2. Sanity Types Generate

```bash
npx sanity typegen generate
```

## 📋 Şema Özellikleri

### Product Schema

- **Çoklu Dil**: `name` ve `description` için TR/EN desteği
- **Varyantlar**: Renk, beden vb. seçenekler
- **SEO**: Meta title ve description
- **Flags**: featured, new, sale, limited, auction, giveaway, banner, exchange
- **Fiyatlandırma**: Ana fiyat, indirim, para birimi
- **Envanter**: SKU, stok, availability

### Category Schema

- **Çoklu Dil**: `title` ve `description` için TR/EN desteği
- **Hiyerarşik Yapı**: Parent-child kategori ilişkileri
- **SEO**: Meta title ve description
- **Flags**: featured, seasonal, promotion, new
- **Görsel**: Kategori görseli ve alt text

## 🔧 API Kullanımı

### Products API

```typescript
// Tüm ürünleri getir
GET /api/products

// Filtreleme ve sayfalama
GET /api/products?category=electronics&flags=featured&page=1&limit=20

// ID ile ürün getir
GET /api/products/[id]

// Slug ile ürün getir
GET /api/products/slug/[slug]

// Kategoriye göre ürünler
GET /api/products/category/[categorySlug]

// Öne çıkan ürünler
GET /api/products/featured?limit=10
```

### Categories API

```typescript
// Tüm kategoriler
GET /api/categories

// Root kategoriler
GET /api/categories?type=root

// Öne çıkan kategoriler
GET /api/categories?type=featured

// ID ile kategori
GET /api/categories/[id]

// Slug ile kategori
GET /api/categories/slug/[slug]
```

## 🎣 React Query Hooks

### useProducts Hook

```typescript
import { useProducts } from './hooks/useProducts';

const ProductList = () => {
  const { data: products, isLoading, error } = useProducts({
    category: 'electronics',
    flags: ['featured'],
    minPrice: 50,
    maxPrice: 500,
    inStock: true,
    search: 'laptop',
    page: 1,
    limit: 20,
    sortBy: 'name',
    sortOrder: 'asc',
    language: 'tr'
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      {products?.map(product => (
        <div key={product._id}>
          <h3>{product.name.tr}</h3>
          <p>{product.price} {product.currency}</p>
        </div>
      ))}
    </div>
  );
};
```

### useCategories Hook

```typescript
import { useCategories } from './hooks/useCategories';

const CategoryNav = () => {
  const { data: categories } = useCategories({
    parentCategory: 'electronics',
    flags: ['featured'],
    language: 'tr'
  });

  return (
    <nav>
      {categories?.map(category => (
        <a key={category._id} href={`/categories/${category.slug.current}`}>
          {category.title.tr}
        </a>
      ))}
    </nav>
  );
};
```

## 🌍 Çoklu Dil Desteği

### Localized Text Helper

```typescript
import { getLocalizedText } from './hooks/useProducts';

const ProductCard = ({ product, language = 'tr' }) => {
  const productName = getLocalizedText(product.name, language);
  const productDescription = getLocalizedText(product.description, language);

  return (
    <div>
      <h3>{productName}</h3>
      <p>{productDescription}</p>
    </div>
  );
};
```

## 💰 Fiyat Yardımcıları

```typescript
import { 
  formatPrice, 
  calculateDiscountedPrice,
  isProductInStock,
  getTotalStock 
} from './hooks/useProducts';

const ProductPrice = ({ product }) => {
  const originalPrice = product.price;
  const discountedPrice = calculateDiscountedPrice(originalPrice, product.discount);
  const formattedPrice = formatPrice(discountedPrice, product.currency);
  const inStock = isProductInStock(product);
  const totalStock = getTotalStock(product);

  return (
    <div>
      <span>{formattedPrice}</span>
      {product.discount && (
        <span className="original-price">
          {formatPrice(originalPrice, product.currency)}
        </span>
      )}
      <span>Stok: {totalStock}</span>
    </div>
  );
};
```

## 🏗️ TypeScript Types

### Ana Interface'ler

```typescript
interface Product {
  _id: string;
  _type: 'product';
  id: string;
  name: MultilingualText;
  slug: SanitySlug;
  image: SanityImage;
  description: MultilingualTextArea;
  category: SanityReference;
  tags?: string[];
  price: number;
  currency: Currency;
  discount?: number;
  sku: string;
  stock: number;
  isAvailable: boolean;
  variants?: ProductVariant[];
  flags?: ProductFlag[];
  seoTitle?: string;
  seoDescription?: string;
}

interface Category {
  _id: string;
  _type: 'category';
  title: MultilingualText;
  slug: SanitySlug;
  image?: SanityImage;
  description?: MultilingualTextArea;
  parentCategory?: SanityReference;
  tags?: string[];
  flags?: CategoryFlag[];
  seoTitle?: string;
  seoDescription?: string;
}
```

## 🧪 Test ve Doğrulama

### Type Safety Test

```typescript
import { testTypes } from './test-api';

// Type'ların doğru çalıştığını test et
testTypes();
```

### API Test

```bash
# Ürünleri test et
curl http://localhost:3000/api/products

# Kategorileri test et
curl http://localhost:3000/api/categories
```

## 📝 Kullanım Örnekleri

Detaylı kullanım örnekleri için `examples/product-usage.tsx` dosyasına bakın.

## 🔄 Sonraki Adımlar

1. **Sanity Studio'yu yeniden başlat** - Yeni şemaları görmek için
2. **Test verileri ekle** - Yeni alanları test etmek için
3. **Frontend entegrasyonu** - React bileşenlerinde kullan
4. **SEO optimizasyonu** - Meta tag'leri implement et
5. **Performans optimizasyonu** - Image CDN ve caching

## 🐛 Sorun Giderme

### Type Errors

```bash
# Sanity types'ı yeniden generate et
npx sanity typegen generate
```

### API Errors

- API endpoint'lerinin doğru çalıştığını kontrol et
- Sanity client konfigürasyonunu kontrol et
- Environment variables'ları kontrol et

## 📚 Kaynaklar

- [Sanity Documentation](https://www.sanity.io/docs)
- [React Query Documentation](https://tanstack.com/query/latest)
- [TypeScript Documentation](https://www.typescriptlang.org/docs)

## 🆕 Yeni Eklenen Özellikler

### Order Management
- **Otomatik Toplam Hesaplama**: Sipariş tutarı otomatik hesaplanır
- **Stok Kontrolü**: Sipariş verilirken stok doğrulaması
- **Kampanya Entegrasyonu**: Kupon kodları ile indirim uygulaması
- **Durum Takibi**: Sipariş durumu değişikliklerinin otomatik takibi

### User Management
- **Çoklu Adres Desteği**: Kullanıcılar birden fazla adres kaydedebilir
- **Rol Tabanlı Yetkilendirme**: Admin, moderatör, finans yöneticisi rolleri
- **Cüzdan Sistemi**: Kullanıcı cüzdan bakiyesi yönetimi
- **Onay Sistemi**: Admin onayı gerektiren kullanıcı kayıtları

### Sales & Campaigns
- **Dinamik Kampanyalar**: Yüzde ve sabit tutar indirimleri
- **Kupon Sistemi**: Benzersiz kupon kodları
- **Hedefleme**: Belirli kullanıcı gruplarına özel kampanyalar
- **Otomatik Durum Güncelleme**: Kampanya süresi dolduğunda otomatik pasifleştirme

### Wallet Transactions
- **Cüzdan İşlemleri**: Yükleme, harcama, iade, bonus işlemleri
- **Onay Süreci**: Admin onayı gerektiren işlemler
- **İşlem Geçmişi**: Detaylı işlem takibi
- **Bakiye Yönetimi**: Otomatik bakiye güncellemeleri

---

**Not**: Bu entegrasyon, modern e-ticaret uygulamaları için production-ready bir yapı sunar. Tüm özellikler TypeScript ile tip güvenliği sağlar, React Query ile optimize edilmiş data fetching sunar ve Sanity Actions ile güçlü iş mantığı yönetimi sağlar.
