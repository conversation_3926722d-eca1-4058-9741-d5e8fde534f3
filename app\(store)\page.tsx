import { getAllProducts } from "@/sanity/lib/products/getAllProducts";
import { getAllCategories } from "@/sanity/lib/products/getAllCategories";
import ProductsView from "@/components/productsView";
import DynamicBanners from "@/components/DynamicBanners";
import { Benefits } from "@/components/stabilPage/benefits";
import AuctionBanners from "@/components/AuctionBanners";
import GiveawayBanners from "@/components/GiveawayBanners";

export default async function Home() {
  const productsData = await getAllProducts();
  const categories = await getAllCategories();
  
  // Tip güvenliği için dönüştürme yapıyoruz
  const products = Array.isArray(productsData) ? productsData : [];

  return (
    <div>
      {/* <BlackFridayBanner />
      <Trendfy_X />  */}
      <DynamicBanners />
      <GiveawayBanners />
      <AuctionBanners />

      <div className="flex flex-col w-full items-center justify-top min-h-screen bg-gray-100 p-4">
        <ProductsView products={products} categories={categories} />
        <Benefits />
      </div>
    </div>
  );
}
