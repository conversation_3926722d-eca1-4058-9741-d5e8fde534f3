import { NextRequest, NextResponse } from 'next/server'
import { getUserByClerkId } from '@/sanity/lib/users/getUser'

export async function POST(req: NextRequest) {
  try {
    const { clerkId, email, name } = await req.json()
    
    console.log('🧪 Testing getUserByClerkId with:', { clerkId, email, name })
    
    if (!clerkId || !email) {
      return NextResponse.json({
        success: false,
        error: 'clerkId and email are required'
      }, { status: 400 })
    }
    
    const user = await getUserByClerkId({
      id: clerkId,
      email: email,
      name: name || 'Test User'
    })
    
    console.log('👤 getUserByClerkId result:', user)
    
    return NextResponse.json({
      success: true,
      user: user,
      message: user ? 'User found/created successfully' : 'User not found and could not be created'
    })
    
  } catch (error) {
    console.error('❌ Test error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function GET() {
  try {
    console.log('📊 Testing with existing user')
    
    // Test with an existing user
    const existingUser = await getUserByClerkId({
      id: 'user_2yI8uUZ28R5xM4ddI5i47Yx3WZ7', // Ege's clerk ID
      email: '<EMAIL>',
      name: 'Ege Hayzaran'
    })
    
    console.log('👤 Existing user result:', existingUser)
    
    // Test with a new user
    const newUser = await getUserByClerkId({
      id: `test_clerk_${Date.now()}`,
      email: '<EMAIL>',
      name: 'New Test User'
    })
    
    console.log('👤 New user result:', newUser)
    
    return NextResponse.json({
      success: true,
      existingUser: existingUser,
      newUser: newUser,
      message: 'User function tests completed'
    })
    
  } catch (error) {
    console.error('❌ Test error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
