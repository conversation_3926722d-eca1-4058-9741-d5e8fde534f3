"use client";
import WalletTopUpForm from './WalletTopUpForm';

export default function WalletClient({ user, requests, cards = [] }: { user: any, requests: any[], cards?: any[] }) {
  // Helper to normalize ticket numbers (trim only, do not remove leading zeros)
  function normalizeTicketNumber(num: any) {
    return String(num).trim();
  }

  // Helper to check if this card is a winner
  const isCardWinner = (card: any) => {
    if (!card.winningNumbers || card.winningNumbers.length === 0) return null;
    // Karttaki herhangi bir numara kazanan numaralar arasında mı?
    return card.numbers.some((num: string) => card.winningNumbers.includes(num));
  };

  return (
    <div className="max-w-xl mx-auto p-6">
      <h1 className="text-3xl font-bold mb-4">Cüzdanım</h1>
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <div className="flex items-center justify-between">
          <span className="text-lg font-semibold">Bakiyeniz:</span>
          <span className="text-2xl font-bold text-green-600">₺{user?.walletBalance !== undefined ? user.walletBalance.toFixed(2) : '0.00'}</span>
        </div>
      </div>

      {/* Kartlarım */}
      <div className="mb-8">
        <h2 className="text-xl font-bold mb-2">Kartlarım</h2>
        <div className="bg-gray-50 rounded-lg p-4 max-h-80 overflow-y-auto">
          {cards && cards.length > 0 ? (
            <ul className="space-y-3">
              {cards.map((card, idx) => (
                <li key={idx} className="bg-white rounded-lg shadow p-4 flex flex-col md:flex-row md:items-center md:justify-between border border-blue-100">
                  <div className="mb-2 md:mb-0">
                    <span className="font-semibold text-blue-700">{card.giveawayTitle}</span>
                    <span className="ml-2 text-gray-500 text-xs">{card.purchasedAt ? new Date(card.purchasedAt).toLocaleString('tr-TR') : ''}</span>
                  </div>
                  <div className="flex gap-2 flex-wrap items-center">
                    {card.numbers.map((num: string, i: number) => (
                      <span key={i} className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full font-mono font-bold text-lg tracking-widest">
                        {num}
                      </span>
                    ))}
                    {typeof isCardWinner(card) === 'boolean' && (
                      isCardWinner(card) ? (
                        <span className="ml-2 px-3 py-1 rounded-full bg-green-200 text-green-800 font-semibold">Kazandınız</span>
                      ) : (
                        <span className="ml-2 px-3 py-1 rounded-full bg-red-200 text-red-800 font-semibold">Kazanamadınız</span>
                      )
                    )}
                    {isCardWinner(card) === null && (
                      <span className="ml-2 px-3 py-1 rounded-full bg-blue-200 text-blue-800 font-semibold">Çekiliş Bekleniyor</span>
                    )}
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <div className="text-gray-500">Henüz kartınız yok.</div>
          )}
        </div>
      </div>

      {user?._id && <WalletTopUpForm userId={user._id} />}
      <div className="mt-8">
        <h2 className="text-xl font-bold mb-2">Yükleme Taleplerim</h2>
        <div className="bg-gray-50 rounded-lg p-4">
          {requests && requests.length > 0 ? (
            <ul className="space-y-2">
              {requests.map((req: any) => (
                <li key={req._id} className="flex items-center justify-between border-b py-2 last:border-b-0">
                  <span>{new Date(req.createdAt).toLocaleString('tr-TR')}</span>
                  <span>₺{req.amount}</span>
                  <span className={`px-2 py-1 rounded text-xs font-semibold ${req.status === 'approved' ? 'bg-green-100 text-green-700' : req.status === 'pending' ? 'bg-yellow-100 text-yellow-700' : 'bg-red-100 text-red-700'}`}>{req.status === 'approved' ? 'Onaylandı' : req.status === 'pending' ? 'Bekliyor' : 'Reddedildi'}</span>
                </li>
              ))}
            </ul>
          ) : (
            <div className="text-gray-500">Henüz yükleme talebiniz yok.</div>
          )}
        </div>
      </div>
    </div>
  );
} 