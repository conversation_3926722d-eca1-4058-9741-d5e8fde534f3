import { NextRequest, NextResponse } from 'next/server'
import { client } from '@/sanity/lib/client'

export async function POST(req: NextRequest) {
  try {
    console.log('🧪 Test Clerk webhook endpoint called')
    
    // Test user data
    const testUser = {
      _type: 'user',
      _id: `test_user_${Date.now()}`,
      clerkId: `test_clerk_${Date.now()}`,
      name: 'Test User',
      email: '<EMAIL>',
      isAdminApproved: false,
      imageUrl: 'https://example.com/test-image.jpg'
    }
    
    console.log('💾 Creating test user in Sanity:', testUser)
    
    // Test Sanity connection
    const result = await client.createIfNotExists(testUser)
    console.log('✅ Test user created in Sanity:', result)
    
    // Test fetching users
    const users = await client.fetch('*[_type == "user"] | order(_createdAt desc) [0...5]')
    console.log('👥 Recent users in Sanity:', users)
    
    return NextResponse.json({
      success: true,
      message: 'Test webhook completed successfully',
      testUser: result,
      recentUsers: users
    })
    
  } catch (error) {
    console.error('❌ Test webhook error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function GET() {
  try {
    console.log('📊 Getting Sanity connection status')
    
    // Test basic connection
    const users = await client.fetch('*[_type == "user"] | order(_createdAt desc) [0...10]')
    
    return NextResponse.json({
      success: true,
      message: 'Sanity connection working',
      userCount: users.length,
      users: users
    })
    
  } catch (error) {
    console.error('❌ Sanity connection error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
