import { NextRequest, NextResponse } from 'next/server';
import { client } from '@/sanity/lib/client';
import { Auction, AuctionQueryParams } from '@/types/sanity';

// Enhanced GROQ query for auctions
const AUCTIONS_QUERY = `
  *[_type == "auction" 
    $statusFilter
    $bidFilter
    $dateFilter
    $searchFilter
  ] | order($sortBy $sortOrder) [$start...$end] {
    _id,
    _type,
    _createdAt,
    _updatedAt,
    _rev,
    id,
    product->{
      _id,
      name,
      image,
      price,
      currency
    },
    description,
    image {
      asset->{
        _id,
        url
      },
      alt,
      hotspot,
      crop
    },
    startingBid,
    currentBid,
    bidIncrementAmount,
    currency,
    bidders[]->{
      _id,
      name,
      email
    },
    bidHistory[] {
      bidAmount,
      bidder->{
        _id,
        name,
        email
      },
      bidTime
    },
    winner->{
      _id,
      name,
      email
    },
    startTime,
    endTime,
    status,
    sale->{
      _id,
      title,
      couponCode
    }
  }
`;

const COUNT_QUERY = `
  count(*[_type == "auction" 
    $statusFilter
    $bidFilter
    $dateFilter
    $searchFilter
  ])
`;

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Check for special endpoints
    const type = searchParams.get('type');
    
    if (type === 'active') {
      const activeAuctions = await client.fetch(`
        *[_type == "auction" && status == "active" && startTime <= now() && endTime >= now()] | order(endTime asc) {
          _id, id, product->{_id, name, image}, currentBid, currency, endTime, status
        }
      `);
      return NextResponse.json({ data: activeAuctions, success: true });
    }
    
    if (type === 'upcoming') {
      const upcomingAuctions = await client.fetch(`
        *[_type == "auction" && status == "upcoming" && startTime > now()] | order(startTime asc) {
          _id, id, product->{_id, name, image}, startingBid, currency, startTime, status
        }
      `);
      return NextResponse.json({ data: upcomingAuctions, success: true });
    }
    
    if (type === 'completed') {
      const completedAuctions = await client.fetch(`
        *[_type == "auction" && status == "completed"] | order(endTime desc) {
          _id, id, product->{_id, name, image}, currentBid, currency, winner->{_id, name}, endTime, status
        }
      `);
      return NextResponse.json({ data: completedAuctions, success: true });
    }

    // Parse query parameters for regular auction fetching
    const params: AuctionQueryParams = {
      status: searchParams.get('status') as any || undefined,
      minBid: searchParams.get('minBid') ? Number(searchParams.get('minBid')) : undefined,
      maxBid: searchParams.get('maxBid') ? Number(searchParams.get('maxBid')) : undefined,
      dateFrom: searchParams.get('dateFrom') || undefined,
      dateTo: searchParams.get('dateTo') || undefined,
      search: searchParams.get('search') || undefined,
      page: Number(searchParams.get('page')) || 1,
      limit: Number(searchParams.get('limit')) || 20,
      sortBy: (searchParams.get('sortBy') as any) || 'endTime',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'asc',
      language: (searchParams.get('language') as 'tr' | 'en') || 'tr',
    };

    // Build dynamic filters
    let statusFilter = '';
    let bidFilter = '';
    let dateFilter = '';
    let searchFilter = '';

    if (params.status) {
      statusFilter = `&& status == "${params.status}"`;
    }

    if (params.minBid !== undefined || params.maxBid !== undefined) {
      const bidConditions = [];
      if (params.minBid !== undefined) bidConditions.push(`currentBid >= ${params.minBid}`);
      if (params.maxBid !== undefined) bidConditions.push(`currentBid <= ${params.maxBid}`);
      bidFilter = `&& (${bidConditions.join(' && ')})`;
    }

    if (params.dateFrom || params.dateTo) {
      const dateConditions = [];
      if (params.dateFrom) dateConditions.push(`startTime >= "${params.dateFrom}"`);
      if (params.dateTo) dateConditions.push(`endTime <= "${params.dateTo}"`);
      dateFilter = `&& (${dateConditions.join(' && ')})`;
    }

    if (params.search) {
      const searchTerm = params.search.toLowerCase();
      searchFilter = `&& (
        product->name.tr match "*${searchTerm}*" ||
        product->name.en match "*${searchTerm}*" ||
        description.tr match "*${searchTerm}*" ||
        description.en match "*${searchTerm}*"
      )`;
    }

    // Calculate pagination
    const start = (params.page - 1) * params.limit;
    const end = start + params.limit;

    // Build final queries
    const finalAuctionsQuery = AUCTIONS_QUERY
      .replace('$statusFilter', statusFilter)
      .replace('$bidFilter', bidFilter)
      .replace('$dateFilter', dateFilter)
      .replace('$searchFilter', searchFilter)
      .replace('$sortBy', params.sortBy || 'endTime')
      .replace('$sortOrder', params.sortOrder || 'asc')
      .replace('$start', start.toString())
      .replace('$end', end.toString());

    const finalCountQuery = COUNT_QUERY
      .replace('$statusFilter', statusFilter)
      .replace('$bidFilter', bidFilter)
      .replace('$dateFilter', dateFilter)
      .replace('$searchFilter', searchFilter);

    // Execute queries
    const [auctions, total] = await Promise.all([
      client.fetch<Auction[]>(finalAuctionsQuery),
      client.fetch<number>(finalCountQuery)
    ]);

    // Return response
    return NextResponse.json({
      data: auctions,
      pagination: {
        page: params.page,
        limit: params.limit,
        total,
        totalPages: Math.ceil(total / params.limit),
        hasNext: end < total,
        hasPrev: params.page > 1,
      },
      success: true,
    });

  } catch (error) {
    console.error('Error fetching auctions:', error);
    return NextResponse.json(
      { 
        error: { 
          message: 'Failed to fetch auctions',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        success: false 
      },
      { status: 500 }
    );
  }
}
