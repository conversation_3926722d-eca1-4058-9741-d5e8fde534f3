import { useQuery } from '@tanstack/react-query';
import { api } from '../lib/api';
import { 
  Notification, 
  NotificationWithDetails,
  ApiResponse 
} from '../types/sanity';

// Query parameters interface
export interface NotificationQueryParams {
  userId?: string;
  type?: 'auction_bid' | 'auction_won' | 'auction_outbid' | 'giveaway_won' | 'order_status' | 'wallet_update' | 'general';
  isRead?: boolean;
  dateFrom?: string;
  dateTo?: string;
  page?: number;
  limit?: number;
  sortBy?: 'sentAt' | 'type';
  sortOrder?: 'asc' | 'desc';
  language?: 'tr' | 'en';
}

// API functions
const fetchNotifications = async (params?: NotificationQueryParams): Promise<Notification[]> => {
  const response = await api.get('/notifications', params);
  return response.data;
};

const fetchNotificationById = async (id: string): Promise<Notification | null> => {
  const response = await api.get(`/notifications/${id}`);
  return response.data;
};

const fetchUserNotifications = async (userId: string): Promise<Notification[]> => {
  const response = await api.get(`/notifications/user/${userId}`);
  return response.data;
};

const fetchUnreadNotifications = async (userId: string): Promise<Notification[]> => {
  const response = await api.get(`/notifications/user/${userId}/unread`);
  return response.data;
};

const markAsRead = async (notificationId: string): Promise<{ success: boolean }> => {
  const response = await api.patch(`/notifications/${notificationId}/read`);
  return response.data;
};

const markAllAsRead = async (userId: string): Promise<{ success: boolean }> => {
  const response = await api.patch(`/notifications/user/${userId}/read-all`);
  return response.data;
};

const deleteNotification = async (notificationId: string): Promise<{ success: boolean }> => {
  const response = await api.delete(`/notifications/${notificationId}`);
  return response.data;
};

// React Query hooks
export const useNotifications = (params?: NotificationQueryParams) => {
  return useQuery<Notification[], Error>({
    queryKey: ['notifications', params],
    queryFn: () => fetchNotifications(params),
    staleTime: 1 * 60 * 1000, // 1 minute
    retry: 2,
    refetchOnWindowFocus: true,
  });
};

export const useNotification = (id: string) => {
  return useQuery<Notification | null, Error>({
    queryKey: ['notification', id],
    queryFn: () => fetchNotificationById(id),
    staleTime: 5 * 60 * 1000,
    retry: 2,
    enabled: !!id,
  });
};

export const useUserNotifications = (userId: string) => {
  return useQuery<Notification[], Error>({
    queryKey: ['notifications', 'user', userId],
    queryFn: () => fetchUserNotifications(userId),
    staleTime: 30 * 1000, // 30 seconds for user notifications
    retry: 2,
    enabled: !!userId,
    refetchInterval: 30 * 1000, // Auto-refresh every 30 seconds
  });
};

export const useUnreadNotifications = (userId: string) => {
  return useQuery<Notification[], Error>({
    queryKey: ['notifications', 'unread', userId],
    queryFn: () => fetchUnreadNotifications(userId),
    staleTime: 15 * 1000, // 15 seconds for unread notifications
    retry: 2,
    enabled: !!userId,
    refetchInterval: 15 * 1000, // Auto-refresh every 15 seconds
  });
};

// Utility hooks
export const useNotificationTypes = () => {
  const types = [
    { value: 'auction_bid', label: 'Açık Artırma Teklifi', icon: '🔨', color: 'blue' },
    { value: 'auction_won', label: 'Açık Artırma Kazandı', icon: '🏆', color: 'green' },
    { value: 'auction_outbid', label: 'Teklif Aşıldı', icon: '⚠️', color: 'orange' },
    { value: 'giveaway_won', label: 'Çekiliş Kazandı', icon: '🎁', color: 'purple' },
    { value: 'order_status', label: 'Sipariş Durumu', icon: '📦', color: 'blue' },
    { value: 'wallet_update', label: 'Cüzdan Güncelleme', icon: '💰', color: 'green' },
    { value: 'general', label: 'Genel', icon: '📢', color: 'gray' },
  ] as const;

  return types;
};

// Helper functions
export const getLocalizedNotificationTitle = (
  title: { tr: string; en?: string } | undefined,
  language: 'tr' | 'en' = 'tr'
): string => {
  if (!title) return '';
  return language === 'en' && title.en ? title.en : title.tr;
};

export const getLocalizedNotificationMessage = (
  message: { tr: string; en?: string } | undefined,
  language: 'tr' | 'en' = 'tr'
): string => {
  if (!message) return '';
  return language === 'en' && message.en ? message.en : message.tr;
};

export const getNotificationTypeIcon = (type: string): string => {
  const typeIcons: Record<string, string> = {
    auction_bid: '🔨',
    auction_won: '🏆',
    auction_outbid: '⚠️',
    giveaway_won: '🎁',
    order_status: '📦',
    wallet_update: '💰',
    general: '📢',
  };
  return typeIcons[type] || '📢';
};

export const getNotificationTypeColor = (type: string): string => {
  const typeColors: Record<string, string> = {
    auction_bid: '#3b82f6',
    auction_won: '#10b981',
    auction_outbid: '#f59e0b',
    giveaway_won: '#8b5cf6',
    order_status: '#3b82f6',
    wallet_update: '#10b981',
    general: '#6b7280',
  };
  return typeColors[type] || '#6b7280';
};

export const formatNotificationTime = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (minutes < 1) return 'Şimdi';
  if (minutes < 60) return `${minutes} dakika önce`;
  if (hours < 24) return `${hours} saat önce`;
  if (days < 7) return `${days} gün önce`;
  
  return date.toLocaleDateString('tr-TR', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

export const getUnreadCount = (notifications: Notification[]): number => {
  return notifications.filter(notification => !notification.isRead).length;
};

export const groupNotificationsByDate = (notifications: Notification[]): Record<string, Notification[]> => {
  const groups: Record<string, Notification[]> = {};
  
  notifications.forEach(notification => {
    const date = new Date(notification.sentAt);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    let groupKey: string;
    
    if (date.toDateString() === today.toDateString()) {
      groupKey = 'Bugün';
    } else if (date.toDateString() === yesterday.toDateString()) {
      groupKey = 'Dün';
    } else {
      groupKey = date.toLocaleDateString('tr-TR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
    }
    
    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }
    
    groups[groupKey].push(notification);
  });
  
  return groups;
};

export const filterNotificationsByType = (notifications: Notification[], types: string[]): Notification[] => {
  if (types.length === 0) return notifications;
  return notifications.filter(notification => types.includes(notification.type));
};

export const filterNotificationsByRead = (notifications: Notification[], isRead?: boolean): Notification[] => {
  if (isRead === undefined) return notifications;
  return notifications.filter(notification => notification.isRead === isRead);
};

// Notification creation helpers (for admin use)
export const createNotificationData = (
  type: string,
  title: { tr: string; en?: string },
  message: { tr: string; en?: string },
  userId?: string,
  relatedDocumentId?: string
) => {
  return {
    type,
    title,
    message,
    user: userId ? { _type: 'reference', _ref: userId } : undefined,
    relatedDocument: relatedDocumentId ? { _type: 'reference', _ref: relatedDocumentId } : undefined,
    sentAt: new Date().toISOString(),
    isRead: false,
  };
};

// Sort helpers
export const sortNotifications = (
  notifications: Notification[], 
  sortBy: 'sentAt' | 'type' = 'sentAt',
  sortOrder: 'asc' | 'desc' = 'desc'
): Notification[] => {
  return [...notifications].sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'sentAt':
        comparison = new Date(a.sentAt).getTime() - new Date(b.sentAt).getTime();
        break;
      case 'type':
        comparison = a.type.localeCompare(b.type);
        break;
    }
    
    return sortOrder === 'desc' ? -comparison : comparison;
  });
};
