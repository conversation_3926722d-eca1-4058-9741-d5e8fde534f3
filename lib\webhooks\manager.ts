// Webhook Management System
import crypto from 'crypto';
import { ErrorTracker } from '../sentry/config';

// Webhook event types
export type WebhookEventType = 
  | 'auction.created'
  | 'auction.started'
  | 'auction.bid_placed'
  | 'auction.completed'
  | 'auction.cancelled'
  | 'giveaway.created'
  | 'giveaway.started'
  | 'giveaway.ticket_purchased'
  | 'giveaway.draw_ready'
  | 'giveaway.completed'
  | 'order.created'
  | 'order.updated'
  | 'order.cancelled'
  | 'payment.completed'
  | 'payment.failed'
  | 'wallet.transaction_created'
  | 'wallet.balance_updated'
  | 'user.created'
  | 'user.updated'
  | 'notification.sent';

// Webhook payload interface
export interface WebhookPayload {
  id: string;
  event: WebhookEventType;
  timestamp: string;
  data: Record<string, any>;
  version: string;
}

// Webhook endpoint interface
export interface WebhookEndpoint {
  id: string;
  url: string;
  events: WebhookEventType[];
  secret: string;
  isActive: boolean;
  retryCount: number;
  timeout: number;
  headers?: Record<string, string>;
  createdAt: string;
  updatedAt: string;
}

// Webhook delivery attempt interface
export interface WebhookDelivery {
  id: string;
  endpointId: string;
  payload: WebhookPayload;
  status: 'pending' | 'success' | 'failed' | 'retrying';
  httpStatus?: number;
  responseBody?: string;
  errorMessage?: string;
  attemptCount: number;
  nextRetryAt?: string;
  deliveredAt?: string;
  createdAt: string;
}

// Webhook manager class
export class WebhookManager {
  private static instance: WebhookManager;
  private endpoints: Map<string, WebhookEndpoint> = new Map();
  private deliveryQueue: WebhookDelivery[] = [];
  private isProcessing = false;

  private constructor() {}

  static getInstance(): WebhookManager {
    if (!WebhookManager.instance) {
      WebhookManager.instance = new WebhookManager();
    }
    return WebhookManager.instance;
  }

  // Register webhook endpoint
  async registerEndpoint(endpoint: Omit<WebhookEndpoint, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const id = crypto.randomUUID();
    const now = new Date().toISOString();
    
    const webhookEndpoint: WebhookEndpoint = {
      ...endpoint,
      id,
      createdAt: now,
      updatedAt: now,
    };

    this.endpoints.set(id, webhookEndpoint);
    
    // Save to database (implement based on your storage)
    await this.saveEndpointToDatabase(webhookEndpoint);
    
    return id;
  }

  // Update webhook endpoint
  async updateEndpoint(id: string, updates: Partial<WebhookEndpoint>): Promise<boolean> {
    const endpoint = this.endpoints.get(id);
    if (!endpoint) return false;

    const updatedEndpoint = {
      ...endpoint,
      ...updates,
      updatedAt: new Date().toISOString(),
    };

    this.endpoints.set(id, updatedEndpoint);
    await this.saveEndpointToDatabase(updatedEndpoint);
    
    return true;
  }

  // Delete webhook endpoint
  async deleteEndpoint(id: string): Promise<boolean> {
    const deleted = this.endpoints.delete(id);
    if (deleted) {
      await this.deleteEndpointFromDatabase(id);
    }
    return deleted;
  }

  // Send webhook event
  async sendEvent(event: WebhookEventType, data: Record<string, any>): Promise<void> {
    const payload: WebhookPayload = {
      id: crypto.randomUUID(),
      event,
      timestamp: new Date().toISOString(),
      data,
      version: '1.0',
    };

    // Find endpoints that listen to this event
    const relevantEndpoints = Array.from(this.endpoints.values()).filter(
      endpoint => endpoint.isActive && endpoint.events.includes(event)
    );

    // Create delivery attempts for each endpoint
    for (const endpoint of relevantEndpoints) {
      const delivery: WebhookDelivery = {
        id: crypto.randomUUID(),
        endpointId: endpoint.id,
        payload,
        status: 'pending',
        attemptCount: 0,
        createdAt: new Date().toISOString(),
      };

      this.deliveryQueue.push(delivery);
      await this.saveDeliveryToDatabase(delivery);
    }

    // Start processing queue if not already processing
    if (!this.isProcessing) {
      this.processDeliveryQueue();
    }
  }

  // Process delivery queue
  private async processDeliveryQueue(): Promise<void> {
    if (this.isProcessing) return;
    this.isProcessing = true;

    try {
      while (this.deliveryQueue.length > 0) {
        const delivery = this.deliveryQueue.shift();
        if (!delivery) continue;

        await this.attemptDelivery(delivery);
      }
    } catch (error) {
      ErrorTracker.captureException(error as Error, { context: 'webhook_processing' });
    } finally {
      this.isProcessing = false;
    }
  }

  // Attempt webhook delivery
  private async attemptDelivery(delivery: WebhookDelivery): Promise<void> {
    const endpoint = this.endpoints.get(delivery.endpointId);
    if (!endpoint) {
      delivery.status = 'failed';
      delivery.errorMessage = 'Endpoint not found';
      await this.updateDeliveryInDatabase(delivery);
      return;
    }

    delivery.attemptCount++;
    delivery.status = 'retrying';

    try {
      const signature = this.generateSignature(delivery.payload, endpoint.secret);
      const headers = {
        'Content-Type': 'application/json',
        'X-Webhook-Signature': signature,
        'X-Webhook-Event': delivery.payload.event,
        'X-Webhook-ID': delivery.payload.id,
        'X-Webhook-Timestamp': delivery.payload.timestamp,
        ...endpoint.headers,
      };

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), endpoint.timeout || 30000);

      const response = await fetch(endpoint.url, {
        method: 'POST',
        headers,
        body: JSON.stringify(delivery.payload),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      delivery.httpStatus = response.status;
      delivery.responseBody = await response.text();

      if (response.ok) {
        delivery.status = 'success';
        delivery.deliveredAt = new Date().toISOString();
      } else {
        delivery.status = 'failed';
        delivery.errorMessage = `HTTP ${response.status}: ${delivery.responseBody}`;
      }

    } catch (error) {
      delivery.status = 'failed';
      delivery.errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      ErrorTracker.captureException(error as Error, {
        webhook: {
          endpointId: delivery.endpointId,
          event: delivery.payload.event,
          attemptCount: delivery.attemptCount,
        },
      });
    }

    // Schedule retry if failed and under retry limit
    if (delivery.status === 'failed' && delivery.attemptCount < endpoint.retryCount) {
      const retryDelay = this.calculateRetryDelay(delivery.attemptCount);
      delivery.nextRetryAt = new Date(Date.now() + retryDelay).toISOString();
      delivery.status = 'pending';
      
      // Re-queue for retry
      setTimeout(() => {
        this.deliveryQueue.push(delivery);
        if (!this.isProcessing) {
          this.processDeliveryQueue();
        }
      }, retryDelay);
    }

    await this.updateDeliveryInDatabase(delivery);
  }

  // Generate webhook signature
  private generateSignature(payload: WebhookPayload, secret: string): string {
    const payloadString = JSON.stringify(payload);
    return crypto
      .createHmac('sha256', secret)
      .update(payloadString)
      .digest('hex');
  }

  // Calculate retry delay with exponential backoff
  private calculateRetryDelay(attemptCount: number): number {
    const baseDelay = 1000; // 1 second
    const maxDelay = 300000; // 5 minutes
    const delay = Math.min(baseDelay * Math.pow(2, attemptCount - 1), maxDelay);
    
    // Add jitter to prevent thundering herd
    const jitter = Math.random() * 0.1 * delay;
    return delay + jitter;
  }

  // Verify webhook signature
  static verifySignature(payload: string, signature: string, secret: string): boolean {
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest('hex');
    
    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  }

  // Database operations (implement based on your storage solution)
  private async saveEndpointToDatabase(endpoint: WebhookEndpoint): Promise<void> {
    // Implement database save logic
    console.log('Saving endpoint to database:', endpoint.id);
  }

  private async deleteEndpointFromDatabase(id: string): Promise<void> {
    // Implement database delete logic
    console.log('Deleting endpoint from database:', id);
  }

  private async saveDeliveryToDatabase(delivery: WebhookDelivery): Promise<void> {
    // Implement database save logic
    console.log('Saving delivery to database:', delivery.id);
  }

  private async updateDeliveryInDatabase(delivery: WebhookDelivery): Promise<void> {
    // Implement database update logic
    console.log('Updating delivery in database:', delivery.id);
  }

  // Get endpoint statistics
  async getEndpointStats(endpointId: string): Promise<{
    totalDeliveries: number;
    successfulDeliveries: number;
    failedDeliveries: number;
    averageResponseTime: number;
    lastDelivery?: string;
  }> {
    // Implement stats calculation from database
    return {
      totalDeliveries: 0,
      successfulDeliveries: 0,
      failedDeliveries: 0,
      averageResponseTime: 0,
    };
  }

  // Get recent deliveries
  async getRecentDeliveries(endpointId: string, limit = 50): Promise<WebhookDelivery[]> {
    // Implement database query
    return [];
  }
}

// Webhook event builders
export const WebhookEvents = {
  auction: {
    created: (auction: any) => ({ auction }),
    started: (auction: any) => ({ auction }),
    bidPlaced: (auction: any, bid: any, bidder: any) => ({ auction, bid, bidder }),
    completed: (auction: any, winner?: any) => ({ auction, winner }),
    cancelled: (auction: any, reason?: string) => ({ auction, reason }),
  },

  giveaway: {
    created: (giveaway: any) => ({ giveaway }),
    started: (giveaway: any) => ({ giveaway }),
    ticketPurchased: (giveaway: any, tickets: any[], buyer: any) => ({ giveaway, tickets, buyer }),
    drawReady: (giveaway: any) => ({ giveaway }),
    completed: (giveaway: any, winners: any[]) => ({ giveaway, winners }),
  },

  order: {
    created: (order: any) => ({ order }),
    updated: (order: any, previousStatus: string) => ({ order, previousStatus }),
    cancelled: (order: any, reason?: string) => ({ order, reason }),
  },

  payment: {
    completed: (payment: any, order: any) => ({ payment, order }),
    failed: (payment: any, error: string) => ({ payment, error }),
  },

  wallet: {
    transactionCreated: (transaction: any) => ({ transaction }),
    balanceUpdated: (userId: string, oldBalance: number, newBalance: number) => ({
      userId,
      oldBalance,
      newBalance,
      change: newBalance - oldBalance,
    }),
  },

  user: {
    created: (user: any) => ({ user }),
    updated: (user: any, changes: Record<string, any>) => ({ user, changes }),
  },

  notification: {
    sent: (notification: any, recipient: any) => ({ notification, recipient }),
  },
};

export default WebhookManager;
