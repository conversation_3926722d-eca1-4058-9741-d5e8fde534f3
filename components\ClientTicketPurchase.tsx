'use client';

import dynamic from 'next/dynamic';
import React, { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import SanityClient from '@sanity/client';
// import { Elements } from '@stripe/react-stripe-js';
// import { loadStripe } from '@stripe/stripe-js';

// const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface ClientTicketPurchaseProps {
  drawId: string;
  ticketPrice: number;
  ticketDigitCount: number;
  ticketNumberCount: number;
}

const TicketPurchase = dynamic(() => import('@/components/TicketPurchase'), {
  ssr: false,
});

export default function ClientTicketPurchase(props: ClientTicketPurchaseProps) {
  const { user } = useUser();
  const [ticketNumbers, setTicketNumbers] = useState<number[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [ticketCreated, setTicketCreated] = useState(false);

  const sanity = SanityClient({
    projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,
    dataset: process.env.NEXT_PUBLIC_SANITY_DATASET!,
    apiVersion: '2023-06-10',
    useCdn: true,
  });

  async function createTicket() {
    if (!user) {
      setError('Lütfen giriş yapın.');
      return;
    }
    if (!props.drawId) {
      setError('Lütfen bir çekiliş seçin.');
      return;
    }

    setLoading(true);
    setError(null);
    setTicketCreated(false);

    try {
      const res = await fetch(`/api/create-ticket`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId: user.id, drawId: props.drawId })
      });

      if (!res.ok) {
        const errData = await res.json();
        throw new Error(errData.error || 'Bilet oluşturulamadı.');
      }
      const data = await res.json();

      if (data.numbers && data.numbers.length > 0) {
        setTicketNumbers(data.numbers);
        setTicketCreated(true);
      } else {
        throw new Error('API yanıtından bilet numaraları alınamadı.');
      }

    } catch (e: any) {
      setError(e.message);
    }
    setLoading(false);
  }

  return (
    <TicketPurchase
      {...props}
      ticketNumbers={ticketNumbers}
      setTicketNumbers={setTicketNumbers}
      createTicket={createTicket}
      loading={loading}
      error={error}
      ticketCreated={ticketCreated}
    />
  );
} 