import { NextRequest, NextResponse } from 'next/server'
import { client } from '@/sanity/lib/client'

export async function POST(req: NextRequest) {
  try {
    const { clerkId, approve = true } = await req.json()
    
    console.log('👑 Approving user:', { clerkId, approve })
    
    if (!clerkId) {
      return NextResponse.json({
        success: false,
        error: 'clerkId is required'
      }, { status: 400 })
    }
    
    // Find user by clerkId
    const user = await client.fetch(`
      *[_type == "user" && clerkId == $clerkId][0] {
        _id,
        clerkId,
        name,
        email,
        isAdminApproved
      }
    `, { clerkId })
    
    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'User not found'
      }, { status: 404 })
    }
    
    console.log('👤 Found user:', user)
    
    // Update user approval status
    const result = await client
      .patch(user._id)
      .set({ isAdminApproved: approve })
      .commit()
    
    console.log('✅ User approval updated:', result)
    
    return NextResponse.json({
      success: true,
      message: `User ${approve ? 'approved' : 'unapproved'} successfully`,
      user: {
        ...user,
        isAdminApproved: approve
      }
    })
    
  } catch (error) {
    console.error('❌ Error approving user:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function GET() {
  try {
    // Get all users with their approval status
    const users = await client.fetch(`
      *[_type == "user"] | order(_createdAt desc) {
        _id,
        clerkId,
        name,
        email,
        isAdminApproved,
        _createdAt
      }
    `)
    
    return NextResponse.json({
      success: true,
      users: users,
      approvedCount: users.filter((u: any) => u.isAdminApproved).length,
      totalCount: users.length
    })
    
  } catch (error) {
    console.error('❌ Error fetching users:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
