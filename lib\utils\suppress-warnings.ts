// Suppress React warnings for known Sanity UI issues
export const suppressReactWarnings = () => {
  if (typeof window !== 'undefined') {
    const originalError = console.error;
    console.error = (...args) => {
      const message = args[0];
      
      // Suppress disableTransition prop warning
      if (
        typeof message === 'string' &&
        message.includes('disableTransition') &&
        message.includes('React does not recognize')
      ) {
        return;
      }
      
      // Suppress other known Sanity UI warnings
      if (
        typeof message === 'string' &&
        (
          message.includes('React does not recognize') ||
          message.includes('styled-components')
        )
      ) {
        return;
      }
      
      originalError.apply(console, args);
    };
  }
};

// Call this in your app initialization
if (process.env.NODE_ENV === 'development') {
  suppressReactWarnings();
}
