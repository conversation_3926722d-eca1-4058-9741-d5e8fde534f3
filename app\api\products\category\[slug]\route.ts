import { NextRequest, NextResponse } from 'next/server';
import { client } from '@/sanity/lib/client';
import { Product } from '@/types/sanity';

const PRODUCTS_BY_CATEGORY_QUERY = `
  *[_type == "product" && category->slug.current == $categorySlug && defined(name.tr)] | order(name.tr asc) {
    _id,
    _type,
    _createdAt,
    _updatedAt,
    _rev,
    id,
    name,
    slug,
    image {
      asset->{
        _id,
        url
      },
      alt,
      hotspot,
      crop
    },
    description,
    category->{
      _id,
      title,
      slug
    },
    tags,
    price,
    currency,
    discount,
    sku,
    stock,
    isAvailable,
    variants,
    flags,
    seoTitle,
    seoDescription
  }
`;

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug: categorySlug } = params;

    if (!categorySlug) {
      return NextResponse.json(
        { 
          error: { message: 'Category slug is required' },
          success: false 
        },
        { status: 400 }
      );
    }

    const products = await client.fetch<Product[]>(PRODUCTS_BY_CATEGORY_QUERY, { categorySlug });

    return NextResponse.json({
      data: products,
      total: products.length,
      success: true,
    });

  } catch (error) {
    console.error('Error fetching products by category:', error);
    return NextResponse.json(
      { 
        error: { 
          message: 'Failed to fetch products by category',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        success: false 
      },
      { status: 500 }
    );
  }
}
