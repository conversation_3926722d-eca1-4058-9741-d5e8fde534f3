// Complete integration example showing all new features
import React, { useState } from 'react';
import { 
  useOrders, 
  useOrdersByStatus,
  getOrderStatusColor,
  formatOrderNumber,
  calculateOrderTotal 
} from '../hooks/useOrders';
import { 
  useUsers, 
  usePendingUsers,
  getUserStatusColor,
  formatWalletBalance,
  getLocalizedUserName 
} from '../hooks/useUsers';
import { 
  useSales, 
  useActiveSales,
  useCouponValidation,
  isSaleActive,
  formatDiscountAmount 
} from '../hooks/useSales';
import { 
  useWalletTransactions, 
  usePendingTransactions,
  getTransactionTypeColor,
  formatTransactionAmount 
} from '../hooks/useWalletTransactions';

// Admin Dashboard Component
export const AdminDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'orders' | 'users' | 'sales' | 'wallet'>('orders');

  return (
    <div className="admin-dashboard">
      <div className="dashboard-header">
        <h1>Admin Dashboard</h1>
        <div className="tab-navigation">
          {['orders', 'users', 'sales', 'wallet'].map(tab => (
            <button
              key={tab}
              className={`tab ${activeTab === tab ? 'active' : ''}`}
              onClick={() => setActiveTab(tab as any)}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </button>
          ))}
        </div>
      </div>

      <div className="dashboard-content">
        {activeTab === 'orders' && <OrdersManagement />}
        {activeTab === 'users' && <UsersManagement />}
        {activeTab === 'sales' && <SalesManagement />}
        {activeTab === 'wallet' && <WalletManagement />}
      </div>
    </div>
  );
};

// Orders Management Component
const OrdersManagement: React.FC = () => {
  const { data: allOrders, isLoading: allOrdersLoading } = useOrders();
  const { data: pendingOrders } = useOrdersByStatus('pending');
  const { data: shippedOrders } = useOrdersByStatus('shipped');

  if (allOrdersLoading) return <div>Siparişler yükleniyor...</div>;

  return (
    <div className="orders-management">
      <div className="stats-grid">
        <div className="stat-card">
          <h3>Toplam Sipariş</h3>
          <p className="stat-number">{allOrders?.length || 0}</p>
        </div>
        <div className="stat-card">
          <h3>Bekleyen Siparişler</h3>
          <p className="stat-number">{pendingOrders?.length || 0}</p>
        </div>
        <div className="stat-card">
          <h3>Kargodaki Siparişler</h3>
          <p className="stat-number">{shippedOrders?.length || 0}</p>
        </div>
      </div>

      <div className="orders-table">
        <h3>Son Siparişler</h3>
        <table>
          <thead>
            <tr>
              <th>Sipariş No</th>
              <th>Müşteri</th>
              <th>Tutar</th>
              <th>Durum</th>
              <th>Tarih</th>
              <th>İşlemler</th>
            </tr>
          </thead>
          <tbody>
            {allOrders?.slice(0, 10).map(order => (
              <tr key={order._id}>
                <td>{formatOrderNumber(order.orderNumber)}</td>
                <td>{order.customer.name}</td>
                <td>{order.totalAmount} {order.currency}</td>
                <td>
                  <span 
                    className="status-badge"
                    style={{ backgroundColor: getOrderStatusColor(order.orderStatus) }}
                  >
                    {order.orderStatus}
                  </span>
                </td>
                <td>{new Date(order.createdAt).toLocaleDateString('tr-TR')}</td>
                <td>
                  <button className="btn-small">Detay</button>
                  <button className="btn-small">Düzenle</button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

// Users Management Component
const UsersManagement: React.FC = () => {
  const { data: allUsers, isLoading: usersLoading } = useUsers();
  const { data: pendingUsers } = usePendingUsers();

  if (usersLoading) return <div>Kullanıcılar yükleniyor...</div>;

  return (
    <div className="users-management">
      <div className="stats-grid">
        <div className="stat-card">
          <h3>Toplam Kullanıcı</h3>
          <p className="stat-number">{allUsers?.length || 0}</p>
        </div>
        <div className="stat-card">
          <h3>Onay Bekleyen</h3>
          <p className="stat-number">{pendingUsers?.length || 0}</p>
        </div>
        <div className="stat-card">
          <h3>Aktif Kullanıcı</h3>
          <p className="stat-number">
            {allUsers?.filter(user => user.status === 'active').length || 0}
          </p>
        </div>
      </div>

      <div className="users-table">
        <h3>Kullanıcı Listesi</h3>
        <table>
          <thead>
            <tr>
              <th>Ad Soyad</th>
              <th>E-posta</th>
              <th>Cüzdan Bakiyesi</th>
              <th>Durum</th>
              <th>Rol</th>
              <th>İşlemler</th>
            </tr>
          </thead>
          <tbody>
            {allUsers?.slice(0, 10).map(user => (
              <tr key={user._id}>
                <td>{getLocalizedUserName(user.name)}</td>
                <td>{user.email}</td>
                <td>{formatWalletBalance(user.walletBalance, user.currency)}</td>
                <td>
                  <span 
                    className="status-badge"
                    style={{ backgroundColor: getUserStatusColor(user.status) }}
                  >
                    {user.status}
                  </span>
                </td>
                <td>{user.role}</td>
                <td>
                  <button className="btn-small">Detay</button>
                  <button className="btn-small">Düzenle</button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

// Sales Management Component
const SalesManagement: React.FC = () => {
  const { data: allSales, isLoading: salesLoading } = useSales();
  const { data: activeSales } = useActiveSales();

  if (salesLoading) return <div>Kampanyalar yükleniyor...</div>;

  return (
    <div className="sales-management">
      <div className="stats-grid">
        <div className="stat-card">
          <h3>Toplam Kampanya</h3>
          <p className="stat-number">{allSales?.length || 0}</p>
        </div>
        <div className="stat-card">
          <h3>Aktif Kampanya</h3>
          <p className="stat-number">{activeSales?.length || 0}</p>
        </div>
        <div className="stat-card">
          <h3>Süresi Dolan</h3>
          <p className="stat-number">
            {allSales?.filter(sale => !isSaleActive(sale)).length || 0}
          </p>
        </div>
      </div>

      <div className="sales-table">
        <h3>Kampanya Listesi</h3>
        <table>
          <thead>
            <tr>
              <th>Kampanya Adı</th>
              <th>Kupon Kodu</th>
              <th>İndirim</th>
              <th>Kullanım</th>
              <th>Durum</th>
              <th>İşlemler</th>
            </tr>
          </thead>
          <tbody>
            {allSales?.slice(0, 10).map(sale => (
              <tr key={sale._id}>
                <td>{sale.title.tr}</td>
                <td>{sale.couponCode}</td>
                <td>{formatDiscountAmount(sale)}</td>
                <td>
                  {sale.usedCount} / {sale.usageLimit || '∞'}
                </td>
                <td>
                  <span 
                    className={`status-badge ${isSaleActive(sale) ? 'active' : 'inactive'}`}
                  >
                    {isSaleActive(sale) ? 'Aktif' : 'Pasif'}
                  </span>
                </td>
                <td>
                  <button className="btn-small">Detay</button>
                  <button className="btn-small">Düzenle</button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

// Wallet Management Component
const WalletManagement: React.FC = () => {
  const { data: allTransactions, isLoading: transactionsLoading } = useWalletTransactions();
  const { data: pendingTransactions } = usePendingTransactions();

  if (transactionsLoading) return <div>Cüzdan işlemleri yükleniyor...</div>;

  const totalDeposits = allTransactions?.filter(t => t.type === 'deposit').reduce((sum, t) => sum + t.amount, 0) || 0;
  const totalWithdrawals = allTransactions?.filter(t => t.type === 'withdrawal').reduce((sum, t) => sum + t.amount, 0) || 0;

  return (
    <div className="wallet-management">
      <div className="stats-grid">
        <div className="stat-card">
          <h3>Toplam Yükleme</h3>
          <p className="stat-number">{totalDeposits} ₺</p>
        </div>
        <div className="stat-card">
          <h3>Toplam Harcama</h3>
          <p className="stat-number">{totalWithdrawals} ₺</p>
        </div>
        <div className="stat-card">
          <h3>Bekleyen İşlem</h3>
          <p className="stat-number">{pendingTransactions?.length || 0}</p>
        </div>
      </div>

      <div className="transactions-table">
        <h3>Son İşlemler</h3>
        <table>
          <thead>
            <tr>
              <th>İşlem ID</th>
              <th>Kullanıcı</th>
              <th>Tutar</th>
              <th>Tür</th>
              <th>Durum</th>
              <th>Tarih</th>
              <th>İşlemler</th>
            </tr>
          </thead>
          <tbody>
            {allTransactions?.slice(0, 10).map(transaction => (
              <tr key={transaction._id}>
                <td>{transaction.transactionId}</td>
                <td>{transaction.user ? 'User' : 'N/A'}</td>
                <td>{formatTransactionAmount(transaction)}</td>
                <td>
                  <span 
                    className="type-badge"
                    style={{ backgroundColor: getTransactionTypeColor(transaction.type) }}
                  >
                    {transaction.type}
                  </span>
                </td>
                <td>{transaction.status}</td>
                <td>{new Date(transaction.createdAt).toLocaleDateString('tr-TR')}</td>
                <td>
                  <button className="btn-small">Detay</button>
                  {transaction.status === 'pending' && (
                    <>
                      <button className="btn-small btn-success">Onayla</button>
                      <button className="btn-small btn-danger">Reddet</button>
                    </>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

// Coupon Validation Component
export const CouponValidator: React.FC = () => {
  const [couponCode, setCouponCode] = useState('');
  const [userId, setUserId] = useState('');
  
  const { data: validation, isLoading } = useCouponValidation(couponCode, userId);

  return (
    <div className="coupon-validator">
      <h3>Kupon Kodu Doğrulama</h3>
      <div className="form-group">
        <input
          type="text"
          placeholder="Kupon kodu"
          value={couponCode}
          onChange={(e) => setCouponCode(e.target.value)}
        />
        <input
          type="text"
          placeholder="Kullanıcı ID (opsiyonel)"
          value={userId}
          onChange={(e) => setUserId(e.target.value)}
        />
      </div>
      
      {isLoading && <div>Doğrulanıyor...</div>}
      
      {validation && (
        <div className={`validation-result ${validation.valid ? 'valid' : 'invalid'}`}>
          {validation.valid ? (
            <div>
              <p>✅ Kupon geçerli!</p>
              {validation.sale && (
                <div>
                  <p>Kampanya: {validation.sale.title.tr}</p>
                  <p>İndirim: {formatDiscountAmount(validation.sale)}</p>
                </div>
              )}
            </div>
          ) : (
            <div>
              <p>❌ Kupon geçersiz</p>
              {validation.message && <p>{validation.message}</p>}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AdminDashboard;
