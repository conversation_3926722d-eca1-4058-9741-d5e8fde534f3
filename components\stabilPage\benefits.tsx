"use client"
import Link from "next/link";

const benefits = [
    {
      title: "<PERSON><PERSON><PERSON><PERSON> Alışveriş",
      description: "En son güvenlik teknolojileriyle korunan ödeme sistemi.",
      icon: (props: any) => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-8 h-8">
          <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z" />
        </svg>
      )
    },
    {
      title: "<PERSON><PERSON><PERSON><PERSON><PERSON> Teslimat",
      description: "Siparişleriniz en hızlı şekilde kapınıza teslim edilir.",
      icon: (props: any) => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-8 h-8">
          <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12" />
        </svg>
      )
    },
    {
      title: "7/24 Destek",
      description: "Müşteri hizmetlerimiz her zaman yanınızda.",
      icon: (props: any) => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-8 h-8">
          <path strokeLinecap="round" strokeLinejoin="round" d="M15.182 15.182a4.5 4.5 0 0 1-6.364 0M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0ZM9.75 9.75c0 .414-.168.75-.375.75S9 10.164 9 9.75 9.168 9 9.375 9s.375.336.375.75Zm-.375 0h.008v.015h-.008V9.75Zm5.625 0c0 .414-.168.75-.375.75s-.375-.336-.375-.75.168-.75.375-.75.375.336.375.75Zm-.375 0h.008v.015h-.008V9.75Z" />
        </svg>
      )
    }
  ];

export const Benefits = () => {
  return (
    <section className="py-12">
      <h2 className="text-3xl font-bold mb-12 text-center text-gray-800 dark:text-white">
      <Link
          href="/"
          className="relative flex items-center justify-center-safe
        rounded-lg shadow-2xl bg-gradient-to-br from-blue-400 to-red-600  "
        >
          Trendfy_X
        </Link>
        </h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {benefits.map((benefit, index) => (
          <div key={index} className="flex flex-col items-center text-center">
            <div className="w-16 h-16 flex items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900 mb-4">
              <benefit.icon className="text-blue-600 dark:text-blue-300" />
            </div>
            <h3 className="text-xl font-semibold mb-2 text-gray-800 dark:text-white">{benefit.title}</h3>
            <p className="text-gray-600 dark:text-gray-300">{benefit.description}</p>
          </div>
        ))}
      </div>
    </section>
  );
}