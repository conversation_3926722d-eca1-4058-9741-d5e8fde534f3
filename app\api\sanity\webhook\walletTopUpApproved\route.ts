import { NextRequest } from 'next/server';
import { client } from '@/sanity/lib/client';

export async function POST(req: NextRequest) {
  const body = await req.json();
  // Sanity webhook body: { _id, user: {_ref}, amount, status }
  const { _id, user, amount, status } = body;
  if (!user?._ref || !amount || status !== 'approved') {
    return new Response(JSON.stringify({ error: 'Eksik veya onaylanmamış talep.' }), { status: 400 });
  }
  try {
    // Kullanıcıyı çek
    const userDoc = await client.fetch(`*[_type == "user" && _id == $userId][0]`, { userId: user._ref });
    if (!userDoc) {
      return new Response(JSON.stringify({ error: 'Kullanıcı bulunamadı.' }), { status: 404 });
    }
    // Bakiyeyi artır
    const newBalance = (userDoc.walletBalance || 0) + amount;
    await client.patch(user._ref).set({ walletBalance: newBalance }).commit();
    return new Response(JSON.stringify({ success: true, newBalance }), { status: 200 });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Bakiye güncellenemedi.' }), { status: 500 });
  }
} 