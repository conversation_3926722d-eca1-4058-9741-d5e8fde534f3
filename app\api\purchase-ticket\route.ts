import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import sanityClient from '@sanity/client';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY! as string, {
  apiVersion: '2022-11-15',
});

const client = sanityClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET!,
  apiVersion: '2023-06-10',
  token: process.env.SANITY_WRITE_TOKEN!,
  useCdn: false,
});

// Bu fonksiyon, bir bilet için benzersiz numaralar üretir
// Yeni implementasyonda, bu fonksiyon global benzersizliği sağlamayacak,
// sadece tek bir bilet içindeki sayıların benzersizliğini garanti edecek.
// Global benzersizlik, generateUniqueDrawNumbers fonksiyonu ile sağlanacak.
function generateUniqueTicketNumbers(count: number, digits: number): number[] {
  const min = Math.pow(10, digits - 1);
  const max = Math.pow(10, digits) - 1;
  const numbers = new Set<number>();
  while (numbers.size < count) {
    const n = Math.floor(Math.random() * (max - min + 1)) + min;
    numbers.add(n);
  }
  return Array.from(numbers);
}

// Bu fonksiyon, çekiliş için global olarak benzersiz numaralar üretmeye çalışır
async function generateUniqueDrawNumbers(drawId: string, count: number, digits: number): Promise<number[] | null> {
  const maxAttempts = 100; // Maksimum deneme sayısı
  const minVal = Math.pow(10, digits - 1);
  const maxVal = Math.pow(10, digits) - 1;

  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    // Öncelikle çekilişin mevcut kullanılan numaralarını al
    const currentDraw = await client.fetch(
      `*[_type == "draw" && _id == $drawId][0]{usedNumbers}`,
      { drawId }
    );
    const usedNumbersInDraw = new Set<number>(currentDraw?.usedNumbers || []);

    const candidateNumbers: number[] = [];
    let generatedCount = 0;
    let localAttempts = 0;
    const maxLocalAttempts = 1000; // Her bir numara için deneme sayısı

    while (generatedCount < count && localAttempts < maxLocalAttempts) {
      const newNum = Math.floor(Math.random() * (maxVal - minVal + 1)) + minVal;
      if (!usedNumbersInDraw.has(newNum)) {
        candidateNumbers.push(newNum);
        usedNumbersInDraw.add(newNum); // Aday numarayı kullanılanlara ekle (bu deneme için)
        generatedCount++;
      }
      localAttempts++;
    }

    if (generatedCount === count) {
      return candidateNumbers;
    }
  }
  return null; // Yeterli benzersiz numara bulunamadı
}

export async function POST(req: NextRequest) {
  try {
    const { userId, drawId } = await req.json();

    if (!userId || !drawId) {
      return NextResponse.json({ error: 'Eksik parametreler (userId veya drawId)' }, { status: 400 });
    }

    // 1. Sanity'den çekiliş bilgilerini getir
    const draw = await client.fetch(
      `*[_type == "draw" && _id == $drawId][0]`,
      { drawId }
    );

    if (!draw) {
      return NextResponse.json({ error: 'Çekiliş bulunamadı.' }, { status: 404 });
    }

    const { ticketDigitCount, ticketNumberCount, ticketPrice } = draw;

    // 2. Rastgele benzersiz numaralar üret (çekiliş genelinde benzersiz)
    const numbers = await generateUniqueDrawNumbers(drawId, ticketNumberCount, ticketDigitCount);

    if (!numbers) {
      return NextResponse.json({ error: 'Çekiliş için yeterli benzersiz numara bulunamadı. Lütfen daha sonra tekrar deneyin veya başka bir çekiliş seçin.' }, { status: 500 });
    }

    // 3. Stripe Ödeme Intent oluştur
    const paymentIntent = await stripe.paymentIntents.create({
      amount: ticketPrice * 100, // TL olarak tutulacak ve kuruşa çevrilecek
      currency: 'try',
      payment_method_types: ['card'],
      metadata: {
        userId,
        drawId,
        numbers: numbers.join(','),
      },
    });

    return NextResponse.json({
      clientSecret: paymentIntent.client_secret,
      numbers,
      drawId,
      price: ticketPrice,
      ticketDigitCount,
      ticketNumberCount,
    }, { status: 200 });
  } catch (error) {
    console.error('Bilet satın alma API hatası:', error);
    return NextResponse.json({ error: 'Sunucu hatası' }, { status: 500 });
  }
} 