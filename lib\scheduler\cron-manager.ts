// Cron Jobs and Scheduled Tasks Manager
import cron from 'node-cron';
import { client } from '@/sanity/lib/client';
import { updateAuctionStatus } from '@/sanity/lib/actions/auctionActions';
import { updateGiveawayStatus, conductGiveawayDraw } from '@/sanity/lib/actions/giveawayActions';
import { updateSaleStatus } from '@/sanity/lib/actions/walletActions';
import { ErrorTracker } from '../sentry/config';
import { WebhookManager, WebhookEvents } from '../webhooks/manager';
import { FCMNotificationSender, NotificationTemplates } from '../firebase/fcm';

// Task status interface
interface ScheduledTask {
  id: string;
  name: string;
  schedule: string;
  isActive: boolean;
  lastRun?: string;
  nextRun?: string;
  runCount: number;
  errorCount: number;
  lastError?: string;
}

// Cron Manager class
export class CronManager {
  private static instance: CronManager;
  private tasks: Map<string, cron.ScheduledTask> = new Map();
  private taskInfo: Map<string, ScheduledTask> = new Map();
  private webhookManager: WebhookManager;
  private notificationSender: FCMNotificationSender;

  private constructor() {
    this.webhookManager = WebhookManager.getInstance();
    this.notificationSender = FCMNotificationSender.getInstance();
  }

  static getInstance(): CronManager {
    if (!CronManager.instance) {
      CronManager.instance = new CronManager();
    }
    return CronManager.instance;
  }

  // Initialize all scheduled tasks
  async initialize(): Promise<void> {
    console.log('🕐 Initializing scheduled tasks...');

    // Auction status updates - every minute
    this.scheduleTask(
      'auction-status-update',
      '* * * * *',
      this.updateAuctionStatuses.bind(this),
      'Update auction statuses based on time'
    );

    // Giveaway status updates - every minute
    this.scheduleTask(
      'giveaway-status-update',
      '* * * * *',
      this.updateGiveawayStatuses.bind(this),
      'Update giveaway statuses and conduct draws'
    );

    // Sales/Campaign status updates - every 5 minutes
    this.scheduleTask(
      'sales-status-update',
      '*/5 * * * *',
      this.updateSalesStatuses.bind(this),
      'Update sales campaign statuses'
    );

    // Cleanup expired notifications - daily at 2 AM
    this.scheduleTask(
      'cleanup-notifications',
      '0 2 * * *',
      this.cleanupExpiredNotifications.bind(this),
      'Clean up expired notifications'
    );

    // Generate daily reports - daily at 6 AM
    this.scheduleTask(
      'daily-reports',
      '0 6 * * *',
      this.generateDailyReports.bind(this),
      'Generate daily analytics reports'
    );

    // Backup critical data - daily at 3 AM
    this.scheduleTask(
      'data-backup',
      '0 3 * * *',
      this.backupCriticalData.bind(this),
      'Backup critical application data'
    );

    // Health check - every 15 minutes
    this.scheduleTask(
      'health-check',
      '*/15 * * * *',
      this.performHealthCheck.bind(this),
      'Perform system health checks'
    );

    console.log(`✅ Initialized ${this.tasks.size} scheduled tasks`);
  }

  // Schedule a new task
  private scheduleTask(
    id: string,
    schedule: string,
    handler: () => Promise<void>,
    description: string
  ): void {
    const taskInfo: ScheduledTask = {
      id,
      name: description,
      schedule,
      isActive: true,
      runCount: 0,
      errorCount: 0,
    };

    const task = cron.schedule(schedule, async () => {
      await this.executeTask(id, handler);
    }, {
      scheduled: false,
      timezone: 'Europe/Istanbul',
    });

    this.tasks.set(id, task);
    this.taskInfo.set(id, taskInfo);

    task.start();
    console.log(`📅 Scheduled task: ${id} (${schedule})`);
  }

  // Execute task with error handling and logging
  private async executeTask(taskId: string, handler: () => Promise<void>): Promise<void> {
    const taskInfo = this.taskInfo.get(taskId);
    if (!taskInfo || !taskInfo.isActive) return;

    const startTime = Date.now();
    
    try {
      console.log(`🔄 Running task: ${taskId}`);
      
      await handler();
      
      taskInfo.runCount++;
      taskInfo.lastRun = new Date().toISOString();
      taskInfo.lastError = undefined;
      
      const duration = Date.now() - startTime;
      console.log(`✅ Task completed: ${taskId} (${duration}ms)`);
      
    } catch (error) {
      taskInfo.errorCount++;
      taskInfo.lastError = error instanceof Error ? error.message : 'Unknown error';
      
      console.error(`❌ Task failed: ${taskId}`, error);
      
      ErrorTracker.captureException(error as Error, {
        scheduled_task: {
          id: taskId,
          name: taskInfo.name,
          schedule: taskInfo.schedule,
          runCount: taskInfo.runCount,
          errorCount: taskInfo.errorCount,
        },
      });
    }
    
    this.taskInfo.set(taskId, taskInfo);
  }

  // Update auction statuses
  private async updateAuctionStatuses(): Promise<void> {
    const auctions = await client.fetch(`
      *[_type == "auction" && status in ["upcoming", "active"]] {
        _id, status, startTime, endTime
      }
    `);

    for (const auction of auctions) {
      try {
        const result = await updateAuctionStatus(auction._id);
        
        if (result.success && result.newStatus !== auction.status) {
          console.log(`🔨 Auction ${auction._id} status updated: ${auction.status} → ${result.newStatus}`);
          
          // Send webhook event
          if (result.newStatus === 'active') {
            await this.webhookManager.sendEvent('auction.started', WebhookEvents.auction.started(auction));
          } else if (result.newStatus === 'completed') {
            await this.webhookManager.sendEvent('auction.completed', WebhookEvents.auction.completed(auction));
          }
        }
      } catch (error) {
        console.error(`Failed to update auction ${auction._id}:`, error);
      }
    }
  }

  // Update giveaway statuses
  private async updateGiveawayStatuses(): Promise<void> {
    const giveaways = await client.fetch(`
      *[_type == "giveaway" && status in ["upcoming", "active", "draw_ready"]] {
        _id, status, startDate, endDate, drawDate, ticketsSold, totalTickets, ticketSalePercentageForDraw
      }
    `);

    for (const giveaway of giveaways) {
      try {
        const result = await updateGiveawayStatus(giveaway._id);
        
        if (result.success && result.newStatus !== giveaway.status) {
          console.log(`🎁 Giveaway ${giveaway._id} status updated: ${giveaway.status} → ${result.newStatus}`);
          
          // Send webhook events
          if (result.newStatus === 'active') {
            await this.webhookManager.sendEvent('giveaway.started', WebhookEvents.giveaway.started(giveaway));
          } else if (result.newStatus === 'draw_ready') {
            await this.webhookManager.sendEvent('giveaway.draw_ready', WebhookEvents.giveaway.drawReady(giveaway));
          } else if (result.newStatus === 'completed') {
            await this.webhookManager.sendEvent('giveaway.completed', WebhookEvents.giveaway.completed(giveaway, []));
          }
        }

        // Auto-conduct draw if ready
        if (giveaway.status === 'draw_ready' && giveaway.drawDate) {
          const drawDate = new Date(giveaway.drawDate);
          const now = new Date();
          
          if (now >= drawDate) {
            console.log(`🎲 Conducting draw for giveaway ${giveaway._id}`);
            const drawResult = await conductGiveawayDraw(giveaway._id);
            
            if (drawResult.success && drawResult.winners) {
              await this.webhookManager.sendEvent(
                'giveaway.completed', 
                WebhookEvents.giveaway.completed(giveaway, drawResult.winners)
              );
            }
          }
        }
      } catch (error) {
        console.error(`Failed to update giveaway ${giveaway._id}:`, error);
      }
    }
  }

  // Update sales/campaign statuses
  private async updateSalesStatuses(): Promise<void> {
    const sales = await client.fetch(`
      *[_type == "sales" && isActive == true] {
        _id, validityUntil
      }
    `);

    for (const sale of sales) {
      try {
        const result = await updateSaleStatus(sale._id);
        
        if (result.success && result.newStatus === false) {
          console.log(`💰 Sale ${sale._id} expired and deactivated`);
        }
      } catch (error) {
        console.error(`Failed to update sale ${sale._id}:`, error);
      }
    }
  }

  // Cleanup expired notifications
  private async cleanupExpiredNotifications(): Promise<void> {
    const expiredNotifications = await client.fetch(`
      *[_type == "notification" && defined(expiresAt) && expiresAt < now()] {
        _id
      }
    `);

    for (const notification of expiredNotifications) {
      try {
        await client
          .patch(notification._id)
          .set({ isActive: false })
          .commit();
      } catch (error) {
        console.error(`Failed to deactivate notification ${notification._id}:`, error);
      }
    }

    console.log(`🧹 Deactivated ${expiredNotifications.length} expired notifications`);
  }

  // Generate daily reports
  private async generateDailyReports(): Promise<void> {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const startOfDay = yesterday.toISOString().split('T')[0] + 'T00:00:00.000Z';
    const endOfDay = yesterday.toISOString().split('T')[0] + 'T23:59:59.999Z';

    try {
      // Generate auction report
      const auctionStats = await client.fetch(`
        {
          "total": count(*[_type == "auction" && _createdAt >= $start && _createdAt <= $end]),
          "completed": count(*[_type == "auction" && _createdAt >= $start && _createdAt <= $end && status == "completed"]),
          "totalBids": count(*[_type == "auction" && _createdAt >= $start && _createdAt <= $end].bidHistory[])
        }
      `, { start: startOfDay, end: endOfDay });

      // Generate giveaway report
      const giveawayStats = await client.fetch(`
        {
          "total": count(*[_type == "giveaway" && _createdAt >= $start && _createdAt <= $end]),
          "completed": count(*[_type == "giveaway" && _createdAt >= $start && _createdAt <= $end && status == "completed"]),
          "totalTickets": sum(*[_type == "giveaway" && _createdAt >= $start && _createdAt <= $end].ticketsSold)
        }
      `, { start: startOfDay, end: endOfDay });

      // Generate order report
      const orderStats = await client.fetch(`
        {
          "total": count(*[_type == "order" && _createdAt >= $start && _createdAt <= $end]),
          "totalAmount": sum(*[_type == "order" && _createdAt >= $start && _createdAt <= $end].totalAmount)
        }
      `, { start: startOfDay, end: endOfDay });

      console.log('📊 Daily Report Generated:', {
        date: yesterday.toISOString().split('T')[0],
        auctions: auctionStats,
        giveaways: giveawayStats,
        orders: orderStats,
      });

      // Store report in database or send to analytics service
      // Implementation depends on your analytics storage solution

    } catch (error) {
      console.error('Failed to generate daily reports:', error);
    }
  }

  // Backup critical data
  private async backupCriticalData(): Promise<void> {
    try {
      // Backup active auctions
      const activeAuctions = await client.fetch(`
        *[_type == "auction" && status in ["upcoming", "active"]]
      `);

      // Backup active giveaways
      const activeGiveaways = await client.fetch(`
        *[_type == "giveaway" && status in ["upcoming", "active", "draw_ready"]]
      `);

      // Backup recent orders
      const recentOrders = await client.fetch(`
        *[_type == "order" && _createdAt > dateTime(now()) - 86400]
      `);

      console.log(`💾 Backup completed: ${activeAuctions.length} auctions, ${activeGiveaways.length} giveaways, ${recentOrders.length} orders`);

      // Store backups (implement based on your backup solution)
      // Could be AWS S3, Google Cloud Storage, etc.

    } catch (error) {
      console.error('Failed to backup critical data:', error);
    }
  }

  // Perform health check
  private async performHealthCheck(): Promise<void> {
    const healthStatus = {
      sanity: false,
      database: false,
      webhooks: false,
      notifications: false,
      timestamp: new Date().toISOString(),
    };

    try {
      // Check Sanity connection
      await client.fetch('*[_type == "product"][0]');
      healthStatus.sanity = true;
    } catch (error) {
      console.error('Sanity health check failed:', error);
    }

    try {
      // Check webhook system
      const endpointCount = this.webhookManager ? 1 : 0;
      healthStatus.webhooks = endpointCount >= 0;
    } catch (error) {
      console.error('Webhook health check failed:', error);
    }

    try {
      // Check notification system
      healthStatus.notifications = !!this.notificationSender;
    } catch (error) {
      console.error('Notification health check failed:', error);
    }

    const allHealthy = Object.values(healthStatus).every(status => status === true || typeof status === 'string');
    
    if (!allHealthy) {
      console.warn('⚠️ Health check failed:', healthStatus);
      ErrorTracker.captureMessage('System health check failed', 'warning', { healthStatus });
    }
  }

  // Get task information
  getTaskInfo(): ScheduledTask[] {
    return Array.from(this.taskInfo.values());
  }

  // Stop a specific task
  stopTask(taskId: string): boolean {
    const task = this.tasks.get(taskId);
    const taskInfo = this.taskInfo.get(taskId);
    
    if (task && taskInfo) {
      task.stop();
      taskInfo.isActive = false;
      this.taskInfo.set(taskId, taskInfo);
      console.log(`⏹️ Stopped task: ${taskId}`);
      return true;
    }
    
    return false;
  }

  // Start a specific task
  startTask(taskId: string): boolean {
    const task = this.tasks.get(taskId);
    const taskInfo = this.taskInfo.get(taskId);
    
    if (task && taskInfo) {
      task.start();
      taskInfo.isActive = true;
      this.taskInfo.set(taskId, taskInfo);
      console.log(`▶️ Started task: ${taskId}`);
      return true;
    }
    
    return false;
  }

  // Stop all tasks
  stopAll(): void {
    this.tasks.forEach((task, taskId) => {
      task.stop();
      const taskInfo = this.taskInfo.get(taskId);
      if (taskInfo) {
        taskInfo.isActive = false;
        this.taskInfo.set(taskId, taskInfo);
      }
    });
    console.log('⏹️ All scheduled tasks stopped');
  }
}

export default CronManager;
