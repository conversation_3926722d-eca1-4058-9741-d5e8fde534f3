// Sanity Actions for Order Management
import { SanityDocument } from 'sanity';
import { client } from '../client';

// Calculate total amount for order
export const calculateTotalAmount = (orderItems: any[]): number => {
  return orderItems.reduce((total, item) => {
    const itemTotal = item.price * item.quantity;
    const discountAmount = item.discount ? (itemTotal * item.discount) / 100 : 0;
    return total + (itemTotal - discountAmount);
  }, 0);
};

// Validate stock availability for order items
export const validateStockAvailability = async (orderItems: any[]): Promise<{ valid: boolean; errors: string[] }> => {
  const errors: string[] = [];
  
  for (const item of orderItems) {
    if (item.product && item.product._ref && item.quantity) {
      try {
        const product = await client.fetch(
          `*[_id == $id][0]{name, stock, isAvailable, variants}`,
          { id: item.product._ref }
        );
        
        if (!product) {
          errors.push(`Ürün bulunamadı: ${item.product._ref}`);
          continue;
        }
        
        if (!product.isAvailable) {
          errors.push(`Ürün stokta mevcut değil: ${product.name?.tr || product.name}`);
          continue;
        }
        
        // Check main stock
        if (product.stock < item.quantity) {
          errors.push(`${product.name?.tr || product.name} için yeterli stok yok. Mevcut: ${product.stock}, İstenen: ${item.quantity}`);
          continue;
        }
        
        // Check variant stock if applicable
        if (item.variant && product.variants) {
          const variant = product.variants.find((v: any) => v.name === item.variant.name && v.value === item.variant.value);
          if (variant && variant.stock < item.quantity) {
            errors.push(`${product.name?.tr || product.name} (${variant.name}: ${variant.value}) için yeterli stok yok. Mevcut: ${variant.stock}, İstenen: ${item.quantity}`);
          }
        }
      } catch (error) {
        console.error('Stok kontrolü hatası:', error);
        errors.push('Stok kontrolü yapılamadı. Lütfen tekrar deneyin.');
      }
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
};

// Update product stock after order
export const updateProductStock = async (orderItems: any[], operation: 'decrease' | 'increase' = 'decrease'): Promise<void> => {
  for (const item of orderItems) {
    if (item.product && item.product._ref && item.quantity) {
      try {
        const product = await client.fetch(
          `*[_id == $id][0]{stock, variants}`,
          { id: item.product._ref }
        );
        
        if (product) {
          const stockChange = operation === 'decrease' ? -item.quantity : item.quantity;
          const newStock = Math.max(0, product.stock + stockChange);
          
          // Update main stock
          await client
            .patch(item.product._ref)
            .set({ stock: newStock })
            .commit();
          
          // Update variant stock if applicable
          if (item.variant && product.variants) {
            const variantIndex = product.variants.findIndex((v: any) => 
              v.name === item.variant.name && v.value === item.variant.value
            );
            
            if (variantIndex !== -1) {
              const updatedVariants = [...product.variants];
              updatedVariants[variantIndex].stock = Math.max(0, updatedVariants[variantIndex].stock + stockChange);
              
              await client
                .patch(item.product._ref)
                .set({ variants: updatedVariants })
                .commit();
            }
          }
        }
      } catch (error) {
        console.error('Stok güncelleme hatası:', error);
      }
    }
  }
};

// Apply campaign discount
export const applyCampaignDiscount = async (orderItems: any[], campaignCode?: string): Promise<{ discountAmount: number; campaignDetails?: any }> => {
  if (!campaignCode) {
    return { discountAmount: 0 };
  }
  
  try {
    const campaign = await client.fetch(
      `*[_type == "sales" && couponCode == $code && isActive == true && validityFrom <= now() && validityUntil >= now()][0]`,
      { code: campaignCode }
    );
    
    if (!campaign) {
      throw new Error('Geçersiz veya süresi dolmuş kampanya kodu');
    }
    
    // Check usage limit
    if (campaign.usageLimit && campaign.usedCount >= campaign.usageLimit) {
      throw new Error('Kampanya kullanım limiti dolmuş');
    }
    
    // Calculate discount
    const subtotal = calculateTotalAmount(orderItems);
    let discountAmount = 0;
    
    if (campaign.discountType === 'percentage') {
      discountAmount = (subtotal * campaign.discountAmount) / 100;
    } else if (campaign.discountType === 'fixed_amount') {
      discountAmount = Math.min(campaign.discountAmount, subtotal);
    }
    
    return {
      discountAmount,
      campaignDetails: campaign
    };
  } catch (error) {
    console.error('Kampanya uygulama hatası:', error);
    return { discountAmount: 0 };
  }
};

// Increment campaign usage count
export const incrementCampaignUsage = async (campaignCode: string): Promise<void> => {
  try {
    await client
      .patch({ query: `*[_type == "sales" && couponCode == $code][0]`, params: { code: campaignCode } })
      .inc({ usedCount: 1 })
      .commit();
  } catch (error) {
    console.error('Kampanya kullanım sayısı artırma hatası:', error);
  }
};

// Create order with automatic calculations
export const createOrderWithCalculations = async (orderData: any): Promise<any> => {
  try {
    // Validate stock
    const stockValidation = await validateStockAvailability(orderData.orderItems);
    if (!stockValidation.valid) {
      throw new Error(`Stok hatası: ${stockValidation.errors.join(', ')}`);
    }
    
    // Calculate subtotal
    const subtotal = calculateTotalAmount(orderData.orderItems);
    
    // Apply campaign discount
    const { discountAmount, campaignDetails } = await applyCampaignDiscount(
      orderData.orderItems, 
      orderData.campaignCode
    );
    
    // Calculate final total
    const totalAmount = subtotal - discountAmount;
    
    // Create order
    const order = await client.create({
      _type: 'order',
      ...orderData,
      totalAmount,
      discountAmount,
      createdAt: new Date().toISOString(),
    });
    
    // Update product stock
    await updateProductStock(orderData.orderItems, 'decrease');
    
    // Increment campaign usage
    if (orderData.campaignCode && campaignDetails) {
      await incrementCampaignUsage(orderData.campaignCode);
    }
    
    return order;
  } catch (error) {
    console.error('Sipariş oluşturma hatası:', error);
    throw error;
  }
};

// Cancel order and restore stock
export const cancelOrderAndRestoreStock = async (orderId: string): Promise<void> => {
  try {
    const order = await client.fetch(
      `*[_id == $id][0]{orderItems, orderStatus, campaignCode}`,
      { id: orderId }
    );
    
    if (!order) {
      throw new Error('Sipariş bulunamadı');
    }
    
    if (order.orderStatus === 'cancelled') {
      throw new Error('Sipariş zaten iptal edilmiş');
    }
    
    // Restore stock
    await updateProductStock(order.orderItems, 'increase');
    
    // Decrement campaign usage if applicable
    if (order.campaignCode) {
      await client
        .patch({ query: `*[_type == "sales" && couponCode == $code][0]`, params: { code: order.campaignCode } })
        .inc({ usedCount: -1 })
        .commit();
    }
    
    // Update order status
    await client
      .patch(orderId)
      .set({ 
        orderStatus: 'cancelled',
        updatedAt: new Date().toISOString()
      })
      .commit();
      
  } catch (error) {
    console.error('Sipariş iptal etme hatası:', error);
    throw error;
  }
};

// Update order status with automatic actions
export const updateOrderStatus = async (orderId: string, newStatus: string, userId?: string): Promise<void> => {
  try {
    const updateData: any = {
      orderStatus: newStatus,
      updatedAt: new Date().toISOString()
    };
    
    // Add processed by user if provided
    if (userId) {
      updateData.processedBy = { _type: 'reference', _ref: userId };
    }
    
    await client
      .patch(orderId)
      .set(updateData)
      .commit();
      
    // Trigger additional actions based on status
    if (newStatus === 'cancelled') {
      await cancelOrderAndRestoreStock(orderId);
    }
    
  } catch (error) {
    console.error('Sipariş durumu güncelleme hatası:', error);
    throw error;
  }
};

// Validate order before processing
export const validateOrderForProcessing = async (orderId: string): Promise<{ valid: boolean; errors: string[] }> => {
  const errors: string[] = [];
  
  try {
    const order = await client.fetch(
      `*[_id == $id][0]{orderStatus, paymentStatus, orderItems, customer}`,
      { id: orderId }
    );
    
    if (!order) {
      errors.push('Sipariş bulunamadı');
      return { valid: false, errors };
    }
    
    if (order.orderStatus === 'cancelled') {
      errors.push('İptal edilmiş sipariş işlenemez');
    }
    
    if (order.paymentStatus !== 'paid') {
      errors.push('Ödeme tamamlanmamış sipariş işlenemez');
    }
    
    if (!order.orderItems || order.orderItems.length === 0) {
      errors.push('Sipariş kalemleri bulunamadı');
    }
    
    if (!order.customer) {
      errors.push('Müşteri bilgileri bulunamadı');
    }
    
    // Re-validate stock availability
    const stockValidation = await validateStockAvailability(order.orderItems);
    if (!stockValidation.valid) {
      errors.push(...stockValidation.errors);
    }
    
  } catch (error) {
    console.error('Sipariş doğrulama hatası:', error);
    errors.push('Sipariş doğrulaması yapılamadı');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
};
