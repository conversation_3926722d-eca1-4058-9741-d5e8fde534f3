import { type SanityClient } from 'next-sanity'

export async function validatePreviewUrl(
  client: SanityClient,
  url: string
) {
  const secret = process.env.SANITY_API_TOKEN
  if (!secret) {
    throw new Error('SANITY_API_TOKEN çevre değişkeni tanı<PERSON>lan<PERSON>mış')
  }

  const parsedUrl = new URL(url)
  const previewSecret = parsedUrl.searchParams.get('secret')
  const redirectTo = parsedUrl.searchParams.get('redirectTo') || '/'

  if (!previewSecret) {
    return { isValid: false }
  }

  if (previewSecret !== secret) {
    return { isValid: false }
  }

  return { isValid: true, redirectTo }
}