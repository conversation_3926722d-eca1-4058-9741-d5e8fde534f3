import { NextApiRequest, NextApiResponse } from 'next';
import SanityClient from '@sanity/client';

const sanity = SanityClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET!,
  apiVersion: '2023-06-10',
  useCdn: false,
});

function generateTicketNumbers(digitCount: number, numberCount: number): number[] {
  const numbers: number[] = [];
  for (let i = 0; i < numberCount; i++) {
    let num = '';
    for (let j = 0; j < digitCount; j++) {
      num += Math.floor(Math.random() * 10).toString();
    }
    numbers.push(parseInt(num, 10));
  }
  return numbers;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method Not Allowed' });
  }

  const { userId, drawId } = req.body;

  if (!userId || !drawId) {
    return res.status(400).json({ error: 'Missing userId or drawId' });
  }

  try {
    // Çekiliş detaylarını Sanity'den al
    const draw = await sanity.fetch(`*[_type == "draw" && _id == $drawId][0]`, { drawId });

    if (!draw) {
      return res.status(404).json({ error: 'Draw not found' });
    }

    const { ticketDigitCount, ticketNumberCount } = draw;

    // Bilet numaralarını oluştur
    const numbers = generateTicketNumbers(ticketDigitCount, ticketNumberCount);

    // Bileti veritabanına kaydet
    const newTicket = {
      _type: 'ticket',
      userId: userId,
      draw: {
        _ref: drawId,
        _type: 'reference',
      },
      numbers: numbers,
      purchaseDate: new Date().toISOString(),
    };

    await sanity.create(newTicket);

    res.status(200).json({ message: 'Ticket created successfully', numbers });
  } catch (error: any) {
    console.error('Error creating ticket:', error);
    res.status(500).json({ error: error.message || 'Failed to create ticket' });
  }
} 