import { NextRequest, NextResponse } from 'next/server'
import { currentUser } from '@clerk/nextjs/server'

// Geçici mock sistem - Sanity permissions sorunu çözülene kadar
export async function POST(request: NextRequest) {
  try {
    const { giveawayId, numberOfTickets } = await request.json();
    const clerkUser = await currentUser();

    if (!clerkUser) {
      return NextResponse.json({
        success: false,
        error: '<PERSON><PERSON><PERSON> ya<PERSON>al<PERSON>ınız'
      }, { status: 401 });
    }

    // Mock ticket generation
    const mockTickets = [];
    for (let i = 0; i < numberOfTickets; i++) {
      const ticketNumber = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
      mockTickets.push(ticketNumber);
    }

    console.log(`🎫 Mock bilet satın alma:`, {
      user: clerkUser.emailAddresses[0]?.emailAddress,
      giveawayId,
      numberOfTickets,
      tickets: mockTickets
    });

    // Simulate successful purchase
    return NextResponse.json({
      success: true,
      message: `${numberOfTickets} adet bilet başarıyla satın alındı (Mock)`,
      data: {
        tickets: mockTickets,
        totalPrice: numberOfTickets * 100,
        giveawayId,
        userId: clerkUser.id,
        purchaseDate: new Date().toISOString(),
        note: "Bu mock bir satın alma işlemidir. Sanity permissions düzeltilince gerçek sistem aktif olacak."
      }
    });

  } catch (error) {
    console.error('❌ Mock ticket purchase error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
