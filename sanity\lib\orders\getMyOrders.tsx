"use server";

import { defineQuery } from "next-sanity"
import { sanityFetch } from "../live";

export async function getMyOrders(userId: string) {
    if (!userId) {
        throw new Error('User not found')
    }

    const MY_ORDERS_QUERY = defineQuery(`
    *[_type == "order" && clerkUserId == $userId] | order(createdAt desc) {
        _id,
        orderNumber,
        customerName,
        totalAmount,
        orderStatus,
        createdAt,
        discountAmount,
        discountCode,
        orderItems[] {
            quantity,
            price,
            product-> {
                name,
                image
            }
        }
    }
    `);

    try {
        const orders = await sanityFetch({
            query: MY_ORDERS_QUERY,
            params: { userId }
        });
        return orders.data || [];
    } catch (error) {
        console.error('Error fetching orders:', error)
        throw new Error('Failed to fetch orders');
    }
}