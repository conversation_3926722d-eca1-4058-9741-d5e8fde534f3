/**
 * ---------------------------------------------------------------------------------
 * This file has been generated by Sanity TypeGen.
 * Command: `sanity typegen generate`
 *
 * Any modifications made directly to this file will be overwritten the next time
 * the TypeScript definitions are generated. Please make changes to the Sanity
 * schema definitions and/or GROQ queries if you need to update these types.
 *
 * For more information on how to use Sanity TypeGen, visit the official documentation:
 * https://www.sanity.io/docs/sanity-typegen
 * ---------------------------------------------------------------------------------
 */

// Source: schema.json
export type WalletTopUpRequest = {
  _id: string;
  _type: "walletTopUpRequest";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  user?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "user";
  };
  amount?: number;
  status?: "pending" | "approved" | "rejected";
  createdAt?: string;
  approvedAt?: string;
};

export type Giveaway = {
  _id: string;
  _type: "giveaway";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  description?: string;
  status?: "active" | "inactive" | "completed";
  startDate?: string;
  endDate?: string;
  drawDate?: string;
  totalTickets?: number;
  ticketsSold?: number;
  ticketPrice?: number;
  ticketSalePercentageForDraw?: number;
  numbersPerCard?: number;
  ticketDigitLength?: number;
  participants?: Array<{
    user?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "user";
    };
    tickets?: Array<{
      ticketNumber?: string;
      purchasedAt?: string;
      chosenDigitCount?: number;
      status?: "won" | "lost";
      _key: string;
    }>;
    _key: string;
  }>;
  prizes?: Array<{
    rank?: number;
    title?: string;
    description?: string;
    value?: number;
    image?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    };
    _key: string;
  }>;
  winningNumbers?: Array<string>;
  winners?: Array<{
    user?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "user";
    };
    ticketNumber?: string;
    prize?: string;
    rank?: number;
    _key: string;
  }>;
  rules?: string;
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
};

export type Auction = {
  _id: string;
  _type: "auction";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  id?: string;
  name?: string;
  description?: string;
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  currentBid?: number;
  startingBid?: number;
  bidIncrementAmount?: number;
  endTime?: string;
  bidders?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "user";
  }>;
  bidHistory?: Array<{
    bidAmount?: number;
    bidder?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "user";
    };
    bidTime?: string;
    _key: string;
  }>;
  status?: "active" | "completed" | "cancelled";
};

export type User = {
  _id: string;
  _type: "user";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  clerkId?: string;
  name?: string;
  email?: string;
  imageUrl?: string;
  isAdminApproved?: boolean;
  walletBalance?: number;
  isAdmin?: boolean;
};

export type Order = {
  _id: string;
  _type: "order";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  orderNumber?: string;
  customerName?: string;
  customerEmail?: string;
  clerkUserId?: string;
  orderItems?: Array<{
    product?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "product";
    };
    quantity?: number;
    price?: number;
    _key: string;
  }>;
  totalAmount?: number;
  paymentIntentId?: string;
  orderStatus?: "pending" | "processing" | "shipped" | "delivered" | "cancelled";
  paymentStatus?: "pending" | "paid" | "failed" | "refunded";
  shippingAddress?: {
    street?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
  };
  createdAt?: string;
};

export type Sales = {
  _id: string;
  _type: "sales";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  description?: string;
  discountAmount?: number;
  validityFrom?: string;
  validityUntil?: string;
  isActive?: boolean;
  couponCode?: string;
};

export type Product = {
  _id: string;
  _type: "product";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: string;
  slug?: Slug;
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  price?: number;
  description?: string;
  category?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "category";
  }>;
  isFeatured?: boolean;
  isAuction?: boolean;
  isGiveaway?: boolean;
  isLimited?: boolean;
  isBanners?: boolean;
  isProductExchange?: boolean;
  stock?: number;
};

export type Category = {
  _id: string;
  _type: "category";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  description?: string;
};

export type BlockContent = Array<{
  children?: Array<{
    marks?: Array<string>;
    text?: string;
    _type: "span";
    _key: string;
  }>;
  style?: "normal" | "h1" | "h2" | "h3" | "h4" | "blockquote";
  listItem?: "bullet";
  markDefs?: Array<{
    href?: string;
    _type: "link";
    _key: string;
  }>;
  level?: number;
  _type: "block";
  _key: string;
} | {
  asset?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
  };
  media?: unknown;
  hotspot?: SanityImageHotspot;
  crop?: SanityImageCrop;
  alt?: string;
  _type: "image";
  _key: string;
}>;

export type SanityImagePaletteSwatch = {
  _type: "sanity.imagePaletteSwatch";
  background?: string;
  foreground?: string;
  population?: number;
  title?: string;
};

export type SanityImagePalette = {
  _type: "sanity.imagePalette";
  darkMuted?: SanityImagePaletteSwatch;
  lightVibrant?: SanityImagePaletteSwatch;
  darkVibrant?: SanityImagePaletteSwatch;
  vibrant?: SanityImagePaletteSwatch;
  dominant?: SanityImagePaletteSwatch;
  lightMuted?: SanityImagePaletteSwatch;
  muted?: SanityImagePaletteSwatch;
};

export type SanityImageDimensions = {
  _type: "sanity.imageDimensions";
  height?: number;
  width?: number;
  aspectRatio?: number;
};

export type SanityImageHotspot = {
  _type: "sanity.imageHotspot";
  x?: number;
  y?: number;
  height?: number;
  width?: number;
};

export type SanityImageCrop = {
  _type: "sanity.imageCrop";
  top?: number;
  bottom?: number;
  left?: number;
  right?: number;
};

export type SanityFileAsset = {
  _id: string;
  _type: "sanity.fileAsset";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  originalFilename?: string;
  label?: string;
  title?: string;
  description?: string;
  altText?: string;
  sha1hash?: string;
  extension?: string;
  mimeType?: string;
  size?: number;
  assetId?: string;
  uploadId?: string;
  path?: string;
  url?: string;
  source?: SanityAssetSourceData;
};

export type SanityImageAsset = {
  _id: string;
  _type: "sanity.imageAsset";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  originalFilename?: string;
  label?: string;
  title?: string;
  description?: string;
  altText?: string;
  sha1hash?: string;
  extension?: string;
  mimeType?: string;
  size?: number;
  assetId?: string;
  uploadId?: string;
  path?: string;
  url?: string;
  metadata?: SanityImageMetadata;
  source?: SanityAssetSourceData;
};

export type SanityImageMetadata = {
  _type: "sanity.imageMetadata";
  location?: Geopoint;
  dimensions?: SanityImageDimensions;
  palette?: SanityImagePalette;
  lqip?: string;
  blurHash?: string;
  hasAlpha?: boolean;
  isOpaque?: boolean;
};

export type Geopoint = {
  _type: "geopoint";
  lat?: number;
  lng?: number;
  alt?: number;
};

export type Slug = {
  _type: "slug";
  current?: string;
  source?: string;
};

export type SanityAssetSourceData = {
  _type: "sanity.assetSourceData";
  name?: string;
  id?: string;
  url?: string;
};

export type AllSanitySchemaTypes = WalletTopUpRequest | Giveaway | Auction | User | Order | Sales | Product | Category | BlockContent | SanityImagePaletteSwatch | SanityImagePalette | SanityImageDimensions | SanityImageHotspot | SanityImageCrop | SanityFileAsset | SanityImageAsset | SanityImageMetadata | Geopoint | Slug | SanityAssetSourceData;
export declare const internalGroqTypeReferenceTo: unique symbol;
// Source: ./sanity/lib/giveaways/getGiveaways.ts
// Variable: ACTIVE_GIVEAWAYS_QUERY
// Query: *[_type == "giveaway" && status == "active"] | order(createdAt desc) {      _id,      title,      description,      status,      startDate,      endDate,      maxTickets,      ticketPrice,      createdAt,      updatedAt,      image {        asset->{url}      },      participants[]{user, tickets},      ticketsSold,      numbersPerCard,      ticketDigitLength,      prizes,      winners[]{user->{_id, name, email}, ticketNumber, prize, rank}    }
export type ACTIVE_GIVEAWAYS_QUERYResult = Array<{
  _id: string;
  title: string | null;
  description: string | null;
  status: "active" | "completed" | "inactive" | null;
  startDate: string | null;
  endDate: string | null;
  maxTickets: null;
  ticketPrice: number | null;
  createdAt: null;
  updatedAt: null;
  image: {
    asset: {
      url: string | null;
    } | null;
  } | null;
  participants: Array<{
    user: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "user";
    } | null;
    tickets: Array<{
      ticketNumber?: string;
      purchasedAt?: string;
      chosenDigitCount?: number;
      status?: "lost" | "won";
      _key: string;
    }> | null;
  }> | null;
  ticketsSold: number | null;
  numbersPerCard: number | null;
  ticketDigitLength: number | null;
  prizes: Array<{
    rank?: number;
    title?: string;
    description?: string;
    value?: number;
    image?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    };
    _key: string;
  }> | null;
  winners: Array<{
    user: {
      _id: string;
      name: string | null;
      email: string | null;
    } | null;
    ticketNumber: string | null;
    prize: string | null;
    rank: number | null;
  }> | null;
}>;
// Variable: GIVEAWAY_BY_ID_QUERY
// Query: *[_type == "giveaway" && _id == $id][0] {      _id,      title,      description,      status,      startDate,      endDate,      maxTickets,      ticketPrice,      createdAt,      updatedAt,      image {        asset->{url}      },      participants[]{user, tickets},      ticketsSold,      numbersPerCard,      ticketDigitLength,      prizes,      winners[]{user->{_id, name, email}, ticketNumber, prize, rank}    }
export type GIVEAWAY_BY_ID_QUERYResult = {
  _id: string;
  title: string | null;
  description: string | null;
  status: "active" | "completed" | "inactive" | null;
  startDate: string | null;
  endDate: string | null;
  maxTickets: null;
  ticketPrice: number | null;
  createdAt: null;
  updatedAt: null;
  image: {
    asset: {
      url: string | null;
    } | null;
  } | null;
  participants: Array<{
    user: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "user";
    } | null;
    tickets: Array<{
      ticketNumber?: string;
      purchasedAt?: string;
      chosenDigitCount?: number;
      status?: "lost" | "won";
      _key: string;
    }> | null;
  }> | null;
  ticketsSold: number | null;
  numbersPerCard: number | null;
  ticketDigitLength: number | null;
  prizes: Array<{
    rank?: number;
    title?: string;
    description?: string;
    value?: number;
    image?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    };
    _key: string;
  }> | null;
  winners: Array<{
    user: {
      _id: string;
      name: string | null;
      email: string | null;
    } | null;
    ticketNumber: string | null;
    prize: string | null;
    rank: number | null;
  }> | null;
} | null;
// Variable: ALL_GIVEAWAYS_QUERY
// Query: *[_type == "giveaway"] | order(createdAt desc) {      _id,      title,      description,      status,      startDate,      endDate,      maxTickets,      ticketPrice,      createdAt,      updatedAt,      image {        asset->{url}      },      participants[]{user, tickets},      ticketsSold,      numbersPerCard,      ticketDigitLength,      prizes,      winners[]{user->{_id, name, email}, ticketNumber, prize, rank}    }
export type ALL_GIVEAWAYS_QUERYResult = Array<{
  _id: string;
  title: string | null;
  description: string | null;
  status: "active" | "completed" | "inactive" | null;
  startDate: string | null;
  endDate: string | null;
  maxTickets: null;
  ticketPrice: number | null;
  createdAt: null;
  updatedAt: null;
  image: {
    asset: {
      url: string | null;
    } | null;
  } | null;
  participants: Array<{
    user: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "user";
    } | null;
    tickets: Array<{
      ticketNumber?: string;
      purchasedAt?: string;
      chosenDigitCount?: number;
      status?: "lost" | "won";
      _key: string;
    }> | null;
  }> | null;
  ticketsSold: number | null;
  numbersPerCard: number | null;
  ticketDigitLength: number | null;
  prizes: Array<{
    rank?: number;
    title?: string;
    description?: string;
    value?: number;
    image?: {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      _type: "image";
    };
    _key: string;
  }> | null;
  winners: Array<{
    user: {
      _id: string;
      name: string | null;
      email: string | null;
    } | null;
    ticketNumber: string | null;
    prize: string | null;
    rank: number | null;
  }> | null;
}>;

// Source: ./sanity/lib/orders/getMyOrders.tsx
// Variable: MY_ORDERS_QUERY
// Query: *[_type == "order" && clerkUserId == $userId] | order(createdAt desc) {        _id,        orderNumber,        customerName,        totalAmount,        orderStatus,        createdAt,        discountAmount,        discountCode,        orderItems[] {            quantity,            price,            product-> {                name,                image            }        }    }
export type MY_ORDERS_QUERYResult = Array<{
  _id: string;
  orderNumber: string | null;
  customerName: string | null;
  totalAmount: number | null;
  orderStatus: "cancelled" | "delivered" | "pending" | "processing" | "shipped" | null;
  createdAt: string | null;
  discountAmount: null;
  discountCode: null;
  orderItems: Array<{
    quantity: number | null;
    price: number | null;
    product: {
      name: string | null;
      image: {
        asset?: {
          _ref: string;
          _type: "reference";
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
        };
        media?: unknown;
        hotspot?: SanityImageHotspot;
        crop?: SanityImageCrop;
        _type: "image";
      } | null;
    } | null;
  }> | null;
}>;

// Source: ./sanity/lib/products/getAllCategories.ts
// Variable: ALL_CATEGORIES_QUERY
// Query: *[        _type == "category"        ] |  order(name asc)
export type ALL_CATEGORIES_QUERYResult = Array<{
  _id: string;
  _type: "category";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  description?: string;
}>;

// Source: ./sanity/lib/products/getAuctionProducts.ts
// Variable: AUCTION_PRODUCTS_QUERY
// Query: *[_type == "product" && isAuction == true] | order(_createdAt desc) {      _id,      _type,      _createdAt,      _updatedAt,      _rev,      name,      slug,      image,      price,      description,      category,      isFeatured,      isAuction,      isGiveaway,      stock    }
export type AUCTION_PRODUCTS_QUERYResult = Array<{
  _id: string;
  _type: "product";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name: string | null;
  slug: Slug | null;
  image: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  } | null;
  price: number | null;
  description: string | null;
  category: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "category";
  }> | null;
  isFeatured: boolean | null;
  isAuction: boolean | null;
  isGiveaway: boolean | null;
  stock: number | null;
}>;

// Source: ./sanity/lib/products/getGiveawayProducts.ts
// Variable: GIVEAWAY_PRODUCTS_QUERY
// Query: *[_type == "product" && isGiveaway == true] | order(_createdAt desc) {      _id,      _type,      _createdAt,      _updatedAt,      _rev,      name,      slug,      image,      price,      description,      category,      isFeatured,      isAuction,      isGiveaway,      stock    }
export type GIVEAWAY_PRODUCTS_QUERYResult = Array<{
  _id: string;
  _type: "product";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name: string | null;
  slug: Slug | null;
  image: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  } | null;
  price: number | null;
  description: string | null;
  category: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "category";
  }> | null;
  isFeatured: boolean | null;
  isAuction: boolean | null;
  isGiveaway: boolean | null;
  stock: number | null;
}>;

// Source: ./sanity/lib/products/getLimitedProducts.ts
// Variable: LIMITED_PRODUCTS_QUERY
// Query: *[_type == "product" && isFeatured == true] | order(_createdAt desc) [0...$limit]{      _id,      _type,      _createdAt,      _updatedAt,      _rev,      name,      slug,      image,      price,      description,      category,      isFeatured,      stock    }
export type LIMITED_PRODUCTS_QUERYResult = Array<{
  _id: string;
  _type: "product";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name: string | null;
  slug: Slug | null;
  image: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  } | null;
  price: number | null;
  description: string | null;
  category: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "category";
  }> | null;
  isFeatured: boolean | null;
  stock: number | null;
}>;

// Source: ./sanity/lib/products/getProductBySlug.ts
// Variable: PRODUCT_BY_SLUG_QUERY
// Query: *[            _type == "product" && slug.current == $slug        ] | order(name asc) [0]
export type PRODUCT_BY_SLUG_QUERYResult = {
  _id: string;
  _type: "product";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: string;
  slug?: Slug;
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  price?: number;
  description?: string;
  category?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "category";
  }>;
  isFeatured?: boolean;
  isAuction?: boolean;
  isGiveaway?: boolean;
  isLimited?: boolean;
  isBanners?: boolean;
  isProductExchange?: boolean;
  stock?: number;
} | null;

// Source: ./sanity/lib/products/getProductsByCategory.ts
// Variable: PRODUCTS_BY_CATEGORY_QUERY
// Query: *[            _type == "product"             && $categorySlug in category[]->slug.current        ]
export type PRODUCTS_BY_CATEGORY_QUERYResult = Array<{
  _id: string;
  _type: "product";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: string;
  slug?: Slug;
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  price?: number;
  description?: string;
  category?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "category";
  }>;
  isFeatured?: boolean;
  isAuction?: boolean;
  isGiveaway?: boolean;
  isLimited?: boolean;
  isBanners?: boolean;
  isProductExchange?: boolean;
  stock?: number;
}>;

// Source: ./sanity/lib/products/searchProductsByName.ts
// Variable: SEARCH_PRODUCTS_BY_NAME_QUERY
// Query: *[            _type == "product"             && name match $searchParam        ]
export type SEARCH_PRODUCTS_BY_NAME_QUERYResult = Array<{
  _id: string;
  _type: "product";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: string;
  slug?: Slug;
  image?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  price?: number;
  description?: string;
  category?: Array<{
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    _key: string;
    [internalGroqTypeReferenceTo]?: "category";
  }>;
  isFeatured?: boolean;
  isAuction?: boolean;
  isGiveaway?: boolean;
  isLimited?: boolean;
  isBanners?: boolean;
  isProductExchange?: boolean;
  stock?: number;
}>;

// Source: ./sanity/lib/sales/getAciveSaleByCouponCode.ts
// Variable: ACTIVE_SALES_BY_COUPON_QUERY
// Query: *[            _type == "sales" &&            isActive == true &&            couponCode == $couponCode        ] | order(validityFrom desc) [0]
export type ACTIVE_SALES_BY_COUPON_QUERYResult = {
  _id: string;
  _type: "sales";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  description?: string;
  discountAmount?: number;
  validityFrom?: string;
  validityUntil?: string;
  isActive?: boolean;
  couponCode?: string;
} | null;
// Variable: ACTIVE_SALES_BY_COUPON_CODES_QUERY
// Query: *[            _type == "sales" &&            isActive == true &&            couponCode in $couponCodes        ] | order(validityFrom desc)
export type ACTIVE_SALES_BY_COUPON_CODES_QUERYResult = Array<{
  _id: string;
  _type: "sales";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  description?: string;
  discountAmount?: number;
  validityFrom?: string;
  validityUntil?: string;
  isActive?: boolean;
  couponCode?: string;
}>;

// Source: ./sanity/lib/users/getUser.ts
// Variable: USER_QUERY
// Query: *[_type == "user" && clerkId == $clerkUserId][0] {      _id,      clerkId,      name,      email,      imageUrl,      isAdminApproved,      isAdmin,      walletBalance    }
export type USER_QUERYResult = {
  _id: string;
  clerkId: string | null;
  name: string | null;
  email: string | null;
  imageUrl: string | null;
  isAdminApproved: boolean | null;
  isAdmin: boolean | null;
  walletBalance: number | null;
} | null;

// Query TypeMap
import "@sanity/client";
declare module "@sanity/client" {
  interface SanityQueries {
    "\n    *[_type == \"giveaway\" && status == \"active\"] | order(createdAt desc) {\n      _id,\n      title,\n      description,\n      status,\n      startDate,\n      endDate,\n      maxTickets,\n      ticketPrice,\n      createdAt,\n      updatedAt,\n      image {\n        asset->{url}\n      },\n      participants[]{user, tickets},\n      ticketsSold,\n      numbersPerCard,\n      ticketDigitLength,\n      prizes,\n      winners[]{user->{_id, name, email}, ticketNumber, prize, rank}\n    }\n  ": ACTIVE_GIVEAWAYS_QUERYResult;
    "\n    *[_type == \"giveaway\" && _id == $id][0] {\n      _id,\n      title,\n      description,\n      status,\n      startDate,\n      endDate,\n      maxTickets,\n      ticketPrice,\n      createdAt,\n      updatedAt,\n      image {\n        asset->{url}\n      },\n      participants[]{user, tickets},\n      ticketsSold,\n      numbersPerCard,\n      ticketDigitLength,\n      prizes,\n      winners[]{user->{_id, name, email}, ticketNumber, prize, rank}\n    }\n  ": GIVEAWAY_BY_ID_QUERYResult;
    "\n    *[_type == \"giveaway\"] | order(createdAt desc) {\n      _id,\n      title,\n      description,\n      status,\n      startDate,\n      endDate,\n      maxTickets,\n      ticketPrice,\n      createdAt,\n      updatedAt,\n      image {\n        asset->{url}\n      },\n      participants[]{user, tickets},\n      ticketsSold,\n      numbersPerCard,\n      ticketDigitLength,\n      prizes,\n      winners[]{user->{_id, name, email}, ticketNumber, prize, rank}\n    }\n  ": ALL_GIVEAWAYS_QUERYResult;
    "\n    *[_type == \"order\" && clerkUserId == $userId] | order(createdAt desc) {\n        _id,\n        orderNumber,\n        customerName,\n        totalAmount,\n        orderStatus,\n        createdAt,\n        discountAmount,\n        discountCode,\n        orderItems[] {\n            quantity,\n            price,\n            product-> {\n                name,\n                image\n            }\n        }\n    }\n    ": MY_ORDERS_QUERYResult;
    "*[\n        _type == \"category\"\n        ] |  order(name asc)\n        ": ALL_CATEGORIES_QUERYResult;
    "*[_type == \"product\" && isAuction == true] | order(_createdAt desc) {\n      _id,\n      _type,\n      _createdAt,\n      _updatedAt,\n      _rev,\n      name,\n      slug,\n      image,\n      price,\n      description,\n      category,\n      isFeatured,\n      isAuction,\n      isGiveaway,\n      stock\n    }": AUCTION_PRODUCTS_QUERYResult;
    "*[_type == \"product\" && isGiveaway == true] | order(_createdAt desc) {\n      _id,\n      _type,\n      _createdAt,\n      _updatedAt,\n      _rev,\n      name,\n      slug,\n      image,\n      price,\n      description,\n      category,\n      isFeatured,\n      isAuction,\n      isGiveaway,\n      stock\n    }": GIVEAWAY_PRODUCTS_QUERYResult;
    "*[_type == \"product\" && isFeatured == true] | order(_createdAt desc) [0...$limit]{\n      _id,\n      _type,\n      _createdAt,\n      _updatedAt,\n      _rev,\n      name,\n      slug,\n      image,\n      price,\n      description,\n      category,\n      isFeatured,\n      stock\n    }": LIMITED_PRODUCTS_QUERYResult;
    "\n        *[\n            _type == \"product\" && slug.current == $slug\n        ] | order(name asc) [0]\n    ": PRODUCT_BY_SLUG_QUERYResult;
    "\n        *[\n            _type == \"product\" \n            && $categorySlug in category[]->slug.current\n        ]\n    ": PRODUCTS_BY_CATEGORY_QUERYResult;
    "\n        *[\n            _type == \"product\" \n            && name match $searchParam\n        ]\n    ": SEARCH_PRODUCTS_BY_NAME_QUERYResult;
    "\n        *[\n            _type == \"sales\" &&\n            isActive == true &&\n            couponCode == $couponCode\n        ] | order(validityFrom desc) [0]\n    ": ACTIVE_SALES_BY_COUPON_QUERYResult;
    "\n        *[\n            _type == \"sales\" &&\n            isActive == true &&\n            couponCode in $couponCodes\n        ] | order(validityFrom desc)\n    ": ACTIVE_SALES_BY_COUPON_CODES_QUERYResult;
    "\n    *[_type == \"user\" && clerkId == $clerkUserId][0] {\n      _id,\n      clerkId,\n      name,\n      email,\n      imageUrl,\n      isAdminApproved,\n      isAdmin,\n      walletBalance\n    }\n  ": USER_QUERYResult;
  }
}
