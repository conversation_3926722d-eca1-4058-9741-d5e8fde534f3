import { useQuery } from '@tanstack/react-query';
import { api } from '../lib/api';
import { 
  WalletTransaction, 
  WalletTransactionWithDetails,
  ApiResponse 
} from '../types/sanity';

// Query parameters interface
export interface WalletTransactionQueryParams {
  userId?: string;
  type?: 'deposit' | 'withdrawal' | 'refund' | 'bonus' | 'commission' | 'penalty';
  status?: 'pending' | 'completed' | 'cancelled' | 'failed';
  dateFrom?: string;
  dateTo?: string;
  minAmount?: number;
  maxAmount?: number;
  page?: number;
  limit?: number;
  sortBy?: 'createdAt' | 'amount' | 'type' | 'status';
  sortOrder?: 'asc' | 'desc';
}

// API functions
const fetchWalletTransactions = async (params?: WalletTransactionQueryParams): Promise<WalletTransaction[]> => {
  const response = await api.get('/wallet-transactions', params);
  return response.data;
};

const fetchWalletTransactionById = async (id: string): Promise<WalletTransaction | null> => {
  const response = await api.get(`/wallet-transactions/${id}`);
  return response.data;
};

const fetchUserWalletTransactions = async (userId: string): Promise<WalletTransaction[]> => {
  const response = await api.get(`/wallet-transactions/user/${userId}`);
  return response.data;
};

const fetchPendingTransactions = async (): Promise<WalletTransaction[]> => {
  const response = await api.get('/wallet-transactions/pending');
  return response.data;
};

const fetchTransactionsByType = async (type: string): Promise<WalletTransaction[]> => {
  const response = await api.get(`/wallet-transactions/type/${type}`);
  return response.data;
};

const fetchUserWalletBalance = async (userId: string): Promise<{ balance: number; currency: string }> => {
  const response = await api.get(`/wallet-transactions/balance/${userId}`);
  return response.data;
};

// React Query hooks
export const useWalletTransactions = (params?: WalletTransactionQueryParams) => {
  return useQuery<WalletTransaction[], Error>({
    queryKey: ['walletTransactions', params],
    queryFn: () => fetchWalletTransactions(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
    refetchOnWindowFocus: false,
  });
};

export const useWalletTransaction = (id: string) => {
  return useQuery<WalletTransaction | null, Error>({
    queryKey: ['walletTransaction', id],
    queryFn: () => fetchWalletTransactionById(id),
    staleTime: 2 * 60 * 1000,
    retry: 2,
    enabled: !!id,
  });
};

export const useUserWalletTransactions = (userId: string) => {
  return useQuery<WalletTransaction[], Error>({
    queryKey: ['walletTransactions', 'user', userId],
    queryFn: () => fetchUserWalletTransactions(userId),
    staleTime: 1 * 60 * 1000, // 1 minute for user's own transactions
    retry: 2,
    enabled: !!userId,
  });
};

export const usePendingTransactions = () => {
  return useQuery<WalletTransaction[], Error>({
    queryKey: ['walletTransactions', 'pending'],
    queryFn: fetchPendingTransactions,
    staleTime: 30 * 1000, // 30 seconds for pending transactions
    retry: 2,
  });
};

export const useTransactionsByType = (type: string) => {
  return useQuery<WalletTransaction[], Error>({
    queryKey: ['walletTransactions', 'type', type],
    queryFn: () => fetchTransactionsByType(type),
    staleTime: 2 * 60 * 1000,
    retry: 2,
    enabled: !!type,
  });
};

export const useUserWalletBalance = (userId: string) => {
  return useQuery<{ balance: number; currency: string }, Error>({
    queryKey: ['walletBalance', userId],
    queryFn: () => fetchUserWalletBalance(userId),
    staleTime: 30 * 1000, // 30 seconds for wallet balance
    retry: 2,
    enabled: !!userId,
  });
};

// Utility hooks
export const useTransactionTypes = () => {
  const types = [
    { value: 'deposit', label: 'Yükleme', color: 'green', icon: '⬆️' },
    { value: 'withdrawal', label: 'Harcama', color: 'red', icon: '⬇️' },
    { value: 'refund', label: 'İade', color: 'blue', icon: '↩️' },
    { value: 'bonus', label: 'Bonus', color: 'purple', icon: '🎁' },
    { value: 'commission', label: 'Komisyon', color: 'orange', icon: '💼' },
    { value: 'penalty', label: 'Ceza', color: 'red', icon: '⚠️' },
  ] as const;

  return types;
};

export const useTransactionStatuses = () => {
  const statuses = [
    { value: 'pending', label: 'Bekliyor', color: 'orange' },
    { value: 'completed', label: 'Tamamlandı', color: 'green' },
    { value: 'cancelled', label: 'İptal Edildi', color: 'red' },
    { value: 'failed', label: 'Başarısız', color: 'red' },
  ] as const;

  return statuses;
};

// Helper functions
export const getTransactionTypeColor = (type: string): string => {
  const typeColors: Record<string, string> = {
    deposit: '#10b981',
    withdrawal: '#ef4444',
    refund: '#3b82f6',
    bonus: '#8b5cf6',
    commission: '#f59e0b',
    penalty: '#ef4444',
  };
  return typeColors[type] || '#6b7280';
};

export const getTransactionStatusColor = (status: string): string => {
  const statusColors: Record<string, string> = {
    pending: '#f59e0b',
    completed: '#10b981',
    cancelled: '#ef4444',
    failed: '#ef4444',
  };
  return statusColors[status] || '#6b7280';
};

export const getTransactionTypeIcon = (type: string): string => {
  const typeIcons: Record<string, string> = {
    deposit: '⬆️',
    withdrawal: '⬇️',
    refund: '↩️',
    bonus: '🎁',
    commission: '💼',
    penalty: '⚠️',
  };
  return typeIcons[type] || '💰';
};

export const formatTransactionAmount = (transaction: WalletTransaction): string => {
  const symbols = { TRY: '₺', USD: '$', EUR: '€' };
  const symbol = symbols[transaction.currency as keyof typeof symbols] || transaction.currency;
  
  const sign = ['withdrawal', 'penalty'].includes(transaction.type) ? '-' : '+';
  
  return `${sign}${transaction.amount} ${symbol}`;
};

export const isTransactionPositive = (transaction: WalletTransaction): boolean => {
  return ['deposit', 'refund', 'bonus'].includes(transaction.type);
};

export const isTransactionNegative = (transaction: WalletTransaction): boolean => {
  return ['withdrawal', 'penalty', 'commission'].includes(transaction.type);
};

export const calculateBalanceChange = (transaction: WalletTransaction): number => {
  return isTransactionPositive(transaction) ? transaction.amount : -transaction.amount;
};

export const getTransactionDescription = (transaction: WalletTransaction): string => {
  if (transaction.description) return transaction.description;
  
  const defaultDescriptions: Record<string, string> = {
    deposit: 'Cüzdan yüklemesi',
    withdrawal: 'Satın alma işlemi',
    refund: 'İade işlemi',
    bonus: 'Bonus kazancı',
    commission: 'Komisyon kesintisi',
    penalty: 'Ceza kesintisi',
  };
  
  return defaultDescriptions[transaction.type] || 'Cüzdan işlemi';
};

export const canCancelTransaction = (transaction: WalletTransaction): boolean => {
  return transaction.status === 'pending' && 
         ['deposit', 'withdrawal'].includes(transaction.type);
};

export const canRefundTransaction = (transaction: WalletTransaction): boolean => {
  return transaction.status === 'completed' && 
         transaction.type === 'withdrawal';
};

export const formatTransactionDate = (dateString: string): string => {
  return new Date(dateString).toLocaleString('tr-TR', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// Statistics helpers
export const calculateTransactionStats = (transactions: WalletTransaction[]) => {
  const stats = {
    totalDeposits: 0,
    totalWithdrawals: 0,
    totalRefunds: 0,
    totalBonuses: 0,
    totalCommissions: 0,
    totalPenalties: 0,
    pendingCount: 0,
    completedCount: 0,
    failedCount: 0,
  };

  transactions.forEach(transaction => {
    // Count by type
    switch (transaction.type) {
      case 'deposit':
        stats.totalDeposits += transaction.amount;
        break;
      case 'withdrawal':
        stats.totalWithdrawals += transaction.amount;
        break;
      case 'refund':
        stats.totalRefunds += transaction.amount;
        break;
      case 'bonus':
        stats.totalBonuses += transaction.amount;
        break;
      case 'commission':
        stats.totalCommissions += transaction.amount;
        break;
      case 'penalty':
        stats.totalPenalties += transaction.amount;
        break;
    }

    // Count by status
    switch (transaction.status) {
      case 'pending':
        stats.pendingCount++;
        break;
      case 'completed':
        stats.completedCount++;
        break;
      case 'failed':
        stats.failedCount++;
        break;
    }
  });

  return stats;
};

// Filter and sort helpers
export const filterTransactionsByDateRange = (
  transactions: WalletTransaction[], 
  dateFrom?: string, 
  dateTo?: string
): WalletTransaction[] => {
  if (!dateFrom && !dateTo) return transactions;
  
  return transactions.filter(transaction => {
    const transactionDate = new Date(transaction.createdAt);
    const fromDate = dateFrom ? new Date(dateFrom) : null;
    const toDate = dateTo ? new Date(dateTo) : null;
    
    if (fromDate && transactionDate < fromDate) return false;
    if (toDate && transactionDate > toDate) return false;
    
    return true;
  });
};

export const sortTransactions = (
  transactions: WalletTransaction[], 
  sortBy: 'createdAt' | 'amount' | 'type' | 'status' = 'createdAt',
  sortOrder: 'asc' | 'desc' = 'desc'
): WalletTransaction[] => {
  return [...transactions].sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'amount':
        comparison = a.amount - b.amount;
        break;
      case 'type':
        comparison = a.type.localeCompare(b.type);
        break;
      case 'status':
        comparison = a.status.localeCompare(b.status);
        break;
      case 'createdAt':
      default:
        comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        break;
    }
    
    return sortOrder === 'desc' ? -comparison : comparison;
  });
};
