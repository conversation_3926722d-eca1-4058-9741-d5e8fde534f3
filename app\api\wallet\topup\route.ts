import { NextRequest } from 'next/server';
import { client } from '@/sanity/lib/client';
import { currentUser } from '@clerk/nextjs/server';
import { getUserByClerkId } from '@/sanity/lib/users/getUser';

export async function POST(req: NextRequest) {
  const { amount } = await req.json();
  const clerkUser = await currentUser();
  if (!clerkUser || !clerkUser.id || !clerkUser.emailAddresses[0]?.emailAddress) {
    return new Response(JSON.stringify({ error: 'Yetkisiz: Kullanıcı bilgileri eksik.' }), { status: 401 });
  }
  const user = await getUserByClerkId({
    id: clerkUser.id,
    email: clerkUser.emailAddresses[0].emailAddress,
    name: clerkUser.fullName || clerkUser.firstName || clerkUser.emailAddresses[0].emailAddress,
  });
  if (!user || !user._id) {
    return new Response(JSON.stringify({ error: '<PERSON><PERSON><PERSON><PERSON><PERSON> bulunamadı.' }), { status: 404 });
  }
  if (!amount || amount < 1) {
    return new Response(JSON.stringify({ error: 'Geçersiz talep.' }), { status: 400 });
  }
  try {
    await client.create({
      _type: 'walletTopUpRequest',
      user: { _type: 'reference', _ref: user._id },
      amount,
      status: 'pending',
      createdAt: new Date().toISOString(),
    });
    return new Response(JSON.stringify({ success: true }), { status: 200 });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Talep kaydedilemedi.' }), { status: 500 });
  }
} 