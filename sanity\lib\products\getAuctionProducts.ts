"use server";

import { defineQuery } from "next-sanity";
import { sanityFetch } from "../live";
import { Product } from "@/sanity.types";

export async function getAuctionProducts(): Promise<Product[]> {
  const AUCTION_PRODUCTS_QUERY = defineQuery(
    `*[_type == "product" && isAuction == true] | order(_createdAt desc) {
      _id,
      _type,
      _createdAt,
      _updatedAt,
      _rev,
      name,
      slug,
      image,
      price,
      description,
      category,
      isFeatured,
      isAuction,
      isGiveaway,
      stock
    }`,
  );

  try {
    const products = await sanityFetch({
      query: AUCTION_PRODUCTS_QUERY,
    });
    return products.data || [];
  } catch (error) {
    console.error('Error fetching auction products:', error);
    throw new Error('Failed to fetch auction products');
  }
} 