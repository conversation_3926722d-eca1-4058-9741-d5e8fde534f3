"use client";

import Loader from "@/components/Loader";
import stripe from "@/lib/stripe";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useBasketStore } from "../store";
import Link from "next/link";

function SuccessPage() {
  const searchParams = useSearchParams();
  const sessionId = searchParams.get("session_id");
  const [orderNumber, setOrderNumber] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const clearBasket = useBasketStore((state) => state.clearBasket);

  useEffect(() => {
    const fetchOrderDetails = async () => {
      if (!sessionId) return;
      
      try {
        const response = await fetch(`/api/get-order?session_id=${sessionId}`);
        const data = await response.json();
        
        if (data.orderNumber) {
          setOrderNumber(data.orderNumber);
          clearBasket();
        }
      } catch (error) {
        console.error("Error fetching order details:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchOrderDetails();
  }, [sessionId, clearBasket]);

  if (loading) {
    return <Loader />;
  }

  return (
    <div className="flex flex-col items-center justify-center h-screen bg-gray-100">
      <div className="bg-white p-12 rounded-xl shadow-lg max-w-2xl w-full mx-4">
        <div className="flex justify-center mb-8">
          <div className="h-16 w-16 bg-green-100 rounded-full flex items-center justify-center">
            <svg
              className="h-8 w-8 text-green-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
        </div>
        <h1 className="text-2xl font-bold text-center mb-4">
          Thank you for your order!
        </h1>
        <p className="text-center text-gray-600 mb-6">
          Your order has been placed successfully.
        </p>
        {orderNumber && (
          <p className="text-center text-gray-600 mb-6">
            <span className="font-bold">Order Number: </span>
            <Link href={`/orders/${orderNumber}`} className="font-bold text-green-600">
              {orderNumber}
            </Link>
          </p>
        )}
        <div className="text-center">
          <Link 
            href="/" 
            className="inline-block bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors"
          >
            Continue Shopping
          </Link>
        </div>
      </div>
    </div>
  );
}

export default SuccessPage;
