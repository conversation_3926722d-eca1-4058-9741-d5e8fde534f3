import { defineQuery } from "next-sanity";
import { CouponCode } from "./couponCodes";
import { sanityFetch } from "../live";

// Tek bir kupon kodu ile aktif indirimi getirir
export const getActiveSaleByCouponCode = async (couponCode: CouponCode) => {
    const ACTIVE_SALES_BY_COUPON_QUERY = defineQuery(`
        *[
            _type == "sales" &&
            isActive == true &&
            couponCode == $couponCode
        ] | order(validityFrom desc) [0]
    `);

    try {
        const activeSale = await sanityFetch({
            query: ACTIVE_SALES_BY_COUPON_QUERY,
            params: { couponCode }
        });
        return activeSale?.data ?? null;
    } catch (error) {
        console.error("Error fetching active sale by coupon code", error);
        return null;
    }
}

// Birden fazla kupon kodu ile aktif indirimleri getirir
export const getActiveSalesByCouponCodes = async (couponCodes: string[]) => {
    const ACTIVE_SALES_BY_COUPON_CODES_QUERY = defineQuery(`
        *[
            _type == "sales" &&
            isActive == true &&
            couponCode in $couponCodes
        ] | order(validityFrom desc)
    `);

    try {
        const result = await sanityFetch({
            query: ACTIVE_SALES_BY_COUPON_CODES_QUERY,
            params: { couponCodes }
        });
        return result?.data ?? [];
    } catch (error) {
        console.error("Error fetching active sales by coupon codes", error);
        return [];
    }
}



