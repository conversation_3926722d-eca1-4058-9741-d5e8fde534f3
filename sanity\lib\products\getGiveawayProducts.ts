"use server";

import { defineQuery } from "next-sanity";
import { sanityFetch } from "../live";
import { Product } from "@/sanity.types";

export async function getGiveawayProducts(): Promise<Product[]> {
  const GIVEAWAY_PRODUCTS_QUERY = defineQuery(
    `*[_type == "product" && isGiveaway == true] | order(_createdAt desc) {
      _id,
      _type,
      _createdAt,
      _updatedAt,
      _rev,
      name,
      slug,
      image,
      price,
      description,
      category,
      isFeatured,
      isAuction,
      isGiveaway,
      stock
    }`,
  );

  try {
    const products = await sanityFetch({
      query: GIVEAWAY_PRODUCTS_QUERY,
    });
    return products.data || [];
  } catch (error) {
    console.error('Error fetching giveaway products:', error);
    throw new Error('Failed to fetch giveaway products');
  }
} 