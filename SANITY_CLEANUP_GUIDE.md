# Sanity Studio Temizlik Rehberi

## Problem
Tesla Model 3 ürününü silmeye çalışıyorsunuz ama 3 doküman ona referans veriyor:
- FtXUk2TbQ7HuvpG8e2SFik
- Nl2FBXzZzpDmfIuldyHDIb  
- Nl2FBXzZzpDmfIuldyZl7J

## Çözüm Adımları

### 1. Manuel Referans Temizleme (Önerilen)

#### Adım 1: Auction Dokümanlarını Temizle
1. Sanity Studio'ya gidin: `http://localhost:3000/Studio`
2. Sol menüden **"Auctions"** seçin
3. Her bir auction dokümanını açın
4. **"Ürün"** alanını bulun ve **"Remove"** butonuna tıklayın
5. Dokümanı kaydedin

#### Adım 2: Giveaway Dokümanlarını Temizle
1. <PERSON> menüden **"Giveaways"** seçin (eğer varsa)
2. Her bir giveaway dokümanını açın
3. **"Ürün"** alanını bulun ve **"Remove"** butonuna tıklayın
4. Dokümanı kaydedin

#### Adım 3: Order Dokümanlarını Temizle
1. Sol menüden **"Orders"** seçin
2. Tesla Model 3 içeren siparişleri bulun
3. Bu siparişleri tamamen silin veya ürün referanslarını kaldırın

### 2. Doküman ID'leri ile Manuel Silme

Eğer dokümanları bulamıyorsanız, Vision tool kullanarak:

1. Studio'da **"Vision"** sekmesine gidin
2. Bu sorguları çalıştırın:

```groq
// Problematik dokümanları bul
*[_id in ["FtXUk2TbQ7HuvpG8e2SFik", "Nl2FBXzZzpDmfIuldyHDIb", "Nl2FBXzZzpDmfIuldyZl7J"]]

// Tesla Model 3'e referans eden tüm dokümanları bul
*[references("Tesla Model 3")]

// Tüm auction'ları listele
*[_type == "auction"]{_id, name, product}
```

### 3. Toplu Silme (Vision Tool ile)

Vision tool'da bu komutları çalıştırabilirsiniz:

```groq
// Tüm auction'ları sil
*[_type == "auction"]

// Tüm giveaway'leri sil  
*[_type == "giveaway"]

// Belirli ID'leri sil
*[_id in ["FtXUk2TbQ7HuvpG8e2SFik", "Nl2FBXzZzpDmfIuldyHDIb", "Nl2FBXzZzpDmfIuldyZl7J"]]
```

**NOT:** Vision tool sadece sorgu yapar, silme işlemi yapmaz. Silme için Studio interface'ini kullanmanız gerekir.

### 4. Alternatif: Yeni Ürün Oluştur

Eğer Tesla Model 3'ü silmek yerine:
1. Yeni bir ürün oluşturun
2. Referansları yeni ürüne yönlendirin
3. Sonra Tesla Model 3'ü silin

## Önemli Notlar

- ⚠️ **API token'ınızın write permissions'ı yok**, bu yüzden otomatik temizlik çalışmıyor
- ✅ **Manuel temizlik en güvenli yöntem**
- 🔄 **Her referansı kaldırdıktan sonra Tesla Model 3'ü silebilirsiniz**

## Test

Temizlik sonrası kontrol için:
```
http://localhost:3000/api/cleanup-references
```

Bu endpoint ile kalan referansları görebilirsiniz.
