'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { FiChevronLeft, FiChevronRight, FiGift, FiUsers, FiClock } from 'react-icons/fi';
import { client } from '@/sanity/lib/client';
import imageUrlBuilder from '@sanity/image-url';
import { SanityImageSource } from '@sanity/image-url/lib/types/types';

interface Giveaway {
  _id: string;
  title: {
    tr?: string;
    en?: string;
  } | string;
  image?: {
    asset?: {
      url?: string;
    };
  };
  description?: {
    tr?: string;
    en?: string;
  } | string;
  startDate?: string;
  endDate?: string;
  ticketPrice: number;
  ticketsSold: number;
  maxTickets?: number;
  status?: string;
}

const builder = imageUrlBuilder(client);
function urlFor(source: SanityImageSource) {
  return builder.image(source);
}

export default function GiveawayBanners() {
  const [giveaways, setGiveaways] = useState<Giveaway[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchGiveaways = async () => {
      try {
        const response = await fetch('/api/giveaways/all');
        const data = await response.json();

        if (data.giveaways) {
          setGiveaways(data.giveaways);
        } else {
          setError('Çekiliş verisi alınamadı');
        }
      } catch (err) {
        console.error('Çekiliş yükleme hatası:', err);
        setError('Çekilişler yüklenirken bir hata oluştu');
      } finally {
        setLoading(false);
      }
    };

    fetchGiveaways();
  }, []);

  useEffect(() => {
    if (giveaways.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) =>
        prevIndex === giveaways.length - 1 ? 0 : prevIndex + 1
      );
    }, 5000);

    return () => clearInterval(interval);
  }, [giveaways.length]);

  const goToPrevious = () => {
    setCurrentIndex(currentIndex === 0 ? giveaways.length - 1 : currentIndex - 1);
  };

  const goToNext = () => {
    setCurrentIndex(currentIndex === giveaways.length - 1 ? 0 : currentIndex + 1);
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <section className="py-8">
        <div className="max-w-7xl mx-auto px-4">
          <div className="bg-gray-200 animate-pulse rounded-lg h-64"></div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-8">
        <div className="max-w-7xl mx-auto px-4 text-center text-red-500">
          Hata oluştu: {error}
        </div>
      </section>
    );
  }

  if (giveaways.length === 0) {
    return (
      <section className="py-8">
        <div className="max-w-7xl mx-auto px-4 text-center text-gray-500">
          Şu anda aktif çekiliş bulunmamaktadır.
        </div>
      </section>
    );
  }

  const currentGiveaway = giveaways[currentIndex];

  return (
    <section className="relative overflow-hidden mb-8">
      <div className="max-w-full mx-auto">
        <div className="relative w-full h-[250px] md:h-[350px] lg:h-[350px] flex items-center justify-center 
        rounded-lg shadow-2xl bg-gradient-to-br from-green-400 to-blue-600 md:p-8">
          <div className="bg-white rounded-lg shadow-xl overflow-hidden flex flex-col md:flex-row w-full max-w-4xl h-full transform transition-transform duration-500 scale-95 opacity-90 hover:scale-100 hover:opacity-100 relative z-10">
            {currentGiveaway.image?.asset?.url ? (
                 <div className="relative w-full md:w-1/2 h-64 md:h-full bg-gray-100 flex items-center justify-center">
                    <img
                        src={currentGiveaway.image.asset.url}
                        alt={typeof currentGiveaway.title === 'string' ? currentGiveaway.title : (currentGiveaway.title?.tr || currentGiveaway.title?.en || 'Çekiliş')}
                        className="p-4 object-contain w-full h-full"
                    />
                 </div>
            ) : (
                <div className="relative w-full md:w-1/2 h-64 md:h-full bg-gray-100 flex items-center justify-center">
                  <div className="text-center text-gray-400">
                    <div className="text-6xl mb-2">🎁</div>
                    <p className="text-sm">Çekiliş Görseli</p>
                  </div>
                </div>
            )}
            <div className="w-full md:w-1/2 p-6 flex flex-col justify-center text-gray-800">
              <h2 className="text-3xl md:text-4xl font-bold mb-3 leading-tight">
                {typeof currentGiveaway.title === 'string'
                  ? currentGiveaway.title
                  : (currentGiveaway.title?.tr || currentGiveaway.title?.en || 'Çekiliş')
                }
              </h2>
              <p className="text-lg md:text-xl text-gray-600 mb-4">
                {typeof currentGiveaway.description === 'string'
                  ? currentGiveaway.description
                  : (currentGiveaway.description?.tr || currentGiveaway.description?.en || 'Harika ödüller kazanma şansı!')
                }
              </p>
              
              <div className="space-y-2 mb-6">
                <div className="flex items-center text-green-600">
                  <FiGift className="mr-2" />
                  <span className="font-semibold">
                    Bilet Fiyatı: ₺{currentGiveaway.ticketPrice || 0}
                  </span>
                </div>
                
                <div className="flex items-center text-blue-600">
                  <FiUsers className="mr-2" />
                  <span className="font-semibold">
                    Satılan: {currentGiveaway.ticketsSold || 0} / {currentGiveaway.maxTickets || 'Sınırsız'}
                  </span>
                </div>
                
                {currentGiveaway.endDate && (
                  <div className="flex items-center text-orange-600">
                    <FiClock className="mr-2" />
                    <span className="font-semibold">
                      Bitiş: {formatDate(currentGiveaway.endDate)}
                    </span>
                  </div>
                )}
              </div>

              <Link
                href={`/giveaways/${currentGiveaway._id}`}
                className="inline-block bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white font-bold py-3 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg text-center"
              >
                Çekilişe Katıl
              </Link>
            </div>
          </div>

          {giveaways.length > 1 && (
            <>
              <button
                onClick={goToPrevious}
                className="absolute left-6 top-1/2 transform -translate-y-1/2 p-3 rounded-full bg-black bg-opacity-30 hover:bg-opacity-50 text-white transition-all z-20"
                aria-label="Önceki çekiliş"
              >
                <FiChevronLeft size={28} />
              </button>

              <button
                onClick={goToNext}
                className="absolute right-6 top-1/2 transform -translate-y-1/2 p-3 rounded-full bg-black bg-opacity-30 hover:bg-opacity-50 text-white transition-all z-20"
                aria-label="Sonraki çekiliş"
              >
                <FiChevronRight size={28} />
              </button>
            </>
          )}
        </div>

        {giveaways.length > 1 && (
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-2 z-20">
            {giveaways.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentIndex
                    ? 'bg-white scale-125'
                    : 'bg-white bg-opacity-50 hover:bg-opacity-75 hover:scale-110'
                }`}
                aria-label={`Çekiliş ${index + 1}'e git`}
              />
            ))}
          </div>
        )}

        {giveaways.length > 1 && (
          <div className="text-center mt-6 px-4">
            <span className="text-base text-gray-600 bg-white bg-opacity-80 px-3 py-1 rounded-full shadow-sm">
              {currentIndex + 1} / {giveaways.length}
            </span>
          </div>
        )}
      </div>
    </section>
  );
}
