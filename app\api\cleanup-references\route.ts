import { NextRequest, NextResponse } from 'next/server'
import { writeClient } from '@/sanity/lib/client'

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Referansları kontrol ediliyor...');

    // Tesla Model 3'e referans eden dokümanları bul
    const teslaReferences = await writeClient.fetch(`
      *[references("Tesla Model 3")]{
        _id,
        _type,
        "title": coalesce(name.tr, name.en, name, title.tr, title.en, title, "Başlıksız")
      }
    `);

    // Tüm referansları kontrol et
    const allReferences = await writeClient.fetch(`
      *[_type in ["auction", "giveaway", "order"] && defined(product)]{
        _id,
        _type,
        "productId": product._ref,
        "title": coalesce(name.tr, name.en, name, title.tr, title.en, title, "Başlıksız")
      }
    `);

    return NextResponse.json({
      success: true,
      message: 'Referans durumu',
      data: {
        teslaReferences,
        allReferences,
        teslaReferenceCount: teslaReferences.length,
        totalReferences: allReferences.length
      }
    });

  } catch (error) {
    console.error('❌ Referans kontrol hatası:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const confirm = searchParams.get('confirm');

    if (confirm !== 'true') {
      return NextResponse.json({
        success: false,
        error: 'Silme işlemi için confirm=true parametresi gerekli'
      }, { status: 400 });
    }

    console.log(`🗑️ ${action} işlemi başlatılıyor...`);

    let deletedCount = 0;
    let results = [];

    if (action === 'remove-tesla-references') {
      // Tesla Model 3'e referans eden dokümanları bul ve referansları kaldır
      const teslaReferences = await writeClient.fetch(`
        *[references("Tesla Model 3")]{
          _id,
          _type,
          product
        }
      `);

      for (const doc of teslaReferences) {
        try {
          // Referansı kaldır (product alanını null yap)
          await writeClient
            .patch(doc._id)
            .unset(['product'])
            .commit();
          
          deletedCount++;
          results.push({
            id: doc._id,
            type: doc._type,
            action: 'reference_removed',
            success: true
          });
        } catch (error) {
          results.push({
            id: doc._id,
            type: doc._type,
            action: 'reference_removal_failed',
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
    } else if (action === 'delete-referencing-docs') {
      // Tesla Model 3'e referans eden dokümanları tamamen sil
      const teslaReferences = await writeClient.fetch(`
        *[references("Tesla Model 3")]{
          _id,
          _type
        }
      `);

      for (const doc of teslaReferences) {
        try {
          await writeClient.delete(doc._id);
          deletedCount++;
          results.push({
            id: doc._id,
            type: doc._type,
            action: 'document_deleted',
            success: true
          });
        } catch (error) {
          results.push({
            id: doc._id,
            type: doc._type,
            action: 'deletion_failed',
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
    } else if (action === 'clean-all-references') {
      // Tüm boş referansları temizle
      const docsWithReferences = await writeClient.fetch(`
        *[_type in ["auction", "giveaway", "order"] && defined(product)]{
          _id,
          _type,
          product
        }
      `);

      for (const doc of docsWithReferences) {
        try {
          // Referansı kaldır
          await writeClient
            .patch(doc._id)
            .unset(['product'])
            .commit();
          
          deletedCount++;
          results.push({
            id: doc._id,
            type: doc._type,
            action: 'reference_cleaned',
            success: true
          });
        } catch (error) {
          results.push({
            id: doc._id,
            type: doc._type,
            action: 'cleaning_failed',
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
    } else {
      return NextResponse.json({
        success: false,
        error: 'Geçersiz aksiyon. Kullanılabilir: remove-tesla-references, delete-referencing-docs, clean-all-references'
      }, { status: 400 });
    }

    console.log(`✅ ${deletedCount} adet işlem tamamlandı`);

    return NextResponse.json({
      success: true,
      message: `${action} işlemi tamamlandı`,
      processedCount: deletedCount,
      results
    });

  } catch (error) {
    console.error('❌ Referans temizleme hatası:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
