import { HeartIcon } from '@sanity/icons';
import { defineArrayMember, defineField, defineType } from 'sanity';
import { nanoid } from 'nanoid';

// Helper function for image field
const defineImageField = (title = 'Görsel') =>
  defineField({
    name: 'image',
    title,
    type: 'image',
    options: { hotspot: true },
    fields: [
      {
        name: 'alt',
        title: 'Alternatif Metin',
        type: 'string',
        validation: (Rule) => Rule.required().error('Alternatif metin zorunludur.'),
      },
    ],
  });

export const giveawayType = defineType({
  name: 'giveaway',
  title: 'Çekili<PERSON>',
  type: 'document',
  icon: HeartIcon,
  groups: [
    { name: 'basic', title: 'Temel Bilgiler' },
    { name: 'tickets', title: 'Biletler' },
    { name: 'prizes', title: 'Ödüller' },
    { name: 'status', title: 'Durum' },
  ],
  fields: [
    defineField({
      name: 'id',
      title: 'Çekiliş ID',
      type: 'string',
      group: 'basic',
      readOnly: true,
      initialValue: () => `GIV-${nanoid(8)}`,
      validation: (Rule) => Rule.required().error('Çekiliş ID zorunludur.'),
    }),
    defineField({
      name: 'title',
      title: 'Çekiliş Başlığı',
      type: 'object',
      group: 'basic',
      fields: [
        { name: 'tr', title: 'Türkçe', type: 'string', validation: (Rule) => Rule.required() },
        { name: 'en', title: 'İngilizce', type: 'string' },
      ],
    }),
    defineField({
      name: 'description',
      title: 'Açıklama',
      type: 'object',
      group: 'basic',
      fields: [
        { name: 'tr', title: 'Türkçe', type: 'text' },
        { name: 'en', title: 'İngilizce', type: 'text' },
      ],
    }),
    defineImageField('Çekiliş Görseli'),
    defineField({
      name: 'rules',
      title: 'Çekiliş Kuralları',
      type: 'object',
      group: 'basic',
      fields: [
        { name: 'tr', title: 'Türkçe', type: 'text' },
        { name: 'en', title: 'İngilizce', type: 'text' },
      ],
    }),
    defineField({
      name: 'sale',
      title: 'Bağlı Kampanya',
      type: 'reference',
      to: [{ type: 'sales' }],
      group: 'basic',
      description: 'Çekilişe bağlı kampanya (isteğe bağlı).',
    }),
    defineField({
      name: 'ticketPrice',
      title: 'Bilet Fiyatı',
      type: 'number',
      group: 'tickets',
      validation: (Rule) => Rule.required().min(0).max(10000).error('Bilet fiyatı 0 ile 10.000 arasında olmalıdır.'),
    }),
    defineField({
      name: 'currency',
      title: 'Para Birimi',
      type: 'string',
      group: 'tickets',
      options: {
        list: [
          { title: 'Türk Lirası (TRY)', value: 'TRY' },
          { title: 'ABD Doları (USD)', value: 'USD' },
          { title: 'Euro (EUR)', value: 'EUR' },
        ],
      },
      initialValue: 'TRY',
      validation: (Rule) => Rule.required().error('Para birimi zorunludur.'),
    }),
    defineField({
      name: 'totalTickets',
      title: 'Toplam Bilet Sayısı',
      type: 'number',
      group: 'tickets',
      validation: (Rule) => Rule.required().min(1).max(100000).error('Toplam bilet sayısı 1 ile 100.000 arasında olmalıdır.'),
    }),
    defineField({
      name: 'ticketsSold',
      title: 'Satılan Bilet Sayısı',
      type: 'number',
      group: 'tickets',
      initialValue: 0,
      readOnly: true,
      validation: (Rule) =>
        Rule.min(0)
          .max(Rule.valueOfField('totalTickets'))
          .error('Satılan bilet sayısı toplam bilet sayısını aşamaz.'),
    }),
    defineField({
      name: 'ticketSalePercentageForDraw',
      title: 'Çekiliş İçin Gerekli Satış Yüzdesi',
      type: 'number',
      group: 'tickets',
      initialValue: 80,
      validation: (Rule) => Rule.min(0).max(100).error('Satış yüzdesi 0 ile 100 arasında olmalıdır.'),
    }),
    defineField({
      name: 'numbersPerCard',
      title: 'Kart Başına Numara Adedi',
      type: 'number',
      group: 'tickets',
      initialValue: 3,
      validation: (Rule) => Rule.required().min(1).max(50).error('Kart başına numara adedi 1 ile 50 arasında olmalıdır.'),
    }),
    defineField({
      name: 'ticketDigitLength',
      title: 'Bilet Numarası Hane Sayısı',
      type: 'number',
      group: 'tickets',
      initialValue: 3,
      validation: (Rule) => Rule.required().min(3).max(6).error('Bilet numarası hane sayısı 3 ile 6 arasında olmalıdır.'),
    }),
    defineField({
      name: 'maxTicketsPerUser',
      title: 'Kullanıcı Başına Maksimum Bilet',
      type: 'number',
      group: 'tickets',
      validation: (Rule) => Rule.min(1).error('Kullanıcı başına en az 1 bilet olmalıdır.'),
      initialValue: 10,
    }),
    defineField({
      name: 'participants',
      title: 'Katılımcılar',
      type: 'array',
      group: 'tickets',
      of: [
        defineArrayMember({
          type: 'object',
          fields: [
            defineField({
              name: 'user',
              title: 'Kullanıcı',
              type: 'reference',
              to: [{ type: 'user' }],
              options: { filter: 'isAdminApproved == true' },
              validation: (Rule) => Rule.required().error('Kullanıcı seçimi zorunludur.'),
            }),
            defineField({
              name: 'tickets',
              title: 'Biletler',
              type: 'array',
              of: [
                defineArrayMember({
                  type: 'object',
                  fields: [
                    defineField({
                      name: 'ticketNumber',
                      title: 'Bilet Numarası',
                      type: 'string',
                      validation: (Rule) =>
                        Rule.required()
                          .regex(/^[0-9]+$/, { name: 'numeric' })
                          .custom((value, context) => {
                            const digitLength = context.document?.ticketDigitLength || 3;
                            if (value.length !== digitLength) {
                              return `Bilet numarası ${digitLength} haneli olmalıdır.`;
                            }
                            return true;
                          })
                          .error('Geçerli bir bilet numarası giriniz.'),
                    }),
                    defineField({
                      name: 'purchasedAt',
                      title: 'Satın Alınma Tarihi',
                      type: 'datetime',
                      initialValue: () => new Date().toISOString(),
                      validation: (Rule) => Rule.required(),
                    }),
                    defineField({
                      name: 'chosenDigitCount',
                      title: 'Seçilen Rakam Sayısı',
                      type: 'number',
                      validation: (Rule) =>
                        Rule.required()
                          .min(1)
                          .max(Rule.valueOfField('numbersPerCard'))
                          .error('Seçilen rakam sayısı kart başına numara adedini aşamaz.'),
                    }),
                    defineField({
                      name: 'status',
                      title: 'Durum',
                      type: 'string',
                      options: {
                        list: [
                          { title: 'Kazandınız', value: 'won' },
                          { title: 'Kazanamadınız', value: 'lost' },
                        ],
                      },
                      initialValue: 'lost',
                      validation: (Rule) => Rule.required(),
                    }),
                  ],
                }),
              ],
              validation: (Rule) => Rule.min(1).error('En az bir bilet eklenmelidir.'),
            }),
          ],
        }),
      ],
    }),
    defineField({
      name: 'prizes',
      title: 'Ödüller',
      type: 'array',
      group: 'prizes',
      of: [
        defineArrayMember({
          type: 'object',
          fields: [
            defineField({
              name: 'rank',
              title: 'Sıralama',
              type: 'number',
              validation: (Rule) => Rule.required().min(1).error('Sıralama 1 veya daha büyük olmalıdır.'),
            }),
            defineField({
              name: 'product',
              title: 'Ödül Ürün',
              type: 'reference',
              to: [{ type: 'product' }],
              description: 'Ödül olarak verilecek ürün (isteğe bağlı).',
            }),
            defineField({
              name: 'title',
              title: 'Ödül Başlığı',
              type: 'object',
              fields: [
                { name: 'tr', title: 'Türkçe', type: 'string', validation: (Rule) => Rule.required() },
                { name: 'en', title: 'İngilizce', type: 'string' },
              ],
            }),
            defineField({
              name: 'description',
              title: 'Ödül Açıklaması',
              type: 'object',
              fields: [
                { name: 'tr', title: 'Türkçe', type: 'text' },
                { name: 'en', title: 'İngilizce', type: 'text' },
              ],
            }),
            defineField({
              name: 'value',
              title: 'Ödül Değeri',
              type: 'number',
              validation: (Rule) => Rule.min(0).error('Ödül değeri sıfır veya pozitif olmalıdır.'),
            }),
            defineField({
              name: 'currency',
              title: 'Para Birimi',
              type: 'string',
              options: {
                list: [
                  { title: 'Türk Lirası (TRY)', value: 'TRY' },
                  { title: 'ABD Doları (USD)', value: 'USD' },
                  { title: 'Euro (EUR)', value: 'EUR' },
                ],
              },
              initialValue: 'TRY',
            }),
            defineImageField('Ödül Görseli'),
            defineField({
              name: 'prizeType',
              title: 'Ödül Türü',
              type: 'string',
              options: {
                list: [
                  { title: 'Ürün', value: 'product' },
                  { title: 'Nakit', value: 'cash' },
                  { title: 'Hediye Kartı', value: 'gift_card' },
                ],
              },
              initialValue: 'product',
              validation: (Rule) => Rule.required().error('Ödül türü zorunludur.'),
            }),
          ],
        }),
      ],
      validation: (Rule) => Rule.min(1).error('En az bir ödül eklenmelidir.'),
    }),
    defineField({
      name: 'status',
      title: 'Durum',
      type: 'string',
      group: 'status',
      options: {
        list: [
          { title: 'Bekliyor', value: 'pending' },
          { title: 'Aktif', value: 'active' },
          { title: 'Tamamlandı', value: 'completed' },
          { title: 'İptal', value: 'cancelled' },
        ],
      },
      initialValue: 'pending',
      validation: (Rule) => Rule.required().error('Durum zorunludur.'),
    }),
    defineField({
      name: 'startDate',
      title: 'Başlangıç Tarihi',
      type: 'datetime',
      group: 'status',
      validation: (Rule) => Rule.required().error('Başlangıç tarihi zorunludur.'),
    }),
    defineField({
      name: 'endDate',
      title: 'Bitiş Tarihi',
      type: 'datetime',
      group: 'status',
      validation: (Rule) =>
        Rule.required()
          .min(Rule.valueOfField('startDate'))
          .error('Bitiş tarihi, başlangıç tarihinden sonra olmalıdır.'),
    }),
    defineField({
      name: 'drawDate',
      title: 'Çekiliş Tarihi',
      type: 'datetime',
      group: 'status',
      description: 'Çekilişin yapılacağı tarih.',
      validation: (Rule) =>
        Rule.min(Rule.valueOfField('endDate')).error('Çekiliş tarihi, bitiş tarihinden sonra olmalıdır.'),
    }),
    defineField({
      name: 'winningNumbers',
      title: 'Kazanan Numaralar',
      type: 'array',
      group: 'status',
      of: [{ type: 'string' }],
      description: 'Çekiliş sonrası belirlenen kazanan numaralar.',
      validation: (Rule) =>
        Rule.custom((numbers, context) => {
          if (!numbers || !context.document?.ticketDigitLength) return true;
          const digitLength = context.document.ticketDigitLength;
          for (const number of numbers) {
            if (number.length !== digitLength) {
              return `Kazanan numaralar ${digitLength} haneli olmalıdır.`;
            }
          }
          return true;
        }),
    }),
    defineField({
      name: 'winners',
      title: 'Kazananlar',
      type: 'array',
      group: 'status',
      of: [
        defineArrayMember({
          type: 'object',
          fields: [
            defineField({
              name: 'user',
              title: 'Kazanan Kullanıcı',
              type: 'reference',
              to: [{ type: 'user' }],
              options: { filter: 'isAdminApproved == true' },
              validation: (Rule) => Rule.required().error('Kazanan kullanıcı zorunludur.'),
            }),
            defineField({
              name: 'ticketNumber',
              title: 'Kazanan Bilet Numarası',
              type: 'string',
              validation: (Rule) => Rule.required().error('Kazanan bilet numarası zorunludur.'),
            }),
            defineField({
              name: 'prize',
              title: 'Kazanılan Ödül',
              type: 'string',
              validation: (Rule) => Rule.required().error('Kazanılan ödül zorunludur.'),
              description: 'Kazanılan ödülün adı veya açıklaması',
            }),
            defineField({
              name: 'rank',
              title: 'Kazanma Sırası',
              type: 'number',
              validation: (Rule) => Rule.required().min(1).error('Kazanma sırası 1 veya daha büyük olmalıdır.'),
            }),
          ],
        }),
      ],
    }),
  ],
  preview: {
    select: {
      title: 'title.tr',
      status: 'status',
      ticketsSold: 'ticketsSold',
      totalTickets: 'totalTickets',
      endDate: 'endDate',
      media: 'image',
    },
    prepare({ title, status, ticketsSold, totalTickets, endDate, media }) {
      return {
        title: title || 'İsimsiz Çekiliş',
        subtitle: `${status || 'Durum Yok'} - ${ticketsSold || 0}/${totalTickets || 0} bilet satıldı - Bitiş: ${
          endDate ? new Date(endDate).toLocaleString('tr-TR') : 'Tanımsız'
        }`,
        media,
      };
    },
  },
});