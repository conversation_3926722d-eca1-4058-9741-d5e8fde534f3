import { getActiveSaleByCouponCode } from "@/sanity/lib/sales/getAciveSaleByCouponCode";
import { couponCode } from "@/sanity/lib/sales/couponCodes";

const BlackFridayBanner = async () => {
  const sale = await getActiveSaleByCouponCode(
    couponCode.BLACKFRIDAY2024   
  );

  if (!sale?.isActive) return null;

  return (
    <div
      className="bg-gradient-to-r from-red-600 to-black text-white px-6 py-10
        mx-4 mt-2 rounded-lg shadow-lg"
    >
      <div className="flex flex-col items-center justify-center">
        <div className="flex-1">
          <h2 className="text-3xl sm:text-5xl font-extrabold text-left mb-4">
            {sale.title}
          </h2>
          <p className="text-left text-lg sm:text-xl text-gray-300">
            Black Friday Sale: {sale.description}
          </p>

          <div className="flex">
            <div className="bg-white text-black px-4 py-2 rounded-full shadow-md transform hover:scale-105 transition-transform duration-300">
              <span className="font-bold test-base sm:text-xl">
                Use Code:{" "}
                <span className="text-red-600">{sale.couponCode}</span>
              </span>
              <span className="ml-2 font-bold text-base sm:text-xl">
                For {sale.discountAmount}% OFF
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlackFridayBanner;
