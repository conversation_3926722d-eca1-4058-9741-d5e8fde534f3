/**
 * This route is responsible for the built-in authoring environment using Sanity Studio.
 * All routes under your studio path is handled by this file using Next.js' catch-all routes:
 * https://nextjs.org/docs/routing/dynamic-routes#catch-all-routes
 *
 * You can learn more about the next-sanity package here:
 * https://github.com/sanity-io/next-sanity
 */

import { NextStudio } from 'next-sanity/studio'
import config from '../../../sanity.config'
import ErrorSuppression from '../../../components/ErrorSuppression'

export const dynamic = 'force-static'

export { metadata, viewport } from 'next-sanity/studio'

export default function StudioPage() {
  // Geçici olarak devre dışı - chunk loading sorunu
  if (process.env.NODE_ENV === 'development') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Sanity Studio</h1>
          <p className="text-gray-600 mb-4">
            Studio geçici olarak devre dışı. Chunk loading sorunu çözülüyor.
          </p>
          <p className="text-sm text-gray-500">
            Sanity verilerinize erişmek için: <br />
            <code className="bg-gray-100 px-2 py-1 rounded">
              https://mz841rpk.sanity.studio
            </code>
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
      <ErrorSuppression />
      <NextStudio config={config} />
    </>
  )
}
