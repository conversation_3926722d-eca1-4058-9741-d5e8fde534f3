import { useQuery } from '@tanstack/react-query';
import { api } from '../lib/api';
import { 
  Auction, 
  AuctionWithDetails,
  ApiResponse 
} from '../types/sanity';

// Query parameters interface
export interface AuctionQueryParams {
  status?: 'upcoming' | 'active' | 'completed' | 'cancelled';
  minBid?: number;
  maxBid?: number;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: 'startTime' | 'endTime' | 'currentBid' | 'startingBid';
  sortOrder?: 'asc' | 'desc';
  language?: 'tr' | 'en';
}

// API functions
const fetchAuctions = async (params?: AuctionQueryParams): Promise<Auction[]> => {
  const response = await api.get('/auctions', params);
  return response.data;
};

const fetchAuctionById = async (id: string): Promise<Auction | null> => {
  const response = await api.get(`/auctions/${id}`);
  return response.data;
};

const fetchActiveAuctions = async (): Promise<Auction[]> => {
  const response = await api.get('/auctions/active');
  return response.data;
};

const fetchUpcomingAuctions = async (): Promise<Auction[]> => {
  const response = await api.get('/auctions/upcoming');
  return response.data;
};

const fetchCompletedAuctions = async (): Promise<Auction[]> => {
  const response = await api.get('/auctions/completed');
  return response.data;
};

const fetchUserBids = async (userId: string): Promise<Auction[]> => {
  const response = await api.get(`/auctions/user-bids/${userId}`);
  return response.data;
};

const placeBid = async (auctionId: string, bidAmount: number, userId: string): Promise<{ success: boolean; message?: string }> => {
  const response = await api.post(`/auctions/${auctionId}/bid`, { bidAmount, userId });
  return response.data;
};

// React Query hooks
export const useAuctions = (params?: AuctionQueryParams) => {
  return useQuery<Auction[], Error>({
    queryKey: ['auctions', params],
    queryFn: () => fetchAuctions(params),
    staleTime: 2 * 60 * 1000, // 2 minutes for auctions
    retry: 2,
    refetchOnWindowFocus: true, // Important for real-time bidding
  });
};

export const useAuction = (id: string) => {
  return useQuery<Auction | null, Error>({
    queryKey: ['auction', id],
    queryFn: () => fetchAuctionById(id),
    staleTime: 30 * 1000, // 30 seconds for individual auction
    retry: 2,
    enabled: !!id,
    refetchInterval: 30 * 1000, // Auto-refresh every 30 seconds
  });
};

export const useActiveAuctions = () => {
  return useQuery<Auction[], Error>({
    queryKey: ['auctions', 'active'],
    queryFn: fetchActiveAuctions,
    staleTime: 1 * 60 * 1000, // 1 minute for active auctions
    retry: 2,
    refetchInterval: 1 * 60 * 1000, // Auto-refresh every minute
  });
};

export const useUpcomingAuctions = () => {
  return useQuery<Auction[], Error>({
    queryKey: ['auctions', 'upcoming'],
    queryFn: fetchUpcomingAuctions,
    staleTime: 5 * 60 * 1000, // 5 minutes for upcoming auctions
    retry: 2,
  });
};

export const useCompletedAuctions = () => {
  return useQuery<Auction[], Error>({
    queryKey: ['auctions', 'completed'],
    queryFn: fetchCompletedAuctions,
    staleTime: 10 * 60 * 1000, // 10 minutes for completed auctions
    retry: 2,
  });
};

export const useUserBids = (userId: string) => {
  return useQuery<Auction[], Error>({
    queryKey: ['auctions', 'user-bids', userId],
    queryFn: () => fetchUserBids(userId),
    staleTime: 2 * 60 * 1000,
    retry: 2,
    enabled: !!userId,
  });
};

// Utility hooks
export const useAuctionStatuses = () => {
  const statuses = [
    { value: 'upcoming', label: 'Yaklaşan', color: 'blue' },
    { value: 'active', label: 'Aktif', color: 'green' },
    { value: 'completed', label: 'Tamamlandı', color: 'gray' },
    { value: 'cancelled', label: 'İptal Edildi', color: 'red' },
  ] as const;

  return statuses;
};

// Helper functions
export const getLocalizedAuctionDescription = (
  description: { tr?: string; en?: string } | undefined,
  language: 'tr' | 'en' = 'tr'
): string => {
  if (!description) return '';
  return language === 'en' && description.en ? description.en : description.tr || '';
};

export const isAuctionActive = (auction: Auction): boolean => {
  const now = new Date();
  const startTime = new Date(auction.startTime);
  const endTime = new Date(auction.endTime);
  
  return auction.status === 'active' && now >= startTime && now <= endTime;
};

export const isAuctionUpcoming = (auction: Auction): boolean => {
  const now = new Date();
  const startTime = new Date(auction.startTime);
  
  return auction.status === 'upcoming' && now < startTime;
};

export const isAuctionCompleted = (auction: Auction): boolean => {
  const now = new Date();
  const endTime = new Date(auction.endTime);
  
  return auction.status === 'completed' || now > endTime;
};

export const getAuctionTimeRemaining = (auction: Auction): { days: number; hours: number; minutes: number; seconds: number } => {
  const now = new Date();
  const endTime = new Date(auction.endTime);
  const diff = endTime.getTime() - now.getTime();
  
  if (diff <= 0) {
    return { days: 0, hours: 0, minutes: 0, seconds: 0 };
  }
  
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((diff % (1000 * 60)) / 1000);
  
  return { days, hours, minutes, seconds };
};

export const formatBidAmount = (amount: number, currency: string = 'TRY'): string => {
  const symbols = { TRY: '₺', USD: '$', EUR: '€' };
  const symbol = symbols[currency as keyof typeof symbols] || currency;
  
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: currency,
    currencyDisplay: 'symbol',
  }).format(amount).replace(currency, symbol);
};

export const canPlaceBid = (auction: Auction, bidAmount: number, userId?: string): { canBid: boolean; reason?: string } => {
  if (!isAuctionActive(auction)) {
    return { canBid: false, reason: 'Açık artırma aktif değil' };
  }
  
  if (bidAmount <= auction.currentBid) {
    return { canBid: false, reason: 'Teklif mevcut tekliften yüksek olmalıdır' };
  }
  
  if (bidAmount < auction.currentBid + auction.bidIncrementAmount) {
    return { canBid: false, reason: `Minimum artış miktarı: ${formatBidAmount(auction.bidIncrementAmount, auction.currency)}` };
  }
  
  if (auction.winner && auction.winner._ref === userId) {
    return { canBid: false, reason: 'Zaten en yüksek teklifi verdiniz' };
  }
  
  return { canBid: true };
};

export const getAuctionStatusColor = (status: string): string => {
  const statusColors: Record<string, string> = {
    upcoming: '#3b82f6',
    active: '#10b981',
    completed: '#6b7280',
    cancelled: '#ef4444',
  };
  return statusColors[status] || '#6b7280';
};

export const getUserBidCount = (auction: Auction, userId: string): number => {
  if (!auction.bidHistory) return 0;
  return auction.bidHistory.filter(bid => bid.bidder._ref === userId).length;
};

export const getUserHighestBid = (auction: Auction, userId: string): number => {
  if (!auction.bidHistory) return 0;
  const userBids = auction.bidHistory.filter(bid => bid.bidder._ref === userId);
  return userBids.length > 0 ? Math.max(...userBids.map(bid => bid.bidAmount)) : 0;
};

export const isUserWinning = (auction: Auction, userId: string): boolean => {
  return auction.winner?._ref === userId;
};

// Filter and sort helpers
export const filterAuctionsByStatus = (auctions: Auction[], status: string): Auction[] => {
  return auctions.filter(auction => auction.status === status);
};

export const sortAuctions = (
  auctions: Auction[], 
  sortBy: 'startTime' | 'endTime' | 'currentBid' | 'startingBid' = 'endTime',
  sortOrder: 'asc' | 'desc' = 'asc'
): Auction[] => {
  return [...auctions].sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'startTime':
        comparison = new Date(a.startTime).getTime() - new Date(b.startTime).getTime();
        break;
      case 'endTime':
        comparison = new Date(a.endTime).getTime() - new Date(b.endTime).getTime();
        break;
      case 'currentBid':
        comparison = a.currentBid - b.currentBid;
        break;
      case 'startingBid':
        comparison = a.startingBid - b.startingBid;
        break;
    }
    
    return sortOrder === 'desc' ? -comparison : comparison;
  });
};
