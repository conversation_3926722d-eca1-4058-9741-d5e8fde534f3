'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FiChevronLeft, FiChevronRight } from 'react-icons/fi';
import { client } from '@/sanity/lib/client';
import { Product } from '@/sanity.types';
import { urlForImage } from '@/sanity/lib/image';

export default function DynamicBanners() {
  const [products, setProducts] = useState<Product[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchProducts();
  }, []);

  // 5 saniyede bir otomatik geçiş
  useEffect(() => {
    if (products.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) =>
        prevIndex === products.length - 1 ? 0 : prevIndex + 1
      );
    }, 3000);

    return () => clearInterval(interval);
  }, [products.length]);

  const fetchProducts = async () => {
    const ALL_PRODUCTS_QUERY = `*[_type == "product"] {
      _id,
      _type,
      name,
      "slug": slug.current,
      image,
      price,
      description,
      category[]->{
          _id,
          title,
          "slug": slug.current
      },
      isFeatured,
      stock
    }`;

    try {
      const fetchedProducts = await client.fetch(ALL_PRODUCTS_QUERY);

      if (Array.isArray(fetchedProducts)) {
        // Eğer isFeatured alanı varsa onu kullan, yoksa tüm ürünleri göster
        const activeProducts = fetchedProducts.filter((product: Product) => product.isFeatured === true || !('isFeatured' in product));
        setProducts(activeProducts);
        setCurrentIndex(0); // Ürünler yüklendikten sonra ilkine dön
      }
    } catch (err) {
      console.error('Ürün yükleme hatası:', err);
      setError('Ürünler yüklenirken bir hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const goToPrevious = () => {
    setCurrentIndex(currentIndex === 0 ? products.length - 1 : currentIndex - 1);
  };

  const goToNext = () => {
    setCurrentIndex(currentIndex === products.length - 1 ? 0 : currentIndex + 1);
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  if (loading) {
    return (
      <section className="py-8">
        <div className="max-w-7xl mx-auto px-4">
          <div className="bg-gray-200 animate-pulse rounded-lg h-64"></div>
        </div>
      </section>
    );
  }

  if (error || products.length === 0) {
    return null; // Hata varsa veya ürün yoksa hiçbir şey gösterme
  }

  const currentProduct = products[currentIndex];

  return (
    <section className="relative overflow-hidden">
      <div className="max-w-full mx-auto ">
        <div className="relative w-full h-[250px] md:h-[350px] lg:h-[350px] flex items-center justify-center 
        rounded-lg shadow-2xl bg-gradient-to-br from-blue-400 to-red-600  md:p-8">
          {/* Ürün Kartı */} 
          <div className="bg-white rounded-lg shadow-xl overflow-hidden flex flex-col md:flex-row w-full max-w-4xl h-full transform transition-transform duration-500 scale-95 opacity-90 hover:scale-100 hover:opacity-100 relative z-10">
            {/* Ürün Görseli */} 
            <div className="relative w-full md:w-1/2 h-64 md:h-full bg-gray-100 flex items-center justify-center">
              {currentProduct.image && (
                <Image
                  src={urlForImage(currentProduct.image).url()}
                  alt={currentProduct.name || 'Ürün Görseli'}
                  layout="fill"
                  objectFit="contain"
                  className="p-4"
                  priority
                />
              )}
            </div>

            {/* Ürün Detayları */} 
            <div className="w-full md:w-1/2 p-6 flex flex-col justify-center text-gray-800">
              <h2 className="text-3xl md:text-4xl font-bold mb-3 leading-tight">
                {currentProduct.name}
              </h2>
              <p className="text-lg md:text-xl text-gray-600 mb-4">
                {currentProduct.description}
              </p>
              <div className="text-2xl md:text-3xl font-extrabold text-blue-600 mb-6">
                {currentProduct.price ? `$${currentProduct.price.toFixed(2)}` : 'Fiyat Belirtilmemiş'}
              </div>
              <Link
                href={`/product/${currentProduct.slug}`}
                className="inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold text-base hover:bg-blue-700 transition-colors duration-300 shadow-md transform hover:scale-105"
              >
                Detayları Gör
                <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Link>
            </div>
          </div>

          {/* Navigasyon Okları */} 
          {products.length > 1 && (
            <>
              <button
                onClick={goToPrevious}
                className="absolute left-6 top-1/2 transform -translate-y-1/2 p-3 rounded-full bg-black bg-opacity-30 hover:bg-opacity-50 text-white transition-all z-20"
                aria-label="Önceki ürün"
              >
                <FiChevronLeft size={28} />
              </button>

              <button
                onClick={goToNext}
                className="absolute right-6 top-1/2 transform -translate-y-1/2 p-3 rounded-full bg-black bg-opacity-30 hover:bg-opacity-50 text-white transition-all z-20"
                aria-label="Sonraki ürün"
              >
                <FiChevronRight size={28} />
              </button>
            </>
          )}
        </div>

        {/* Nokta İndikatörleri */} 
        {products.length > 1 && (
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-2 z-20">
            {products.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${ 
                  index === currentIndex
                    ? 'bg-white scale-125'
                    : 'bg-white bg-opacity-50 hover:bg-opacity-75 hover:scale-110'
                }`}
                aria-label={`Ürün ${index + 1}'e git`}
              />
            ))}
          </div>
        )}

        {/* Ürün Sayısı Göstergesi */} 
        {products.length > 1 && (
          <div className="text-center mt-6 px-4">
            <span className="text-base text-gray-600 bg-white bg-opacity-80 px-3 py-1 rounded-full shadow-sm">
              {currentIndex + 1} / {products.length}
            </span>
          </div>
        )}
      </div>
    </section>
  );
}
