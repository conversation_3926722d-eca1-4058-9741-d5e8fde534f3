export function formatCurrency(
    amount: number, 
    currencyCode: string = 'USD'
) {
    
    try{
        return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currencyCode.toUpperCase(),
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
    }).format(amount);
}catch(error){
    console.error('invalid currency code:',currencyCode , error);
    return `$${currencyCode.toUpperCase()} ${amount.toFixed(2)}`;
}
}