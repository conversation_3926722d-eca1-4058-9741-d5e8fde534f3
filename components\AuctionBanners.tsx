'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FiChevronLeft, FiChevronRight } from 'react-icons/fi';
import { getActiveAuctions } from '@/sanity/lib/auctions/getAuctions'; // Import auction fetching function
import { Auction } from '@/sanity.types'; // Import Auction type
import { urlForImage } from '@/sanity/lib/image';

export default function AuctionBanners() {
  const [auctions, setAuctions] = useState<Auction[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchAuctions();
  }, []);

  // 5 saniyede bir otomatik geçiş
  useEffect(() => {
    if (auctions.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) =>
        prevIndex === auctions.length - 1 ? 0 : prevIndex + 1
      );
    }, 3000);

    return () => clearInterval(interval);
  }, [auctions.length]);

  const fetchAuctions = async () => {
    try {
      const fetchedAuctions = await getActiveAuctions(); // Fetch active auctions

      if (Array.isArray(fetchedAuctions)) {
        setAuctions(fetchedAuctions);
        setCurrentIndex(0); // Auctions loaded, reset to first
      }
    } catch (err) {
      console.error('Açık artırma yükleme hatası:', err);
      setError('Açık artırmalar yüklenirken bir hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const goToPrevious = () => {
    setCurrentIndex(currentIndex === 0 ? auctions.length - 1 : currentIndex - 1);
  };

  const goToNext = () => {
    setCurrentIndex(currentIndex === auctions.length - 1 ? 0 : currentIndex + 1);
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  if (loading) {
    return (
      <section className="py-8">
        <div className="max-w-7xl mx-auto px-4">
          <div className="bg-gray-200 animate-pulse rounded-lg h-64"></div>
        </div>
      </section>
    );
  }

  if (error) {
    return <section className="py-8"><div className="max-w-7xl mx-auto px-4 text-center text-red-500">Hata oluştu: {error}</div></section>;
  }

  if (auctions.length === 0) {
    return <section className="py-8"><div className="max-w-7xl mx-auto px-4 text-center text-gray-500">Şu anda aktif açık artırma bulunmamaktadır.</div></section>;
  }

  const currentAuction = auctions[currentIndex];

  return (
    <section className="relative overflow-hidden mb-8">
      <div className="max-w-full mx-auto ">
        <div className="relative w-full h-[250px] md:h-[350px] lg:h-[350px] flex items-center justify-center 
        rounded-lg shadow-2xl bg-gradient-to-br from-purple-400 to-indigo-600  md:p-8">
          {/* Auction Card */} 
          <div className="bg-white rounded-lg shadow-xl overflow-hidden flex flex-col md:flex-row w-full max-w-4xl h-full transform transition-transform duration-500 scale-95 opacity-90 hover:scale-100 hover:opacity-100 relative z-10">
            {/* Auction Image */} 
            <div className="relative w-full md:w-1/2 h-64 md:h-full bg-gray-100 flex items-center justify-center">
              {currentAuction.image ? (
                <Image
                  src={urlForImage(currentAuction.image).url()}
                  alt={currentAuction.name || 'Açık Artırma Görseli'}
                  layout="fill"
                  objectFit="contain"
                  className="p-4"
                  priority
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-gray-400">
                  <div className="text-center">
                    <div className="text-6xl mb-2">🏷️</div>
                    <p className="text-sm">Görsel Yükleniyor</p>
                  </div>
                </div>
              )}
            </div>

            {/* Auction Details */} 
            <div className="w-full md:w-1/2 p-6 flex flex-col justify-center text-gray-800">
              <h2 className="text-3xl md:text-4xl font-bold mb-3 leading-tight">
                {currentAuction.name}
              </h2>
              <p className="text-lg md:text-xl text-gray-600 mb-4">
                {currentAuction.description}
              </p>
              <div className="text-2xl md:text-3xl font-extrabold text-indigo-600 mb-6">
                Güncel Teklif: {currentAuction.currentBid ? `₺${currentAuction.currentBid.toFixed(2)}` : 'Teklif Belirtilmemiş'}
              </div>
              <Link
                href={`/auctions/${currentAuction.id}`} // Assuming auction IDs are used for routing
                className="inline-flex items-center justify-center px-6 py-3 bg-indigo-600 text-white rounded-lg font-semibold text-base hover:bg-indigo-700 transition-colors duration-300 shadow-md transform hover:scale-105"
              >
                Teklif Ver
                <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Link>
            </div>
          </div>

          {/* Navigation Arrows */} 
          {auctions.length > 1 && (
            <>
              <button
                onClick={goToPrevious}
                className="absolute left-6 top-1/2 transform -translate-y-1/2 p-3 rounded-full bg-black bg-opacity-30 hover:bg-opacity-50 text-white transition-all z-20"
                aria-label="Önceki açık artırma"
              >
                <FiChevronLeft size={28} />
              </button>

              <button
                onClick={goToNext}
                className="absolute right-6 top-1/2 transform -translate-y-1/2 p-3 rounded-full bg-black bg-opacity-30 hover:bg-opacity-50 text-white transition-all z-20"
                aria-label="Sonraki açık artırma"
              >
                <FiChevronRight size={28} />
              </button>
            </>
          )}
        </div>

        {/* Dot Indicators */} 
        {auctions.length > 1 && (
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-2 z-20">
            {auctions.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${ 
                  index === currentIndex
                    ? 'bg-white scale-125'
                    : 'bg-white bg-opacity-50 hover:bg-opacity-75 hover:scale-110'
                }`}
                aria-label={`Açık artırma ${index + 1}'e git`}
              />
            ))}
          </div>
        )}

        {/* Auction Count Indicator */} 
        {auctions.length > 1 && (
          <div className="text-center mt-6 px-4">
            <span className="text-base text-gray-600 bg-white bg-opacity-80 px-3 py-1 rounded-full shadow-sm">
              {currentIndex + 1} / {auctions.length}
            </span>
          </div>
        )}
      </div>
    </section>
  );
} 