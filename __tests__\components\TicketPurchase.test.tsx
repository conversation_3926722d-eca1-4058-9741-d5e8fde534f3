import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useUser } from '@clerk/nextjs'
import { useStripe, useElements } from '@stripe/react-stripe-js'
import TicketPurchase from '@/components/TicketPurchase'

// Mock dependencies
jest.mock('@clerk/nextjs')
jest.mock('@stripe/react-stripe-js')

const mockUseUser = useUser as jest.MockedFunction<typeof useUser>
const mockUseStripe = useStripe as jest.MockedFunction<typeof useStripe>
const mockUseElements = useElements as jest.MockedFunction<typeof useElements>

// Mock fetch
global.fetch = jest.fn()

// Mock Sanity client
jest.mock('@sanity/client', () => {
  return jest.fn().mockImplementation(() => ({
    fetch: jest.fn()
  }))
})

describe('TicketPurchase Component', () => {
  const defaultProps = {
    drawId: null,
    ticketPrice: null,
    ticketDigitCount: null,
    ticketNumberCount: null,
    clientSecret: null,
    ticketNumbers: [],
    setClientSecret: jest.fn(),
    setTicketNumbers: jest.fn(),
  }

  beforeEach(() => {
    jest.clearAllMocks()
    
    // Default mocks
    mockUseUser.mockReturnValue({
      user: {
        id: 'user-123',
        fullName: 'Test User'
      },
      isLoaded: true
    } as any)

    mockUseStripe.mockReturnValue({
      confirmPayment: jest.fn()
    } as any)

    mockUseElements.mockReturnValue({} as any)

    // Mock Sanity client fetch
    const mockSanityFetch = jest.fn().mockResolvedValue([
      {
        _id: 'draw-1',
        name: 'Test Draw',
        drawDate: '2024-12-31T00:00:00Z',
        ticketDigitCount: 3,
        ticketNumberCount: 3,
        ticketPrice: 10
      }
    ])

    // Mock the sanity client
    jest.doMock('@sanity/client', () => {
      return jest.fn().mockImplementation(() => ({
        fetch: mockSanityFetch
      }))
    })
  })

  it('should render loading state initially', () => {
    render(<TicketPurchase {...defaultProps} />)
    expect(screen.getByText('Çekilişler yükleniyor...')).toBeInTheDocument()
  })

  it('should show login message when user is not authenticated', async () => {
    mockUseUser.mockReturnValue({
      user: null,
      isLoaded: true
    } as any)

    render(<TicketPurchase {...defaultProps} />)

    await waitFor(() => {
      expect(screen.getByText('Bilet satın almak için giriş yapmalısınız.')).toBeInTheDocument()
    })
  })

  it('should display available draws when loaded', async () => {
    render(<TicketPurchase {...defaultProps} />)

    await waitFor(() => {
      expect(screen.getByText('Test Draw (31.12.2024) - 10 TL')).toBeInTheDocument()
    })
  })

  it('should show no draws message when no active draws', async () => {
    ;(global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve([])
    })

    render(<TicketPurchase {...defaultProps} />)

    await waitFor(() => {
      expect(screen.getByText('Şu anda aktif bir çekiliş bulunmamaktadır.')).toBeInTheDocument()
    })
  })

  it('should handle draw selection', async () => {
    render(<TicketPurchase {...defaultProps} />)

    await waitFor(() => {
      const select = screen.getByLabelText('Çekiliş Seçin:')
      expect(select).toBeInTheDocument()
    })

    const select = screen.getByLabelText('Çekiliş Seçin:')
    fireEvent.change(select, { target: { value: 'draw-1' } })

    await waitFor(() => {
      expect(screen.getByText('Biletteki sayıların rakam sayısı: 3')).toBeInTheDocument()
      expect(screen.getByText('Bilet başına sayı adedi: 3')).toBeInTheDocument()
      expect(screen.getByText('Bilet fiyatı: 10 TL')).toBeInTheDocument()
    })
  })

  it('should handle ticket creation', async () => {
    ;(global.fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve([
          {
            _id: 'draw-1',
            name: 'Test Draw',
            drawDate: '2024-12-31T00:00:00Z',
            ticketDigitCount: 3,
            ticketNumberCount: 3,
            ticketPrice: 10
          }
        ])
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          clientSecret: 'pi_test_client_secret',
          numbers: [123, 456, 789],
          drawId: 'draw-1',
          price: 10,
          ticketDigitCount: 3,
          ticketNumberCount: 3
        })
      })

    const setClientSecret = jest.fn()
    const setTicketNumbers = jest.fn()

    render(
      <TicketPurchase 
        {...defaultProps} 
        setClientSecret={setClientSecret}
        setTicketNumbers={setTicketNumbers}
      />
    )

    await waitFor(() => {
      const button = screen.getByText('Bilet Oluştur ve Satın Al')
      expect(button).toBeInTheDocument()
    })

    const button = screen.getByText('Bilet Oluştur ve Satın Al')
    fireEvent.click(button)

    await waitFor(() => {
      expect(setClientSecret).toHaveBeenCalledWith('pi_test_client_secret')
      expect(setTicketNumbers).toHaveBeenCalledWith([123, 456, 789])
    })
  })

  it('should handle ticket creation error', async () => {
    ;(global.fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve([
          {
            _id: 'draw-1',
            name: 'Test Draw',
            drawDate: '2024-12-31T00:00:00Z',
            ticketDigitCount: 3,
            ticketNumberCount: 3,
            ticketPrice: 10
          }
        ])
      })
      .mockResolvedValueOnce({
        ok: false,
        json: () => Promise.resolve({
          error: 'Bilet oluşturulamadı'
        })
      })

    render(<TicketPurchase {...defaultProps} />)

    await waitFor(() => {
      const button = screen.getByText('Bilet Oluştur ve Satın Al')
      expect(button).toBeInTheDocument()
    })

    const button = screen.getByText('Bilet Oluştur ve Satın Al')
    fireEvent.click(button)

    await waitFor(() => {
      expect(screen.getByText('Bilet oluşturulamadı')).toBeInTheDocument()
    })
  })

  it('should show payment form when ticket is created', () => {
    const propsWithTicket = {
      ...defaultProps,
      clientSecret: 'pi_test_client_secret',
      ticketNumbers: [123, 456, 789],
      ticketPrice: 10,
      ticketDigitCount: 3,
      ticketNumberCount: 3
    }

    render(<TicketPurchase {...propsWithTicket} />)

    expect(screen.getByText('Oluşturulan Bilet Numaraları:')).toBeInTheDocument()
    expect(screen.getByText('123')).toBeInTheDocument()
    expect(screen.getByText('456')).toBeInTheDocument()
    expect(screen.getByText('789')).toBeInTheDocument()
    expect(screen.getByText('Fiyat: 10 TL')).toBeInTheDocument()
  })

  it('should show success message after payment', () => {
    const propsWithSuccess = {
      ...defaultProps,
      drawId: 'draw-1',
      ticketNumbers: [123, 456, 789]
    }

    render(<TicketPurchase {...propsWithSuccess} />)

    // Simulate payment success by setting the state
    const component = screen.getByText('Çekiliş Bileti Satın Al').closest('div')
    
    // This would normally be set by the payment flow
    // For testing, we can check if the component structure supports it
    expect(component).toBeInTheDocument()
  })
})
