// Sentry Configuration and Error Tracking
import * as Sentry from '@sentry/nextjs';
import { User } from '@/types/sanity';

// Sentry configuration
export const sentryConfig = {
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
  debug: process.env.NODE_ENV === 'development',
  integrations: [
    new Sentry.BrowserTracing({
      tracePropagationTargets: [
        'localhost',
        /^https:\/\/yourapp\.com\/api/,
      ],
    }),
  ],
  beforeSend(event, hint) {
    // Filter out certain errors in production
    if (process.env.NODE_ENV === 'production') {
      // Don't send network errors for certain endpoints
      if (event.exception?.values?.[0]?.value?.includes('Network Error')) {
        return null;
      }
      
      // Don't send cancelled requests
      if (event.exception?.values?.[0]?.value?.includes('AbortError')) {
        return null;
      }
    }
    
    return event;
  },
};

// Initialize Sentry
export const initSentry = () => {
  Sentry.init(sentryConfig);
};

// Error tracking utilities
export class ErrorTracker {
  // Set user context
  static setUser(user: Partial<User>) {
    Sentry.setUser({
      id: user._id,
      email: user.email,
      username: user.name?.tr || user.name?.en,
    });
  }

  // Clear user context
  static clearUser() {
    Sentry.setUser(null);
  }

  // Set custom context
  static setContext(key: string, context: Record<string, any>) {
    Sentry.setContext(key, context);
  }

  // Add breadcrumb
  static addBreadcrumb(message: string, category: string, level: Sentry.SeverityLevel = 'info', data?: Record<string, any>) {
    Sentry.addBreadcrumb({
      message,
      category,
      level,
      data,
      timestamp: Date.now() / 1000,
    });
  }

  // Capture exception
  static captureException(error: Error, context?: Record<string, any>) {
    if (context) {
      Sentry.withScope((scope) => {
        Object.entries(context).forEach(([key, value]) => {
          scope.setContext(key, value);
        });
        Sentry.captureException(error);
      });
    } else {
      Sentry.captureException(error);
    }
  }

  // Capture message
  static captureMessage(message: string, level: Sentry.SeverityLevel = 'info', context?: Record<string, any>) {
    if (context) {
      Sentry.withScope((scope) => {
        Object.entries(context).forEach(([key, value]) => {
          scope.setContext(key, value);
        });
        Sentry.captureMessage(message, level);
      });
    } else {
      Sentry.captureMessage(message, level);
    }
  }

  // Track performance
  static startTransaction(name: string, op: string) {
    return Sentry.startTransaction({ name, op });
  }

  // Track API calls
  static trackApiCall(endpoint: string, method: string, duration: number, status: number, error?: Error) {
    const transaction = Sentry.startTransaction({
      name: `${method} ${endpoint}`,
      op: 'http.client',
    });

    transaction.setData('http.method', method);
    transaction.setData('http.url', endpoint);
    transaction.setData('http.status_code', status);
    transaction.setData('duration', duration);

    if (error) {
      transaction.setStatus('internal_error');
      this.captureException(error, {
        api: {
          endpoint,
          method,
          status,
          duration,
        },
      });
    } else {
      transaction.setStatus('ok');
    }

    transaction.finish();
  }
}

// Business logic error tracking
export class BusinessErrorTracker {
  // Track auction errors
  static trackAuctionError(error: Error, auctionId: string, userId?: string, action?: string) {
    ErrorTracker.captureException(error, {
      auction: {
        id: auctionId,
        userId,
        action,
      },
      business_context: 'auction',
    });
  }

  // Track giveaway errors
  static trackGiveawayError(error: Error, giveawayId: string, userId?: string, action?: string) {
    ErrorTracker.captureException(error, {
      giveaway: {
        id: giveawayId,
        userId,
        action,
      },
      business_context: 'giveaway',
    });
  }

  // Track order errors
  static trackOrderError(error: Error, orderId?: string, userId?: string, action?: string) {
    ErrorTracker.captureException(error, {
      order: {
        id: orderId,
        userId,
        action,
      },
      business_context: 'order',
    });
  }

  // Track payment errors
  static trackPaymentError(error: Error, paymentData: Record<string, any>) {
    ErrorTracker.captureException(error, {
      payment: {
        ...paymentData,
        // Remove sensitive data
        cardNumber: undefined,
        cvv: undefined,
      },
      business_context: 'payment',
    });
  }

  // Track wallet errors
  static trackWalletError(error: Error, userId: string, transactionType?: string, amount?: number) {
    ErrorTracker.captureException(error, {
      wallet: {
        userId,
        transactionType,
        amount,
      },
      business_context: 'wallet',
    });
  }

  // Track notification errors
  static trackNotificationError(error: Error, notificationData: Record<string, any>) {
    ErrorTracker.captureException(error, {
      notification: notificationData,
      business_context: 'notification',
    });
  }
}

// Performance monitoring
export class PerformanceTracker {
  // Track page load performance
  static trackPageLoad(pageName: string, loadTime: number) {
    const transaction = Sentry.startTransaction({
      name: `Page Load: ${pageName}`,
      op: 'navigation',
    });

    transaction.setData('page', pageName);
    transaction.setData('load_time', loadTime);
    transaction.finish();
  }

  // Track component render performance
  static trackComponentRender(componentName: string, renderTime: number) {
    const transaction = Sentry.startTransaction({
      name: `Component Render: ${componentName}`,
      op: 'react.render',
    });

    transaction.setData('component', componentName);
    transaction.setData('render_time', renderTime);
    transaction.finish();
  }

  // Track database query performance
  static trackDatabaseQuery(query: string, duration: number, recordCount?: number) {
    const transaction = Sentry.startTransaction({
      name: `DB Query: ${query}`,
      op: 'db.query',
    });

    transaction.setData('query', query);
    transaction.setData('duration', duration);
    if (recordCount !== undefined) {
      transaction.setData('record_count', recordCount);
    }
    transaction.finish();
  }
}

// React Hook for error boundary
export const useSentryErrorBoundary = () => {
  return {
    onError: (error: Error, errorInfo: any) => {
      ErrorTracker.captureException(error, {
        react: {
          componentStack: errorInfo.componentStack,
        },
      });
    },
  };
};

// Custom error classes for better categorization
export class AuctionError extends Error {
  constructor(message: string, public auctionId: string, public userId?: string) {
    super(message);
    this.name = 'AuctionError';
  }
}

export class GiveawayError extends Error {
  constructor(message: string, public giveawayId: string, public userId?: string) {
    super(message);
    this.name = 'GiveawayError';
  }
}

export class PaymentError extends Error {
  constructor(message: string, public paymentId?: string, public userId?: string) {
    super(message);
    this.name = 'PaymentError';
  }
}

export class WalletError extends Error {
  constructor(message: string, public userId: string, public transactionId?: string) {
    super(message);
    this.name = 'WalletError';
  }
}

// Error reporting utilities
export const reportError = {
  auction: (error: Error | string, auctionId: string, userId?: string, action?: string) => {
    const errorObj = typeof error === 'string' ? new AuctionError(error, auctionId, userId) : error;
    BusinessErrorTracker.trackAuctionError(errorObj, auctionId, userId, action);
  },

  giveaway: (error: Error | string, giveawayId: string, userId?: string, action?: string) => {
    const errorObj = typeof error === 'string' ? new GiveawayError(error, giveawayId, userId) : error;
    BusinessErrorTracker.trackGiveawayError(errorObj, giveawayId, userId, action);
  },

  order: (error: Error | string, orderId?: string, userId?: string, action?: string) => {
    const errorObj = typeof error === 'string' ? new Error(error) : error;
    BusinessErrorTracker.trackOrderError(errorObj, orderId, userId, action);
  },

  payment: (error: Error | string, paymentData: Record<string, any>) => {
    const errorObj = typeof error === 'string' ? new PaymentError(error) : error;
    BusinessErrorTracker.trackPaymentError(errorObj, paymentData);
  },

  wallet: (error: Error | string, userId: string, transactionType?: string, amount?: number) => {
    const errorObj = typeof error === 'string' ? new WalletError(error, userId) : error;
    BusinessErrorTracker.trackWalletError(errorObj, userId, transactionType, amount);
  },

  notification: (error: Error | string, notificationData: Record<string, any>) => {
    const errorObj = typeof error === 'string' ? new Error(error) : error;
    BusinessErrorTracker.trackNotificationError(errorObj, notificationData);
  },
};

export default ErrorTracker;
