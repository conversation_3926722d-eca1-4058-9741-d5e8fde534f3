"use client";

import { useState, useEffect } from "react";
import { useUser } from "@clerk/nextjs";
import SanityClient from "@sanity/client";

interface Draw {
  _id: string;
  name: string;
  drawDate: string;
  ticketDigitCount: number;
  ticketNumberCount: number;
  ticketPrice: number;
}

interface TicketPurchaseProps {
  drawId: string | null;
  ticketPrice: number | null;
  ticketDigitCount: number | null;
  ticketNumberCount: number | null;
  ticketNumbers: number[];
  setTicketNumbers: React.Dispatch<React.SetStateAction<number[]>>;
  createTicket: () => Promise<void>;
  loading: boolean;
  error: string | null;
  ticketCreated: boolean;
}

export default function TicketPurchase({
  drawId,
  ticketPrice,
  ticketDigitCount,
  ticketNumberCount,
  ticketNumbers,
  setTicketNumbers,
  createTicket,
  loading,
  error,
  ticketCreated,
}: TicketPurchaseProps) {
  const { user } = useUser();
  const [availableDraws, setAvailableDraws] = useState<Draw[]>([]);
  const [selectedDrawId, setSelectedDrawId] = useState<string | null>(drawId);
  const [selectedDraw, setSelectedDraw] = useState<Draw | null>(null);

  const [loadingDraws, setLoadingDraws] = useState(true);
  const [paymentSuccess, setPaymentSuccess] = useState(false);

  const sanity = SanityClient({
    projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,
    dataset: process.env.NEXT_PUBLIC_SANITY_DATASET!,
    apiVersion: "2023-06-10",
    useCdn: true,
  });

  useEffect(() => {
    async function fetchDraws() {
      setLoadingDraws(true);
      try {
        const draws: Draw[] = await sanity.fetch(
          `*[_type == "draw" && drawDate > now()] | order(drawDate asc)`
        );
        setAvailableDraws(draws);
        if (draws.length > 0 && !selectedDrawId) {
          setSelectedDrawId(draws[0]._id ?? null);
          setSelectedDraw(draws[0]);
        } else if (selectedDrawId) {
          const initialDraw = draws.find((d) => d._id === selectedDrawId);
          setSelectedDraw(initialDraw || null);
        }
      } catch (e: any) {
        // setError(`Çekilişler yüklenirken hata oluştu: ${e.message}`); // Hata artık prop olarak geliyor
      } finally {
        setLoadingDraws(false);
      }
    }
    fetchDraws();
  }, [selectedDrawId]);

  useEffect(() => {
    if (selectedDrawId && availableDraws.length > 0) {
      const foundDraw = availableDraws.find(
        (draw) => draw._id === selectedDrawId
      );
      setSelectedDraw(foundDraw || null);
    }
  }, [selectedDrawId, availableDraws]);

  return (
    <div>
      <h2>Çekiliş Bileti Satın Al</h2>
      {loadingDraws ? (
        <p>Çekilişler yükleniyor...</p>
      ) : availableDraws?.length === 0 ? (
        <p>Şu anda aktif bir çekiliş bulunmamaktadır.</p>
      ) : (
        <>
          {!ticketCreated && (
            <>
              <label htmlFor="draw-select">Çekiliş Seçin:</label>
              <select
                id="draw-select"
                value={selectedDrawId || ""}
                onChange={(e) => setSelectedDrawId(e.target.value)}
                disabled={loading}
              >
                {availableDraws?.map((draw) => (
                  <option key={draw._id} value={draw._id}>
                    {draw.name} ({new Date(draw.drawDate).toLocaleDateString()})
                    - {draw.ticketPrice} TL
                  </option>
                ))}
              </select>

              {selectedDraw && (
                <div>
                  <p>
                    Biletteki sayıların rakam sayısı:{" "}
                    {selectedDraw.ticketDigitCount}
                  </p>
                  <p>
                    Bilet başına sayı adedi: {selectedDraw.ticketNumberCount}
                  </p>
                  <p>Bilet fiyatı: {selectedDraw.ticketPrice} TL</p>
                </div>
              )}

              <button
                onClick={createTicket}
                disabled={loading || !user || !selectedDrawId}
              >
                {loading ? "Bilet Oluşturuluyor..." : "Bilet Oluştur"}
              </button>
            </>
          )}
        </>
      )}

      {!user && (
        <p style={{ color: "orange" }}>
          Bilet satın almak için giriş yapmalısınız.
        </p>
      )}
      {error && <p style={{ color: "red" }}>{error}</p>}

      {ticketCreated && (
        <div>
          <h3>Oluşturulan Bilet Numaraları:</h3>
          <ul>
            {ticketNumbers.map((num, i) => (
              <li key={i}>{num}</li>
            ))}
          </ul>
          {ticketPrice && <p>Fiyat: {ticketPrice} TL</p>}
          {ticketDigitCount && <p>Rakam Sayısı: {ticketDigitCount}</p>}
          {ticketNumberCount && <p>Sayı Adedi: {ticketNumberCount}</p>}
          <div style={{ color: "green", fontWeight: "bold" }}>
            <p>Tebrikler! Biletiniz başarıyla oluşturuldu ve kaydedildi.</p>
            <p>Çekiliş ID: {drawId}</p>
            <p>Numaralarınız: {ticketNumbers.join(", ")}</p>
            <p>Çekiliş sonuçlarını takip etmeyi unutmayın!</p>
          </div>
        </div>
      )}
    </div>
  );
}
