"use server";

import { defineQuery } from "next-sanity";
import { sanityFetch } from "../live";
import { Product } from "@/sanity.types";

export async function getLimitedProducts(limit: number): Promise<Product[]> {
  const LIMITED_PRODUCTS_QUERY = defineQuery(
    `*[_type == "product" && isFeatured == true] | order(_createdAt desc) [0...$limit]{
      _id,
      _type,
      _createdAt,
      _updatedAt,
      _rev,
      name,
      slug,
      image,
      price,
      description,
      category,
      isFeatured,
      stock
    }`,
  );

  try {
    const products = await sanityFetch({
      query: LIMITED_PRODUCTS_QUERY,
      params: { limit },
    });
    return products.data || [];
  } catch (error) {
    console.error('Error fetching limited products:', error);
    throw new Error('Failed to fetch limited products');
  }
} 