import { NextRequest, NextResponse } from 'next/server'
import { client } from '@/sanity/lib/client'

export async function POST(req: NextRequest) {
  try {
    console.log('🎁 Creating test giveaway')
    
    const now = new Date()
    const oneWeekLater = new Date()
    oneWeekLater.setDate(now.getDate() + 7)
    
    const testGiveaway = {
      _type: 'giveaway',
      title: 'Test Çekilişi - iPhone 15 Pro',
      description: 'Test amaçlı oluşturulan çekiliş. iPhone 15 Pro kazanma şansı!',
      status: 'active',
      startDate: now.toISOString(),
      endDate: oneWeekLater.toISOString(),
      totalTickets: 100,
      ticketsSold: 0,
      ticketPrice: 10,
      ticketSalePercentageForDraw: 80,
      participants: [],
      prizes: [
        {
          _key: 'prize1',
          rank: 1,
          title: 'iPhone 15 Pro',
          description: '256GB iPhone 15 Pro - Titanium Blue',
          value: 45000,
        },
        {
          _key: 'prize2',
          rank: 2,
          title: 'AirPods Pro',
          description: '2. nesil AirPods Pro',
          value: 8000,
        },
        {
          _key: 'prize3',
          rank: 3,
          title: 'Apple Watch',
          description: 'Apple Watch Series 9',
          value: 12000,
        }
      ],
      rules: 'Çekiliş kuralları: 1) Sadece onaylı kullanıcılar katılabilir. 2) Bilet satın alma işlemi geri alınamaz. 3) Kazananlar çekiliş tarihinden sonra açıklanır.',
      winningNumbers: [],
      winners: []
    }
    
    console.log('💾 Creating giveaway in Sanity:', testGiveaway)
    
    const result = await client.create(testGiveaway)
    console.log('✅ Test giveaway created:', result)
    
    return NextResponse.json({
      success: true,
      message: 'Test giveaway created successfully',
      giveaway: result
    })
    
  } catch (error) {
    console.error('❌ Error creating test giveaway:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function GET() {
  try {
    console.log('📊 Getting all giveaways')
    
    const giveaways = await client.fetch(`
      *[_type == "giveaway"] | order(_createdAt desc) {
        _id,
        title,
        description,
        status,
        startDate,
        endDate,
        drawDate,
        totalTickets,
        ticketsSold,
        ticketPrice,
        participants,
        prizes,
        winningNumbers,
        winners
      }
    `)
    
    return NextResponse.json({
      success: true,
      giveaways: giveaways,
      count: giveaways.length
    })
    
  } catch (error) {
    console.error('❌ Error fetching giveaways:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
