import { client } from '../client';

export async function getWalletTopUpRequestsByUser(userId: string) {
  if (!userId) return [];
  const query = `*[_type == "walletTopUpRequest" && user._ref == $userId] | order(createdAt desc)`;
  const params = { userId };
  const results = await client.fetch(query, params);
  return results.map((req: any) => ({
    ...req,
    amount: req.amount ?? 0,
    status: req.status ?? 'pending',
  }));
} 