import { POST } from '@/app/api/giveaways/buyTickets/route'
import { client } from '@/sanity/lib/client'
import { getGiveawayById } from '@/sanity/lib/giveaways/getGiveaways'
import { getUserByClerkId } from '@/sanity/lib/users/getUser'
import { currentUser } from '@clerk/nextjs/server'

// Mock dependencies
jest.mock('@/sanity/lib/client')
jest.mock('@/sanity/lib/giveaways/getGiveaways')
jest.mock('@/sanity/lib/users/getUser')
jest.mock('@clerk/nextjs/server')

const mockClient = client as jest.Mocked<typeof client>
const mockGetGiveawayById = getGiveawayById as jest.MockedFunction<typeof getGiveawayById>
const mockGetUserByClerkId = getUserByClerkId as jest.MockedFunction<typeof getUserByClerkId>
const mockCurrentUser = currentUser as jest.MockedFunction<typeof currentUser>

describe('/api/giveaways/buyTickets', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should return 401 if user is not authenticated', async () => {
    mockCurrentUser.mockResolvedValue(null)

    const request = new Request('http://localhost/api/giveaways/buyTickets', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        giveawayId: 'giveaway-123',
        numberOfTickets: 1,
        selectedTicketLength: 3,
        selectedDigitCount: 3
      }),
    })

    const response = await POST(request)
    expect(response.status).toBe(401)

    const data = await response.json()
    expect(data.error).toBe('Yetkisiz: Kullanıcı bilgileri eksik.')
  })

  it('should return 403 if user is not admin approved', async () => {
    mockCurrentUser.mockResolvedValue({
      id: 'clerk-123',
      emailAddresses: [{ emailAddress: '<EMAIL>' }],
      fullName: 'Test User'
    } as any)

    mockGetUserByClerkId.mockResolvedValue({
      _id: 'user-123',
      clerkId: 'clerk-123',
      name: 'Test User',
      email: '<EMAIL>',
      isAdminApproved: false
    } as any)

    const request = new Request('http://localhost/api/giveaways/buyTickets', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        giveawayId: 'giveaway-123',
        numberOfTickets: 1,
        selectedTicketLength: 3,
        selectedDigitCount: 3
      }),
    })

    const response = await POST(request)
    expect(response.status).toBe(403)

    const data = await response.json()
    expect(data.error).toBe('Bilet satın almak için yönetici onayı gerekiyor.')
  })

  it('should return 404 if giveaway is not found', async () => {
    mockCurrentUser.mockResolvedValue({
      id: 'clerk-123',
      emailAddresses: [{ emailAddress: '<EMAIL>' }],
      fullName: 'Test User'
    } as any)

    mockGetUserByClerkId.mockResolvedValue({
      _id: 'user-123',
      clerkId: 'clerk-123',
      name: 'Test User',
      email: '<EMAIL>',
      isAdminApproved: true
    } as any)

    mockGetGiveawayById.mockResolvedValue(null)

    const request = new Request('http://localhost/api/giveaways/buyTickets', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        giveawayId: 'giveaway-123',
        numberOfTickets: 1,
        selectedTicketLength: 3,
        selectedDigitCount: 3
      }),
    })

    const response = await POST(request)
    expect(response.status).toBe(404)

    const data = await response.json()
    expect(data.error).toBe('Çekiliş bulunamadı.')
  })

  it('should return 400 if giveaway is not active', async () => {
    mockCurrentUser.mockResolvedValue({
      id: 'clerk-123',
      emailAddresses: [{ emailAddress: '<EMAIL>' }],
      fullName: 'Test User'
    } as any)

    mockGetUserByClerkId.mockResolvedValue({
      _id: 'user-123',
      clerkId: 'clerk-123',
      name: 'Test User',
      email: '<EMAIL>',
      isAdminApproved: true
    } as any)

    mockGetGiveawayById.mockResolvedValue({
      _id: 'giveaway-123',
      title: 'Test Giveaway',
      status: 'inactive'
    } as any)

    const request = new Request('http://localhost/api/giveaways/buyTickets', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        giveawayId: 'giveaway-123',
        numberOfTickets: 1,
        selectedTicketLength: 3,
        selectedDigitCount: 3
      }),
    })

    const response = await POST(request)
    expect(response.status).toBe(400)

    const data = await response.json()
    expect(data.error).toBe('Çekiliş aktif değil.')
  })

  it('should handle missing user data gracefully', async () => {
    mockCurrentUser.mockResolvedValue({
      id: 'clerk-123',
      emailAddresses: [{ emailAddress: '<EMAIL>' }],
      fullName: 'Test User'
    } as any)

    mockGetUserByClerkId.mockResolvedValue(null)

    const request = new Request('http://localhost/api/giveaways/buyTickets', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        giveawayId: 'giveaway-123',
        numberOfTickets: 1,
        selectedTicketLength: 3,
        selectedDigitCount: 3
      }),
    })

    const response = await POST(request)
    expect(response.status).toBe(403)

    const data = await response.json()
    expect(data.error).toBe('Bilet satın almak için yönetici onayı gerekiyor.')
  })
})
