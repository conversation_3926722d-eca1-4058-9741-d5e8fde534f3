import { NextRequest, NextResponse } from 'next/server'
import { client } from '@/sanity/lib/client'
import { getUserByClerkId } from '@/sanity/lib/users/getUser'

export async function POST(req: NextRequest) {
  try {
    const { giveawayId, numberOfTickets = 1, selectedTicketLength = 4, selectedDigitCount = 3 } = await req.json()
    
    console.log('🎫 Testing ticket purchase:', { giveawayId, numberOfTickets, selectedTicketLength, selectedDigitCount })
    
    if (!giveawayId) {
      return NextResponse.json({
        success: false,
        error: 'giveawayId is required'
      }, { status: 400 })
    }
    
    // Get test user (Ege)
    const testUser = await getUserByClerkId({
      id: 'user_2yI8uUZ28R5xM4ddI5i47Yx3WZ7',
      email: '<EMAIL>',
      name: '<PERSON><PERSON>'
    })
    
    if (!testUser) {
      return NextResponse.json({
        success: false,
        error: 'Test user not found'
      }, { status: 404 })
    }
    
    console.log('👤 Test user:', testUser)
    
    // Check if user is admin approved
    if (!testUser.isAdminApproved) {
      return NextResponse.json({
        success: false,
        error: 'User is not admin approved',
        user: testUser
      }, { status: 403 })
    }
    
    // Get giveaway
    const giveaway = await client.fetch(`
      *[_type == "giveaway" && _id == $giveawayId][0] {
        _id,
        title,
        status,
        totalTickets,
        ticketsSold,
        participants,
        ticketSalePercentageForDraw,
        drawDate
      }
    `, { giveawayId })
    
    if (!giveaway) {
      return NextResponse.json({
        success: false,
        error: 'Giveaway not found'
      }, { status: 404 })
    }
    
    console.log('🎁 Giveaway:', giveaway)
    
    if (giveaway.status !== 'active') {
      return NextResponse.json({
        success: false,
        error: 'Giveaway is not active'
      }, { status: 400 })
    }
    
    // Generate ticket numbers
    const generateRandomTicket = (length: number, digitCount: number): string => {
      let ticket = '';
      const availableDigits = Array.from({ length: 10 }, (_, i) => i);
      let selectedDigits: number[] = [];

      // Select distinct digits based on digitCount
      while (selectedDigits.length < digitCount) {
        const randomDigit = availableDigits[Math.floor(Math.random() * availableDigits.length)];
        if (!selectedDigits.includes(randomDigit)) {
          selectedDigits.push(randomDigit);
        }
      }

      // Fill the ticket number with selected digits randomly
      for (let i = 0; i < length; i++) {
        ticket += selectedDigits[Math.floor(Math.random() * selectedDigits.length)].toString();
      }
      return ticket;
    };
    
    const newTickets = []
    for (let i = 0; i < numberOfTickets; i++) {
      const ticketNumber = generateRandomTicket(selectedTicketLength, selectedDigitCount)
      newTickets.push({
        _key: `${Date.now()}-${testUser._id}-${i}`,
        ticketNumber,
        purchasedAt: new Date().toISOString(),
        chosenDigitCount: selectedDigitCount,
      })
    }
    
    console.log('🎫 Generated tickets:', newTickets)
    
    // Update giveaway with new tickets
    const transaction = client.transaction()
    const newTicketsSold = (giveaway.ticketsSold || 0) + numberOfTickets
    
    // Check if user is already a participant
    const existingParticipantIndex = giveaway.participants?.findIndex((p: any) => p.user?._ref === testUser._id)
    
    if (existingParticipantIndex !== -1 && existingParticipantIndex !== undefined) {
      // User exists, append tickets to their existing entry
      console.log('👤 User already participant, appending tickets')
      transaction.patch(giveaway._id, (patch) =>
        patch
          .set({ ticketsSold: newTicketsSold })
          .append(`participants[${existingParticipantIndex}].tickets`, newTickets)
      )
    } else {
      // New participant, add new participant entry
      console.log('👤 New participant, adding user')
      transaction.patch(giveaway._id, (patch) =>
        patch
          .set({ ticketsSold: newTicketsSold })
          .append('participants', [
            {
              _key: `${Date.now()}-${testUser._id}`,
              user: { _ref: testUser._id, _type: 'reference' },
              tickets: newTickets,
            },
          ])
      )
    }
    
    // Check if draw date should be set
    const currentPercentage = (newTicketsSold / (giveaway.totalTickets || 1)) * 100
    if (!giveaway.drawDate && currentPercentage >= (giveaway.ticketSalePercentageForDraw || 0)) {
      const oneWeekFromNow = new Date()
      oneWeekFromNow.setDate(oneWeekFromNow.getDate() + 7)
      console.log('📅 Setting draw date:', oneWeekFromNow.toISOString())
      transaction.patch(giveaway._id, (patch) =>
        patch.set({ drawDate: oneWeekFromNow.toISOString() })
      )
    }
    
    const result = await transaction.commit()
    console.log('✅ Transaction committed:', result)
    
    return NextResponse.json({
      success: true,
      message: 'Tickets purchased successfully',
      purchasedTickets: newTickets,
      newTicketsSold: newTicketsSold,
      currentPercentage: currentPercentage
    })
    
  } catch (error) {
    console.error('❌ Error purchasing tickets:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function GET() {
  try {
    // Get all giveaways with participants
    const giveaways = await client.fetch(`
      *[_type == "giveaway"] | order(_createdAt desc) {
        _id,
        title,
        status,
        totalTickets,
        ticketsSold,
        participants[] {
          user-> {
            _id,
            name,
            email
          },
          tickets[] {
            ticketNumber,
            purchasedAt,
            chosenDigitCount
          }
        }
      }
    `)
    
    return NextResponse.json({
      success: true,
      giveaways: giveaways
    })
    
  } catch (error) {
    console.error('❌ Error fetching giveaways:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
