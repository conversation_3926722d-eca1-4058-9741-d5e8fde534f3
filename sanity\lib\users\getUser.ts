import { defineQuery } from "next-sanity";
import { sanityFetch } from "../live";
import { User } from "@/sanity.types";
import { client, writeClient } from '../client'; // Import both read and write clients

interface ClerkUserData {
  id: string; // clerkUserId
  email: string; // user's email from clerk
  name?: string; // user's name from clerk, optional
  
}

export async function getUserByClerkId(clerkUserData: ClerkUserData): Promise<User | null> {
  const { id: clerkUserId, email, name } = clerkUserData;

  if (!clerkUserId) {
    return null;
  }

  const USER_QUERY = defineQuery(`
    *[_type == "user" && clerkId == $clerkUserId][0] {
      _id,
      clerkId,
      name,
      email,
      imageUrl,
      isAdminApproved,
      isAdmin,
      walletBalance
    }
  `);

  try {
    let user = await sanityFetch({
      query: USER_QUERY,
      params: { clerkUserId },
    });

    if (user.data) {
      // User exists in Sanity, try to update their info if new data is provided
      const patchData: { name?: string; email?: string } = {};
      if (name && user.data.name !== name) {
        patchData.name = name;
      }
      if (email && user.data.email !== email) {
        patchData.email = email;
      }

      if (Object.keys(patchData).length > 0) {
        try {
          await writeClient.patch(user.data._id).set(patchData).commit();
          // Update local object with new data
          if (patchData.name) user.data.name = patchData.name;
          if (patchData.email) user.data.email = patchData.email;
        } catch (updateError) {
          console.warn("Could not update user due to permissions:", updateError);
          // Continue with existing user data
        }
      }
      if (user.data.walletBalance === undefined) user.data.walletBalance = 0;
      return user.data as User;
    } else {
      // User does not exist in Sanity, try to create a new one
      try {
        const newUser = await writeClient.create<User>({
          _id: clerkUserId,
          _type: 'user',
          _createdAt: new Date().toISOString(),
          _updatedAt: new Date().toISOString(),
          _rev: '',
          clerkId: clerkUserId,
          name: name,
          email: email,
          isAdminApproved: false, // Default to false, admin will approve
          walletBalance: 0,
        });
        if (newUser.walletBalance === undefined) newUser.walletBalance = 0;
        return newUser;
      } catch (createError) {
        console.warn("Could not create user due to permissions:", createError);
        // Return a minimal user object for the session
        return {
          _id: clerkUserId,
          _type: 'user',
          _createdAt: new Date().toISOString(),
          _updatedAt: new Date().toISOString(),
          _rev: '',
          clerkId: clerkUserId,
          name: name || 'User',
          email: email || '',
          isAdminApproved: false,
          walletBalance: 0,
        } as User;
      }
    }
  } catch (error) {
    console.error("Error fetching or creating user by Clerk ID:", error);
    // Return a minimal user object as fallback
    return {
      _id: clerkUserId,
      _type: 'user',
      _createdAt: new Date().toISOString(),
      _updatedAt: new Date().toISOString(),
      _rev: '',
      clerkId: clerkUserId,
      name: name || 'User',
      email: email || '',
      isAdminApproved: false,
      walletBalance: 0,
    } as User;
  }
} 