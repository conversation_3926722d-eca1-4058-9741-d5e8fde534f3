import { defineQuery } from "next-sanity";
import { sanityFetch } from "../live";
import { User } from "@/sanity.types";
import { client } from '../client'; // Import Sanity client

interface ClerkUserData {
  id: string; // clerkUserId
  email: string; // user's email from clerk
  name?: string; // user's name from clerk, optional
  
}

export async function getUserByClerkId(clerkUserData: ClerkUserData): Promise<User | null> {
  const { id: clerkUserId, email, name } = clerkUserData;

  if (!clerkUserId) {
    return null;
  }

  const USER_QUERY = defineQuery(`
    *[_type == "user" && clerkId == $clerkUserId][0] {
      _id,
      clerkId,
      name,
      email,
      imageUrl,
      isAdminApproved,
      isAdmin,
      walletBalance
    }
  `);

  try {
    let user = await sanityFetch({
      query: USER_QUERY,
      params: { clerkUserId },
    });

    if (user.data) {
      // User exists in Sanity, update their info if new data is provided
      const patchData: { name?: string; email?: string } = {};
      if (name && user.data.name !== name) {
        patchData.name = name;
      }
      if (email && user.data.email !== email) {
        patchData.email = email;
      }

      if (Object.keys(patchData).length > 0) {
        await client.patch(user.data._id).set(patchData).commit();
        // Update local object with new data
        if (patchData.name) user.data.name = patchData.name;
        if (patchData.email) user.data.email = patchData.email;
      }
      if (user.data.walletBalance === undefined) user.data.walletBalance = 0;
      return user.data as User;
    } else {
      // User does not exist in Sanity, create a new one
      const newUser = await client.create<User>({
        _id: clerkUserId,
        _type: 'user',
        _createdAt: new Date().toISOString(),
        _updatedAt: new Date().toISOString(),
        _rev: '',
        clerkId: clerkUserId,
        name: name,
        email: email,
        isAdminApproved: false, // Default to false, admin will approve
        walletBalance: 0,
      });
      if (newUser.walletBalance === undefined) newUser.walletBalance = 0;
      return newUser;
    }
  } catch (error) {
    console.error("Error fetching or creating user by Clerk ID:", error);
    return null;
  }
} 