import { NextRequest, NextResponse } from 'next/server'
import { writeClient } from '@/sanity/lib/client'

export async function POST(request: NextRequest) {
  try {
    const { giveawayId, status } = await request.json();

    if (!giveawayId || !status) {
      return NextResponse.json({
        success: false,
        error: 'giveawayId and status are required'
      }, { status: 400 });
    }

    console.log(`🔄 Updating giveaway ${giveawayId} status to ${status}`);

    // Update giveaway status
    const result = await writeClient
      .patch(giveawayId)
      .set({ status })
      .commit();

    console.log('✅ Giveaway status updated:', result);

    return NextResponse.json({
      success: true,
      message: `Giveaway status updated to ${status}`,
      data: result
    });

  } catch (error) {
    console.error('❌ Error updating giveaway status:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const giveawayId = searchParams.get('id');

    if (!giveawayId) {
      // Get all giveaways with their status
      const giveaways = await writeClient.fetch(`
        *[_type == "giveaway"] | order(_createdAt desc) {
          _id,
          title,
          status,
          startDate,
          endDate,
          ticketsSold,
          totalTickets
        }
      `);

      return NextResponse.json({
        success: true,
        data: giveaways
      });
    }

    // Get specific giveaway
    const giveaway = await writeClient.fetch(`
      *[_type == "giveaway" && _id == $giveawayId][0] {
        _id,
        title,
        status,
        startDate,
        endDate,
        ticketsSold,
        totalTickets,
        participants
      }
    `, { giveawayId });

    if (!giveaway) {
      return NextResponse.json({
        success: false,
        error: 'Giveaway not found'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: giveaway
    });

  } catch (error) {
    console.error('❌ Error fetching giveaway:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
