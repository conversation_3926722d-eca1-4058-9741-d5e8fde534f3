// Example usage of the new Product and Category system with TypeScript
import React from 'react';
import { 
  useProducts, 
  useProductBySlug, 
  useFeaturedProducts,
  useProductsByCategory,
  getLocalizedText,
  formatPrice,
  calculateDiscountedPrice,
  isProductInStock,
  getTotalStock
} from '../hooks/useProducts';
import { 
  useCategories, 
  useRootCategories, 
  useFeaturedCategories,
  getLocalizedCategoryText,
  buildCategoryHierarchy 
} from '../hooks/useCategories';
import { Product, Category } from '../types/sanity';

// Example: Product List Component
export const ProductList: React.FC = () => {
  const { data: products, isLoading, error } = useProducts({
    flags: ['featured'],
    inStock: true,
    page: 1,
    limit: 12,
    sortBy: 'name',
    sortOrder: 'asc',
    language: 'tr'
  });

  if (isLoading) return <div className="loading">Ürünler yükleniyor...</div>;
  if (error) return <div className="error">Hata: {error.message}</div>;

  return (
    <div className="product-grid">
      {products?.map(product => (
        <ProductCard key={product._id} product={product} />
      ))}
    </div>
  );
};

// Example: Product Card Component
interface ProductCardProps {
  product: Product;
  language?: 'tr' | 'en';
}

export const ProductCard: React.FC<ProductCardProps> = ({ 
  product, 
  language = 'tr' 
}) => {
  const productName = getLocalizedText(product.name, language);
  const productDescription = getLocalizedText(product.description, language);
  const originalPrice = product.price;
  const discountedPrice = calculateDiscountedPrice(originalPrice, product.discount);
  const formattedPrice = formatPrice(discountedPrice, product.currency);
  const inStock = isProductInStock(product);
  const totalStock = getTotalStock(product);

  return (
    <div className="product-card">
      {product.image && (
        <img 
          src={product.image.asset._ref} 
          alt={product.image.alt}
          className="product-image"
        />
      )}
      
      <div className="product-info">
        <h3 className="product-name">{productName}</h3>
        {productDescription && (
          <p className="product-description">{productDescription}</p>
        )}
        
        <div className="product-price">
          <span className="current-price">{formattedPrice}</span>
          {product.discount && product.discount > 0 && (
            <>
              <span className="original-price">
                {formatPrice(originalPrice, product.currency)}
              </span>
              <span className="discount-badge">%{product.discount} İndirim</span>
            </>
          )}
        </div>

        <div className="product-stock">
          {inStock ? (
            <span className="in-stock">Stokta ({totalStock} adet)</span>
          ) : (
            <span className="out-of-stock">Stokta Yok</span>
          )}
        </div>

        {product.flags && product.flags.length > 0 && (
          <div className="product-flags">
            {product.flags.map(flag => (
              <span key={flag} className={`flag flag-${flag}`}>
                {getFlagLabel(flag)}
              </span>
            ))}
          </div>
        )}

        {product.variants && product.variants.length > 0 && (
          <div className="product-variants">
            <span>Varyantlar: </span>
            {product.variants.map((variant, index) => (
              <span key={index} className="variant">
                {variant.name}: {variant.value}
              </span>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

// Example: Product Detail Component
interface ProductDetailProps {
  slug: string;
  language?: 'tr' | 'en';
}

export const ProductDetail: React.FC<ProductDetailProps> = ({ 
  slug, 
  language = 'tr' 
}) => {
  const { data: product, isLoading, error } = useProductBySlug(slug);

  if (isLoading) return <div className="loading">Ürün yükleniyor...</div>;
  if (error) return <div className="error">Hata: {error.message}</div>;
  if (!product) return <div className="not-found">Ürün bulunamadı</div>;

  const productName = getLocalizedText(product.name, language);
  const productDescription = getLocalizedText(product.description, language);

  return (
    <div className="product-detail">
      <div className="product-images">
        {product.image && (
          <img 
            src={product.image.asset._ref} 
            alt={product.image.alt}
            className="main-image"
          />
        )}
      </div>

      <div className="product-info">
        <h1>{productName}</h1>
        {productDescription && <p>{productDescription}</p>}
        
        <ProductCard product={product} language={language} />
        
        {/* SEO Information (for admin/debug) */}
        {product.seoTitle && (
          <div className="seo-info">
            <h4>SEO Bilgileri:</h4>
            <p><strong>Başlık:</strong> {product.seoTitle}</p>
            {product.seoDescription && (
              <p><strong>Açıklama:</strong> {product.seoDescription}</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// Example: Category Navigation Component
export const CategoryNavigation: React.FC = () => {
  const { data: rootCategories, isLoading } = useRootCategories();
  const { data: allCategories } = useCategories();

  if (isLoading) return <div className="loading">Kategoriler yükleniyor...</div>;

  const hierarchicalCategories = allCategories ? buildCategoryHierarchy(allCategories) : [];

  return (
    <nav className="category-navigation">
      <ul className="category-list">
        {rootCategories?.map(category => (
          <CategoryItem 
            key={category._id} 
            category={category}
            allCategories={allCategories || []}
          />
        ))}
      </ul>
    </nav>
  );
};

// Example: Category Item Component
interface CategoryItemProps {
  category: Category;
  allCategories: Category[];
  language?: 'tr' | 'en';
}

export const CategoryItem: React.FC<CategoryItemProps> = ({ 
  category, 
  allCategories,
  language = 'tr' 
}) => {
  const categoryTitle = getLocalizedCategoryText(category.title, language);
  const subCategories = allCategories.filter(cat => 
    cat.parentCategory && cat.parentCategory._ref === category._id
  );

  return (
    <li className="category-item">
      <a href={`/categories/${category.slug.current}`} className="category-link">
        {category.image && (
          <img 
            src={category.image.asset._ref} 
            alt={category.image.alt}
            className="category-icon"
          />
        )}
        <span>{categoryTitle}</span>
        {category.flags?.includes('featured') && (
          <span className="featured-badge">Öne Çıkan</span>
        )}
      </a>
      
      {subCategories.length > 0 && (
        <ul className="subcategory-list">
          {subCategories.map(subCategory => (
            <CategoryItem 
              key={subCategory._id}
              category={subCategory}
              allCategories={allCategories}
              language={language}
            />
          ))}
        </ul>
      )}
    </li>
  );
};

// Helper function to get flag labels
const getFlagLabel = (flag: string): string => {
  const flagLabels: Record<string, string> = {
    featured: 'Öne Çıkan',
    new: 'Yeni',
    sale: 'İndirimli',
    limited: 'Sınırlı Sayıda',
    auction: 'Müzayede',
    giveaway: 'Çekiliş',
    banner: 'Banner',
    exchange: 'Ürün Takası',
  };
  
  return flagLabels[flag] || flag;
};

// Example: Search Component
interface ProductSearchProps {
  onResults?: (products: Product[]) => void;
}

export const ProductSearch: React.FC<ProductSearchProps> = ({ onResults }) => {
  const [searchTerm, setSearchTerm] = React.useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = React.useState('');

  // Debounce search term
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  const { data: searchResults, isLoading } = useProducts({
    search: debouncedSearchTerm,
    limit: 20,
  });

  React.useEffect(() => {
    if (searchResults && onResults) {
      onResults(searchResults);
    }
  }, [searchResults, onResults]);

  return (
    <div className="product-search">
      <input
        type="text"
        placeholder="Ürün ara..."
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        className="search-input"
      />
      
      {isLoading && <div className="search-loading">Aranıyor...</div>}
      
      {searchResults && searchResults.length > 0 && (
        <div className="search-results">
          {searchResults.map(product => (
            <ProductCard key={product._id} product={product} />
          ))}
        </div>
      )}
    </div>
  );
};
