import { Product } from "@/sanity.types";
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

export interface BasketItem {
    product: Product;
    quantity: number;
}

export interface BasketState {
    items: BasketItem[];
    addItem: (product: Product, quantity?: number) => void;
    removeItem: (productId: string) => void;
    getItemCount: (productId: string) => number;
    getTotalPrice: () => number;
    clearBasket: () => void;
    getGroupedItems: () => Record<string, BasketItem[]>;
}

export const useBasketStore = create<BasketState>()(
    persist(
        (set, get) => ({
        items: [],
            
            addItem: (product: Product, quantity = 1) => {
                set((state) => {
                    const existingItem = state.items.find((item) => item.product._id === product._id);
               if (existingItem) {
                return {
                            items: state.items.map((item) =>
                                item.product._id === product._id
                                    ? { ...item, quantity: item.quantity + quantity }
                                    : item
                            ),
                        };
               } else {
                        return { items: [...state.items, { product, quantity }] };
               }
            });
        },

            removeItem: (productId: string) => {
                set((state) => {
                    const existingItem = state.items.find((item) => item.product._id === productId);

                    if (existingItem && existingItem.quantity > 1) {
                        return {
                            items: state.items.map((item) =>
                                item.product._id === productId
                                    ? { ...item, quantity: item.quantity - 1 }
                                    : item
                            ),
                        };
                    }
                    return {
                        items: state.items.filter((item) => item.product._id !== productId),
                    };
                });
            },

        clearBasket: () => {
            set({ items: [] });
        },
            
        getGroupedItems: () => {
            return get().items.reduce((acc, item) => {
                    const categoryRef = item.product.category?.[0]?._ref || 'Uncategorized';
                    if (!acc[categoryRef]) {
                        acc[categoryRef] = [];
                }
                    acc[categoryRef].push(item);
                return acc;
            }, {} as Record<string, BasketItem[]>);
        },  
            
            getItemCount: (productId: string) => {
                return get().items.find((item) => item.product._id === productId)?.quantity || 0;
        },
            
        getTotalPrice: () => {
                return get().items.reduce((total, item) => total + (item.product.price || 0) * item.quantity, 0);
            },
        }),
            {
                name: "basket-storage",
                storage: createJSONStorage(() => localStorage),
            }
    )
);

