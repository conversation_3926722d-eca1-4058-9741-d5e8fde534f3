// Advanced Analytics and Reporting System
import { client } from '@/sanity/lib/client';
import { ErrorTracker } from '../sentry/config';

// Analytics event types
export type AnalyticsEventType = 
  | 'page_view'
  | 'auction_view'
  | 'auction_bid'
  | 'giveaway_view'
  | 'giveaway_ticket_purchase'
  | 'product_view'
  | 'order_created'
  | 'payment_completed'
  | 'user_registration'
  | 'user_login'
  | 'notification_clicked'
  | 'search_performed';

// Analytics event interface
export interface AnalyticsEvent {
  id: string;
  type: AnalyticsEventType;
  userId?: string;
  sessionId: string;
  timestamp: string;
  properties: Record<string, any>;
  metadata: {
    userAgent?: string;
    ip?: string;
    referrer?: string;
    device?: string;
    browser?: string;
    os?: string;
    country?: string;
    city?: string;
  };
}

// Analytics metrics interfaces
export interface AuctionMetrics {
  totalAuctions: number;
  activeAuctions: number;
  completedAuctions: number;
  totalBids: number;
  averageBidsPerAuction: number;
  totalRevenue: number;
  averageWinningBid: number;
  participationRate: number;
  conversionRate: number;
}

export interface GiveawayMetrics {
  totalGiveaways: number;
  activeGiveaways: number;
  completedGiveaways: number;
  totalTicketsSold: number;
  totalRevenue: number;
  averageTicketsPerUser: number;
  participationRate: number;
  completionRate: number;
}

export interface UserMetrics {
  totalUsers: number;
  activeUsers: number;
  newUsers: number;
  retentionRate: number;
  averageSessionDuration: number;
  bounceRate: number;
  topCountries: Array<{ country: string; count: number }>;
  deviceBreakdown: Array<{ device: string; count: number }>;
}

export interface RevenueMetrics {
  totalRevenue: number;
  auctionRevenue: number;
  giveawayRevenue: number;
  averageOrderValue: number;
  revenueGrowth: number;
  topProducts: Array<{ product: string; revenue: number }>;
  monthlyRevenue: Array<{ month: string; revenue: number }>;
}

// Analytics Manager class
export class AnalyticsManager {
  private static instance: AnalyticsManager;
  private events: AnalyticsEvent[] = [];
  private batchSize = 100;
  private flushInterval = 30000; // 30 seconds
  private flushTimer?: NodeJS.Timeout;

  private constructor() {
    this.startBatchProcessor();
  }

  static getInstance(): AnalyticsManager {
    if (!AnalyticsManager.instance) {
      AnalyticsManager.instance = new AnalyticsManager();
    }
    return AnalyticsManager.instance;
  }

  // Track analytics event
  track(
    type: AnalyticsEventType,
    properties: Record<string, any> = {},
    userId?: string,
    sessionId?: string,
    metadata: Partial<AnalyticsEvent['metadata']> = {}
  ): void {
    const event: AnalyticsEvent = {
      id: this.generateEventId(),
      type,
      userId,
      sessionId: sessionId || this.generateSessionId(),
      timestamp: new Date().toISOString(),
      properties,
      metadata: {
        userAgent: metadata.userAgent,
        ip: metadata.ip,
        referrer: metadata.referrer,
        device: metadata.device,
        browser: metadata.browser,
        os: metadata.os,
        country: metadata.country,
        city: metadata.city,
      },
    };

    this.events.push(event);

    // Flush if batch size reached
    if (this.events.length >= this.batchSize) {
      this.flush();
    }
  }

  // Flush events to storage
  private async flush(): Promise<void> {
    if (this.events.length === 0) return;

    const eventsToFlush = [...this.events];
    this.events = [];

    try {
      // Store events in your analytics database
      await this.storeEvents(eventsToFlush);
      console.log(`📊 Flushed ${eventsToFlush.length} analytics events`);
    } catch (error) {
      console.error('Failed to flush analytics events:', error);
      ErrorTracker.captureException(error as Error, {
        analytics: {
          eventCount: eventsToFlush.length,
          eventTypes: [...new Set(eventsToFlush.map(e => e.type))],
        },
      });
      
      // Re-add events to queue for retry
      this.events.unshift(...eventsToFlush);
    }
  }

  // Start batch processor
  private startBatchProcessor(): void {
    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.flushInterval);
  }

  // Stop batch processor
  stopBatchProcessor(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = undefined;
    }
    this.flush(); // Flush remaining events
  }

  // Store events (implement based on your storage solution)
  private async storeEvents(events: AnalyticsEvent[]): Promise<void> {
    // This could be sent to:
    // - Google Analytics
    // - Mixpanel
    // - Amplitude
    // - Custom database
    // - Multiple destinations
    
    console.log('Storing analytics events:', events.length);
    // Implementation depends on your analytics backend
  }

  // Generate unique event ID
  private generateEventId(): string {
    return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Generate session ID
  private generateSessionId(): string {
    return `sess_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Get auction metrics
  async getAuctionMetrics(dateFrom?: string, dateTo?: string): Promise<AuctionMetrics> {
    try {
      const dateFilter = this.buildDateFilter(dateFrom, dateTo);
      
      const metrics = await client.fetch(`
        {
          "totalAuctions": count(*[_type == "auction" ${dateFilter}]),
          "activeAuctions": count(*[_type == "auction" && status == "active" ${dateFilter}]),
          "completedAuctions": count(*[_type == "auction" && status == "completed" ${dateFilter}]),
          "totalBids": count(*[_type == "auction" ${dateFilter}].bidHistory[]),
          "totalRevenue": sum(*[_type == "auction" && status == "completed" ${dateFilter}].currentBid),
          "auctions": *[_type == "auction" && status == "completed" ${dateFilter}] {
            currentBid,
            bidHistory
          }
        }
      `);

      const averageBidsPerAuction = metrics.totalAuctions > 0 
        ? metrics.totalBids / metrics.totalAuctions 
        : 0;

      const averageWinningBid = metrics.completedAuctions > 0 
        ? metrics.totalRevenue / metrics.completedAuctions 
        : 0;

      // Calculate participation and conversion rates
      const uniqueBidders = new Set();
      metrics.auctions.forEach((auction: any) => {
        auction.bidHistory?.forEach((bid: any) => {
          uniqueBidders.add(bid.bidder._ref);
        });
      });

      const participationRate = metrics.totalAuctions > 0 
        ? (uniqueBidders.size / metrics.totalAuctions) * 100 
        : 0;

      const conversionRate = metrics.totalBids > 0 
        ? (metrics.completedAuctions / metrics.totalBids) * 100 
        : 0;

      return {
        totalAuctions: metrics.totalAuctions,
        activeAuctions: metrics.activeAuctions,
        completedAuctions: metrics.completedAuctions,
        totalBids: metrics.totalBids,
        averageBidsPerAuction,
        totalRevenue: metrics.totalRevenue || 0,
        averageWinningBid,
        participationRate,
        conversionRate,
      };
    } catch (error) {
      ErrorTracker.captureException(error as Error, { context: 'auction_metrics' });
      throw error;
    }
  }

  // Get giveaway metrics
  async getGiveawayMetrics(dateFrom?: string, dateTo?: string): Promise<GiveawayMetrics> {
    try {
      const dateFilter = this.buildDateFilter(dateFrom, dateTo);
      
      const metrics = await client.fetch(`
        {
          "totalGiveaways": count(*[_type == "giveaway" ${dateFilter}]),
          "activeGiveaways": count(*[_type == "giveaway" && status == "active" ${dateFilter}]),
          "completedGiveaways": count(*[_type == "giveaway" && status == "completed" ${dateFilter}]),
          "giveaways": *[_type == "giveaway" ${dateFilter}] {
            ticketsSold,
            ticketPrice,
            totalTickets,
            participants,
            status
          }
        }
      `);

      const totalTicketsSold = metrics.giveaways.reduce((sum: number, g: any) => sum + (g.ticketsSold || 0), 0);
      const totalRevenue = metrics.giveaways.reduce((sum: number, g: any) => sum + ((g.ticketsSold || 0) * (g.ticketPrice || 0)), 0);
      
      const uniqueParticipants = new Set();
      metrics.giveaways.forEach((giveaway: any) => {
        giveaway.participants?.forEach((participant: any) => {
          uniqueParticipants.add(participant.user._ref);
        });
      });

      const averageTicketsPerUser = uniqueParticipants.size > 0 
        ? totalTicketsSold / uniqueParticipants.size 
        : 0;

      const participationRate = metrics.totalGiveaways > 0 
        ? (uniqueParticipants.size / metrics.totalGiveaways) * 100 
        : 0;

      const completionRate = metrics.totalGiveaways > 0 
        ? (metrics.completedGiveaways / metrics.totalGiveaways) * 100 
        : 0;

      return {
        totalGiveaways: metrics.totalGiveaways,
        activeGiveaways: metrics.activeGiveaways,
        completedGiveaways: metrics.completedGiveaways,
        totalTicketsSold,
        totalRevenue,
        averageTicketsPerUser,
        participationRate,
        completionRate,
      };
    } catch (error) {
      ErrorTracker.captureException(error as Error, { context: 'giveaway_metrics' });
      throw error;
    }
  }

  // Get user metrics
  async getUserMetrics(dateFrom?: string, dateTo?: string): Promise<UserMetrics> {
    try {
      const dateFilter = this.buildDateFilter(dateFrom, dateTo);
      
      const metrics = await client.fetch(`
        {
          "totalUsers": count(*[_type == "user" ${dateFilter}]),
          "activeUsers": count(*[_type == "user" && status == "active" ${dateFilter}]),
          "newUsers": count(*[_type == "user" ${dateFilter}]),
          "users": *[_type == "user" ${dateFilter}] {
            _createdAt,
            lastLogin,
            status
          }
        }
      `);

      // Calculate retention rate (users who logged in within last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const recentlyActiveUsers = metrics.users.filter((user: any) => 
        user.lastLogin && new Date(user.lastLogin) > thirtyDaysAgo
      ).length;

      const retentionRate = metrics.totalUsers > 0 
        ? (recentlyActiveUsers / metrics.totalUsers) * 100 
        : 0;

      return {
        totalUsers: metrics.totalUsers,
        activeUsers: metrics.activeUsers,
        newUsers: metrics.newUsers,
        retentionRate,
        averageSessionDuration: 0, // Would need session tracking
        bounceRate: 0, // Would need page view tracking
        topCountries: [], // Would need location tracking
        deviceBreakdown: [], // Would need device tracking
      };
    } catch (error) {
      ErrorTracker.captureException(error as Error, { context: 'user_metrics' });
      throw error;
    }
  }

  // Get revenue metrics
  async getRevenueMetrics(dateFrom?: string, dateTo?: string): Promise<RevenueMetrics> {
    try {
      const dateFilter = this.buildDateFilter(dateFrom, dateTo);
      
      const [auctionRevenue, giveawayRevenue, orderMetrics] = await Promise.all([
        client.fetch(`sum(*[_type == "auction" && status == "completed" ${dateFilter}].currentBid)`),
        client.fetch(`
          sum(*[_type == "giveaway" ${dateFilter}] {
            "revenue": ticketsSold * ticketPrice
          }.revenue)
        `),
        client.fetch(`
          {
            "totalRevenue": sum(*[_type == "order" ${dateFilter}].totalAmount),
            "orderCount": count(*[_type == "order" ${dateFilter}]),
            "orders": *[_type == "order" ${dateFilter}] {
              totalAmount,
              _createdAt,
              orderItems
            }
          }
        `)
      ]);

      const totalRevenue = (auctionRevenue || 0) + (giveawayRevenue || 0) + (orderMetrics.totalRevenue || 0);
      const averageOrderValue = orderMetrics.orderCount > 0 
        ? orderMetrics.totalRevenue / orderMetrics.orderCount 
        : 0;

      // Calculate monthly revenue (last 12 months)
      const monthlyRevenue = this.calculateMonthlyRevenue(orderMetrics.orders);

      return {
        totalRevenue,
        auctionRevenue: auctionRevenue || 0,
        giveawayRevenue: giveawayRevenue || 0,
        averageOrderValue,
        revenueGrowth: 0, // Would need historical comparison
        topProducts: [], // Would need product-level analysis
        monthlyRevenue,
      };
    } catch (error) {
      ErrorTracker.captureException(error as Error, { context: 'revenue_metrics' });
      throw error;
    }
  }

  // Build date filter for GROQ queries
  private buildDateFilter(dateFrom?: string, dateTo?: string): string {
    if (!dateFrom && !dateTo) return '';
    
    const conditions = [];
    if (dateFrom) conditions.push(`_createdAt >= "${dateFrom}"`);
    if (dateTo) conditions.push(`_createdAt <= "${dateTo}"`);
    
    return `&& (${conditions.join(' && ')})`;
  }

  // Calculate monthly revenue breakdown
  private calculateMonthlyRevenue(orders: any[]): Array<{ month: string; revenue: number }> {
    const monthlyData: Record<string, number> = {};
    
    orders.forEach(order => {
      const date = new Date(order._createdAt);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      monthlyData[monthKey] = (monthlyData[monthKey] || 0) + order.totalAmount;
    });

    return Object.entries(monthlyData)
      .map(([month, revenue]) => ({ month, revenue }))
      .sort((a, b) => a.month.localeCompare(b.month));
  }

  // Generate comprehensive dashboard data
  async getDashboardData(dateFrom?: string, dateTo?: string) {
    try {
      const [auctionMetrics, giveawayMetrics, userMetrics, revenueMetrics] = await Promise.all([
        this.getAuctionMetrics(dateFrom, dateTo),
        this.getGiveawayMetrics(dateFrom, dateTo),
        this.getUserMetrics(dateFrom, dateTo),
        this.getRevenueMetrics(dateFrom, dateTo),
      ]);

      return {
        auctions: auctionMetrics,
        giveaways: giveawayMetrics,
        users: userMetrics,
        revenue: revenueMetrics,
        generatedAt: new Date().toISOString(),
      };
    } catch (error) {
      ErrorTracker.captureException(error as Error, { context: 'dashboard_data' });
      throw error;
    }
  }
}

// Event tracking utilities
export const trackEvent = {
  pageView: (page: string, userId?: string, sessionId?: string) => {
    AnalyticsManager.getInstance().track('page_view', { page }, userId, sessionId);
  },

  auctionView: (auctionId: string, userId?: string, sessionId?: string) => {
    AnalyticsManager.getInstance().track('auction_view', { auctionId }, userId, sessionId);
  },

  auctionBid: (auctionId: string, bidAmount: number, userId?: string, sessionId?: string) => {
    AnalyticsManager.getInstance().track('auction_bid', { auctionId, bidAmount }, userId, sessionId);
  },

  giveawayView: (giveawayId: string, userId?: string, sessionId?: string) => {
    AnalyticsManager.getInstance().track('giveaway_view', { giveawayId }, userId, sessionId);
  },

  giveawayTicketPurchase: (giveawayId: string, ticketCount: number, amount: number, userId?: string, sessionId?: string) => {
    AnalyticsManager.getInstance().track('giveaway_ticket_purchase', { giveawayId, ticketCount, amount }, userId, sessionId);
  },

  productView: (productId: string, userId?: string, sessionId?: string) => {
    AnalyticsManager.getInstance().track('product_view', { productId }, userId, sessionId);
  },

  orderCreated: (orderId: string, amount: number, userId?: string, sessionId?: string) => {
    AnalyticsManager.getInstance().track('order_created', { orderId, amount }, userId, sessionId);
  },

  paymentCompleted: (orderId: string, amount: number, method: string, userId?: string, sessionId?: string) => {
    AnalyticsManager.getInstance().track('payment_completed', { orderId, amount, method }, userId, sessionId);
  },

  userRegistration: (userId: string, method: string, sessionId?: string) => {
    AnalyticsManager.getInstance().track('user_registration', { method }, userId, sessionId);
  },

  userLogin: (userId: string, method: string, sessionId?: string) => {
    AnalyticsManager.getInstance().track('user_login', { method }, userId, sessionId);
  },

  notificationClicked: (notificationId: string, type: string, userId?: string, sessionId?: string) => {
    AnalyticsManager.getInstance().track('notification_clicked', { notificationId, type }, userId, sessionId);
  },

  searchPerformed: (query: string, results: number, userId?: string, sessionId?: string) => {
    AnalyticsManager.getInstance().track('search_performed', { query, results }, userId, sessionId);
  },
};

export default AnalyticsManager;
