import { getProductBySlug } from "@/sanity/lib/products/getProductBySlug";
import { NextResponse } from "next/server";

export async function GET(
  request: Request, // request is unused, but required for the second argument to be context
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    const product = await getProductBySlug(slug);

    if (!product) {
      return new NextResponse("Product not found", { status: 404 });
    }

    return NextResponse.json({ product });
  } catch (error) {
    console.error("Error fetching product:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
} 
