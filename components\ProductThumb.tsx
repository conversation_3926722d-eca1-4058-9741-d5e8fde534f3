import { imageUrl } from "@/lib/imageUrl";
import { Product } from "@/sanity.types";
import Link from "next/link";
import Image from "next/image";
import { getTurkishText } from "@/lib/utils/multilingual";

function ProductThumb({ product }: { product: Product }) {
  const isOutOfStock = product.stock != null && product.stock <= 0;
  const productSlug = typeof product.slug === "string" ? product.slug : product.slug?.current;

  if (!productSlug) return null;

  return (
    <Link
      href={`/product/${productSlug}`}
      className={`block overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition hover:shadow-md ${isOutOfStock ? "opacity-50" : ""}`}
    >
      <div className="relative aspect-square bg-gray-200">
        {product.image && product.image.asset ? (
          <Image
            src={imageUrl(product.image).width(400).height(400).url()}
            alt={getTurkishText(product.name, "Product image")}
            fill
            sizes="(max-width: 768px) 100vw, 33vw"
            className="object-cover object-center"
          />
        ) : (
          <div className="flex h-full w-full items-center justify-center bg-gray-100">
            <span className="text-gray-400">No Image</span>
          </div>
        )}
        {isOutOfStock && (
          <div className="absolute inset-0 z-10 flex items-center justify-center bg-black/50">
            <span className="text-lg font-bold text-white">Out of Stock</span>
          </div>
        )}
      </div>
      <div className="p-4">
        <h3 className="truncate text-lg font-semibold text-gray-900">
          {getTurkishText(product.name, 'Unnamed Product')}
        </h3>
        <div className="mt-2 flex items-center justify-between">
          <p className="text-lg font-bold text-gray-900">${product.price?.toLocaleString()}</p>
          {/* {product.stock !== undefined && (
            <span className="text-sm text-gray-500">
              {product.stock > 0 ? `${product.stock} in stock` : "Out of stock"}
            </span>
          )} */}
        </div>
      </div>
    </Link>
  );
}

export default ProductThumb;
