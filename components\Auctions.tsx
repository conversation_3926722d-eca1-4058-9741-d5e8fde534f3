import Link from "next/link";

export const Auctions = () => {
  return (
    <section className="py-12 px-4 max-w-full mx-auto">
      <Link
        href="/auctions"
        className="block relative overflow-hidden rounded-lg shadow-xl bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 transition-all duration-300 transform hover:scale-[1.01] group"
      >
        <div className="relative z-10 p-8 md:p-12 text-white flex flex-col md:flex-row items-center justify-between">
          <div className="text-center md:text-left mb-6 md:mb-0">
            <h2 className="text-4xl md:text-5xl font-extrabold mb-3 leading-tight group-hover:underline">
              Büyük Açık Artırmalar Başladı!
            </h2>
            <p className="text-xl md:text-2xl opacity-90 max-w-2xl">
              En özel ürünleri kaçırma<PERSON>ın, teklifinizi verin ve kazanın!
            </p>
          </div>
          <div className="flex-shrink-0">
            <svg
              className="w-24 h-24 md:w-32 md:h-32 text-white opacity-80 group-hover:opacity-100 transition-opacity"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M17 9V3.5L12 8L7 3.5V9M17 9H12M17 9L12 14M12 9H7M12 14L7 9"
              />
            </svg>
          </div>
        </div>
        {/* Arka plan deseni veya görseli eklenebilir */}
        <div className="absolute inset-0 bg-pattern-auction opacity-20"></div>
      </Link>
    </section>
  );
};
