import { useQuery } from '@tanstack/react-query';
import { api } from '../lib/api';
import { 
  Order, 
  OrderWithDetails,
  ApiResponse 
} from '../types/sanity';

// Query parameters interface
export interface OrderQueryParams {
  customer?: string; // clerkUserId
  status?: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled';
  paymentStatus?: 'pending' | 'paid' | 'cancelled';
  source?: 'regular' | 'giveaway' | 'auction';
  dateFrom?: string;
  dateTo?: string;
  page?: number;
  limit?: number;
  sortBy?: 'orderNumber' | 'createdAt' | 'totalAmount';
  sortOrder?: 'asc' | 'desc';
}

// API functions
const fetchOrders = async (params?: OrderQueryParams): Promise<Order[]> => {
  const response = await api.get('/orders', params);
  return response.data;
};

const fetchOrderById = async (id: string): Promise<Order | null> => {
  const response = await api.get(`/orders/${id}`);
  return response.data;
};

const fetchOrderByNumber = async (orderNumber: string): Promise<Order | null> => {
  const response = await api.get(`/orders/number/${orderNumber}`);
  return response.data;
};

const fetchMyOrders = async (clerkUserId: string): Promise<Order[]> => {
  const response = await api.get(`/orders/my/${clerkUserId}`);
  return response.data;
};

const fetchOrdersByStatus = async (status: string): Promise<Order[]> => {
  const response = await api.get(`/orders/status/${status}`);
  return response.data;
};

// React Query hooks
export const useOrders = (params?: OrderQueryParams) => {
  return useQuery<Order[], Error>({
    queryKey: ['orders', params],
    queryFn: () => fetchOrders(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
    refetchOnWindowFocus: false,
  });
};

export const useOrder = (id: string) => {
  return useQuery<Order | null, Error>({
    queryKey: ['order', id],
    queryFn: () => fetchOrderById(id),
    staleTime: 2 * 60 * 1000,
    retry: 2,
    enabled: !!id,
  });
};

export const useOrderByNumber = (orderNumber: string) => {
  return useQuery<Order | null, Error>({
    queryKey: ['order', 'number', orderNumber],
    queryFn: () => fetchOrderByNumber(orderNumber),
    staleTime: 2 * 60 * 1000,
    retry: 2,
    enabled: !!orderNumber,
  });
};

export const useMyOrders = (clerkUserId: string) => {
  return useQuery<Order[], Error>({
    queryKey: ['orders', 'my', clerkUserId],
    queryFn: () => fetchMyOrders(clerkUserId),
    staleTime: 1 * 60 * 1000, // 1 minute for user's own orders
    retry: 2,
    enabled: !!clerkUserId,
  });
};

export const useOrdersByStatus = (status: string) => {
  return useQuery<Order[], Error>({
    queryKey: ['orders', 'status', status],
    queryFn: () => fetchOrdersByStatus(status),
    staleTime: 2 * 60 * 1000,
    retry: 2,
    enabled: !!status,
  });
};

// Utility hooks
export const useOrderStatuses = () => {
  const statuses = [
    { value: 'pending', label: 'Bekliyor', color: 'orange' },
    { value: 'confirmed', label: 'Onaylandı', color: 'blue' },
    { value: 'shipped', label: 'Kargoda', color: 'purple' },
    { value: 'delivered', label: 'Teslim Edildi', color: 'green' },
    { value: 'cancelled', label: 'İptal Edildi', color: 'red' },
  ] as const;

  return statuses;
};

export const usePaymentStatuses = () => {
  const statuses = [
    { value: 'pending', label: 'Ödeme Bekliyor', color: 'orange' },
    { value: 'paid', label: 'Ödendi', color: 'green' },
    { value: 'cancelled', label: 'Ödeme İptal', color: 'red' },
  ] as const;

  return statuses;
};

export const useOrderSources = () => {
  const sources = [
    { value: 'regular', label: 'Normal Satın Alma', icon: '🛒' },
    { value: 'giveaway', label: 'Çekiliş', icon: '🎁' },
    { value: 'auction', label: 'Müzayede', icon: '🔨' },
  ] as const;

  return sources;
};

// Helper functions
export const getLocalizedOrderNotes = (
  notes: { tr?: string; en?: string } | undefined,
  language: 'tr' | 'en' = 'tr'
): string => {
  if (!notes) return '';
  return language === 'en' && notes.en ? notes.en : notes.tr || '';
};

export const calculateOrderTotal = (order: Order): number => {
  const itemsTotal = order.orderItems.reduce((total, item) => {
    const itemTotal = item.price * item.quantity;
    const discountAmount = item.discount ? (itemTotal * item.discount) / 100 : 0;
    return total + (itemTotal - discountAmount);
  }, 0);

  const campaignDiscount = order.discountAmount || 0;
  return itemsTotal - campaignDiscount;
};

export const getOrderStatusColor = (status: string): string => {
  const statusColors: Record<string, string> = {
    pending: '#f59e0b',
    confirmed: '#3b82f6',
    shipped: '#8b5cf6',
    delivered: '#10b981',
    cancelled: '#ef4444',
  };
  return statusColors[status] || '#6b7280';
};

export const getPaymentStatusColor = (status: string): string => {
  const statusColors: Record<string, string> = {
    pending: '#f59e0b',
    paid: '#10b981',
    cancelled: '#ef4444',
  };
  return statusColors[status] || '#6b7280';
};

export const formatOrderNumber = (orderNumber: string): string => {
  return orderNumber.replace(/^ORD-/, '#');
};

export const isOrderEditable = (order: Order): boolean => {
  return order.orderStatus === 'pending' && order.paymentStatus === 'pending';
};

export const isOrderCancellable = (order: Order): boolean => {
  return ['pending', 'confirmed'].includes(order.orderStatus) && 
         order.paymentStatus !== 'cancelled';
};

export const getOrderProgress = (status: string): number => {
  const progressMap: Record<string, number> = {
    pending: 25,
    confirmed: 50,
    shipped: 75,
    delivered: 100,
    cancelled: 0,
  };
  return progressMap[status] || 0;
};

// Filter and sort helpers
export const filterOrdersByDateRange = (orders: Order[], dateFrom?: string, dateTo?: string): Order[] => {
  if (!dateFrom && !dateTo) return orders;
  
  return orders.filter(order => {
    const orderDate = new Date(order.createdAt);
    const fromDate = dateFrom ? new Date(dateFrom) : null;
    const toDate = dateTo ? new Date(dateTo) : null;
    
    if (fromDate && orderDate < fromDate) return false;
    if (toDate && orderDate > toDate) return false;
    
    return true;
  });
};

export const sortOrders = (
  orders: Order[], 
  sortBy: 'orderNumber' | 'createdAt' | 'totalAmount' = 'createdAt',
  sortOrder: 'asc' | 'desc' = 'desc'
): Order[] => {
  return [...orders].sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'orderNumber':
        comparison = a.orderNumber.localeCompare(b.orderNumber);
        break;
      case 'totalAmount':
        comparison = a.totalAmount - b.totalAmount;
        break;
      case 'createdAt':
      default:
        comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        break;
    }
    
    return sortOrder === 'desc' ? -comparison : comparison;
  });
};
