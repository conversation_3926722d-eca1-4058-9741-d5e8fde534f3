import { NextRequest, NextResponse } from 'next/server'
import { client } from '@/sanity/lib/client'

export async function POST(req: NextRequest) {
  try {
    console.log('🔄 Starting user migration from id to clerkId')
    
    // Fetch all users that have 'id' field but no 'clerkId' field
    const usersToMigrate = await client.fetch(`
      *[_type == "user" && defined(id) && !defined(clerkId)] {
        _id,
        id,
        name,
        email,
        isAdminApproved,
        imageUrl
      }
    `)
    
    console.log(`📊 Found ${usersToMigrate.length} users to migrate`)
    
    if (usersToMigrate.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No users need migration',
        migratedCount: 0
      })
    }
    
    const migrationResults = []
    
    for (const user of usersToMigrate) {
      try {
        console.log(`🔄 Migrating user: ${user.name} (${user.email})`)
        
        // Update user to add clerkId field and remove old id field
        const result = await client
          .patch(user._id)
          .set({ 
            clerkId: user.id  // Copy id to clerkId
          })
          .unset(['id'])  // Remove old id field
          .commit()
        
        migrationResults.push({
          _id: user._id,
          name: user.name,
          email: user.email,
          oldId: user.id,
          success: true
        })
        
        console.log(`✅ Migrated user: ${user.name}`)
        
      } catch (error) {
        console.error(`❌ Failed to migrate user ${user.name}:`, error)
        migrationResults.push({
          _id: user._id,
          name: user.name,
          email: user.email,
          oldId: user.id,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }
    
    const successCount = migrationResults.filter(r => r.success).length
    const failureCount = migrationResults.filter(r => !r.success).length
    
    console.log(`✅ Migration completed: ${successCount} success, ${failureCount} failures`)
    
    return NextResponse.json({
      success: true,
      message: `Migration completed: ${successCount} users migrated successfully`,
      migratedCount: successCount,
      failureCount: failureCount,
      results: migrationResults
    })
    
  } catch (error) {
    console.error('❌ Migration error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function GET() {
  try {
    console.log('📊 Checking migration status')
    
    // Check current state
    const usersWithId = await client.fetch(`count(*[_type == "user" && defined(id)])`)
    const usersWithClerkId = await client.fetch(`count(*[_type == "user" && defined(clerkId)])`)
    const totalUsers = await client.fetch(`count(*[_type == "user"])`)
    
    const usersNeedingMigration = await client.fetch(`
      *[_type == "user" && defined(id) && !defined(clerkId)] {
        _id,
        id,
        name,
        email
      }
    `)
    
    return NextResponse.json({
      success: true,
      status: {
        totalUsers,
        usersWithId,
        usersWithClerkId,
        usersNeedingMigration: usersNeedingMigration.length,
        users: usersNeedingMigration
      }
    })
    
  } catch (error) {
    console.error('❌ Status check error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
