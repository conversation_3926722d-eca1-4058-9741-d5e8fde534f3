
import { defineLive } from "next-sanity";
import { client } from "@/sanity/lib/client";

const token = process.env.SANITY_API_READ_TOKEN;

if (!token) {
  throw new Error("Missing SANITY_API_READ_TOKEN");
}

export const { sanityFetch, SanityLive } = defineLive({
  client: client.withConfig({     
    // Use write token for draft mode
    token: process.env.SANITY_API_WRITE_TOKEN,
    apiVersion: '2025-06-11',
    useCdn: false, // Disable CDN for draft mode
    perspective: 'published', // Default to published
  }),
  serverToken: token,
  browserToken: token,
  fetchOptions: {
    revalidate: 0,
  },
});
