// Sanity Actions for Wallet Management
import { client } from '../client';

// Update user wallet balance
export const updateWalletBalance = async (userId: string, amount: number, operation: 'add' | 'subtract' = 'add'): Promise<{ success: boolean; newBalance: number; error?: string }> => {
  try {
    // Get current balance
    const user = await client.fetch(
      `*[_type == "user" && _id == $userId][0]{walletBalance, currency}`,
      { userId }
    );
    
    if (!user) {
      return { success: false, newBalance: 0, error: 'Kullanıcı bulunamadı' };
    }
    
    const currentBalance = user.walletBalance || 0;
    let newBalance: number;
    
    if (operation === 'add') {
      newBalance = currentBalance + amount;
    } else {
      newBalance = currentBalance - amount;
      
      // Check for sufficient balance
      if (newBalance < 0) {
        return { success: false, newBalance: currentBalance, error: 'Yetersiz bakiye' };
      }
    }
    
    // Update user balance
    await client
      .patch(userId)
      .set({ walletBalance: newBalance })
      .commit();
    
    return { success: true, newBalance };
  } catch (error) {
    console.error('Cüzdan bakiyesi güncelleme hatası:', error);
    return { success: false, newBalance: 0, error: 'Bakiye güncellenemedi' };
  }
};

// Process wallet top-up request
export const processWalletTopUpRequest = async (
  requestId: string, 
  action: 'approve' | 'reject', 
  adminUserId: string,
  reason?: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    // Get the top-up request
    const request = await client.fetch(
      `*[_type == "walletTopUpRequest" && _id == $requestId][0]{user, amount, currency, status}`,
      { requestId }
    );
    
    if (!request) {
      return { success: false, error: 'Yükleme talebi bulunamadı' };
    }
    
    if (request.status !== 'pending') {
      return { success: false, error: 'Bu talep zaten işlenmiş' };
    }
    
    const updateData: any = {
      status: action === 'approve' ? 'approved' : 'rejected',
      approvedAt: new Date().toISOString(),
      processedBy: { _type: 'reference', _ref: adminUserId }
    };
    
    if (reason) {
      updateData.reason = reason;
    }
    
    // Update the request status
    await client
      .patch(requestId)
      .set(updateData)
      .commit();
    
    // If approved, update user wallet balance and create transaction
    if (action === 'approve') {
      const balanceResult = await updateWalletBalance(request.user._ref, request.amount, 'add');
      
      if (!balanceResult.success) {
        // Rollback the request status
        await client
          .patch(requestId)
          .set({ status: 'pending' })
          .commit();
        
        return { success: false, error: balanceResult.error };
      }
      
      // Create wallet transaction record
      await createWalletTransaction({
        user: { _type: 'reference', _ref: request.user._ref },
        amount: request.amount,
        currency: request.currency,
        type: 'deposit',
        description: 'Cüzdan yükleme talebi onaylandı',
        relatedTopUpRequest: { _type: 'reference', _ref: requestId },
        status: 'completed',
        balanceBefore: balanceResult.newBalance - request.amount,
        balanceAfter: balanceResult.newBalance,
        processedBy: { _type: 'reference', _ref: adminUserId }
      });
    }
    
    return { success: true };
  } catch (error) {
    console.error('Cüzdan yükleme talebi işleme hatası:', error);
    return { success: false, error: 'Talep işlenemedi' };
  }
};

// Create wallet transaction
export const createWalletTransaction = async (transactionData: any): Promise<{ success: boolean; transaction?: any; error?: string }> => {
  try {
    // Get user's current balance if not provided
    if (!transactionData.balanceBefore) {
      const user = await client.fetch(
        `*[_type == "user" && _id == $userId][0]{walletBalance}`,
        { userId: transactionData.user._ref }
      );
      
      if (user) {
        transactionData.balanceBefore = user.walletBalance;
      }
    }
    
    // Calculate balance after if not provided
    if (!transactionData.balanceAfter) {
      const balanceChange = ['deposit', 'refund', 'bonus'].includes(transactionData.type) 
        ? transactionData.amount 
        : -transactionData.amount;
      
      transactionData.balanceAfter = (transactionData.balanceBefore || 0) + balanceChange;
    }
    
    // Create transaction
    const transaction = await client.create({
      _type: 'walletTransaction',
      ...transactionData,
      createdAt: new Date().toISOString(),
      processedAt: transactionData.status === 'completed' ? new Date().toISOString() : undefined
    });
    
    return { success: true, transaction };
  } catch (error) {
    console.error('Cüzdan işlemi oluşturma hatası:', error);
    return { success: false, error: 'İşlem oluşturulamadı' };
  }
};

// Process wallet transaction (approve/reject pending transactions)
export const processWalletTransaction = async (
  transactionId: string, 
  action: 'approve' | 'reject', 
  adminUserId: string,
  notes?: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    // Get the transaction
    const transaction = await client.fetch(
      `*[_type == "walletTransaction" && _id == $transactionId][0]{user, amount, type, status, balanceAfter}`,
      { transactionId }
    );
    
    if (!transaction) {
      return { success: false, error: 'İşlem bulunamadı' };
    }
    
    if (transaction.status !== 'pending') {
      return { success: false, error: 'Bu işlem zaten işlenmiş' };
    }
    
    const updateData: any = {
      status: action === 'approve' ? 'completed' : 'cancelled',
      processedAt: new Date().toISOString(),
      processedBy: { _type: 'reference', _ref: adminUserId }
    };
    
    if (notes) {
      updateData.notes = notes;
    }
    
    // If approved, update user wallet balance
    if (action === 'approve') {
      const balanceChange = ['deposit', 'refund', 'bonus'].includes(transaction.type) 
        ? transaction.amount 
        : -transaction.amount;
      
      const balanceResult = await updateWalletBalance(
        transaction.user._ref, 
        transaction.amount, 
        ['deposit', 'refund', 'bonus'].includes(transaction.type) ? 'add' : 'subtract'
      );
      
      if (!balanceResult.success) {
        return { success: false, error: balanceResult.error };
      }
      
      updateData.balanceAfter = balanceResult.newBalance;
    }
    
    // Update transaction
    await client
      .patch(transactionId)
      .set(updateData)
      .commit();
    
    return { success: true };
  } catch (error) {
    console.error('Cüzdan işlemi onaylama hatası:', error);
    return { success: false, error: 'İşlem onaylanamadı' };
  }
};

// Create refund transaction for order
export const createRefundTransaction = async (
  orderId: string, 
  userId: string, 
  refundAmount: number, 
  adminUserId: string,
  reason?: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    // Get order details
    const order = await client.fetch(
      `*[_type == "order" && _id == $orderId][0]{customer, totalAmount, currency, orderStatus}`,
      { orderId }
    );
    
    if (!order) {
      return { success: false, error: 'Sipariş bulunamadı' };
    }
    
    if (order.orderStatus === 'cancelled') {
      return { success: false, error: 'İptal edilmiş sipariş için iade yapılamaz' };
    }
    
    // Create refund transaction
    const transactionResult = await createWalletTransaction({
      user: { _type: 'reference', _ref: userId },
      amount: refundAmount,
      currency: order.currency,
      type: 'refund',
      description: reason || `Sipariş iadesi - ${order.orderNumber || orderId}`,
      relatedOrder: { _type: 'reference', _ref: orderId },
      status: 'completed',
      processedBy: { _type: 'reference', _ref: adminUserId }
    });
    
    if (!transactionResult.success) {
      return { success: false, error: transactionResult.error };
    }
    
    // Update user wallet balance
    const balanceResult = await updateWalletBalance(userId, refundAmount, 'add');
    
    if (!balanceResult.success) {
      return { success: false, error: balanceResult.error };
    }
    
    return { success: true };
  } catch (error) {
    console.error('İade işlemi oluşturma hatası:', error);
    return { success: false, error: 'İade işlemi oluşturulamadı' };
  }
};

// Get user wallet summary
export const getUserWalletSummary = async (userId: string): Promise<{
  balance: number;
  currency: string;
  totalDeposits: number;
  totalWithdrawals: number;
  totalRefunds: number;
  pendingTransactions: number;
}> => {
  try {
    // Get user balance
    const user = await client.fetch(
      `*[_type == "user" && _id == $userId][0]{walletBalance, currency}`,
      { userId }
    );
    
    // Get transaction summary
    const transactions = await client.fetch(
      `*[_type == "walletTransaction" && user._ref == $userId && status == "completed"]{type, amount}`,
      { userId }
    );
    
    const pendingCount = await client.fetch(
      `count(*[_type == "walletTransaction" && user._ref == $userId && status == "pending"])`,
      { userId }
    );
    
    const summary = transactions.reduce((acc: any, transaction: any) => {
      switch (transaction.type) {
        case 'deposit':
          acc.totalDeposits += transaction.amount;
          break;
        case 'withdrawal':
          acc.totalWithdrawals += transaction.amount;
          break;
        case 'refund':
          acc.totalRefunds += transaction.amount;
          break;
      }
      return acc;
    }, {
      totalDeposits: 0,
      totalWithdrawals: 0,
      totalRefunds: 0
    });
    
    return {
      balance: user?.walletBalance || 0,
      currency: user?.currency || 'TRY',
      ...summary,
      pendingTransactions: pendingCount
    };
  } catch (error) {
    console.error('Cüzdan özeti alma hatası:', error);
    return {
      balance: 0,
      currency: 'TRY',
      totalDeposits: 0,
      totalWithdrawals: 0,
      totalRefunds: 0,
      pendingTransactions: 0
    };
  }
};

// Auto-update sale status based on validity
export const updateSaleStatus = async (saleId: string): Promise<{ success: boolean; newStatus?: boolean; error?: string }> => {
  try {
    const sale = await client.fetch(
      `*[_type == "sales" && _id == $saleId][0]{validityUntil, isActive}`,
      { saleId }
    );
    
    if (!sale) {
      return { success: false, error: 'Kampanya bulunamadı' };
    }
    
    const now = new Date();
    const validityUntil = new Date(sale.validityUntil);
    const shouldBeActive = now <= validityUntil;
    
    // Update status if it has changed
    if (sale.isActive !== shouldBeActive) {
      await client
        .patch(saleId)
        .set({ isActive: shouldBeActive })
        .commit();
      
      return { success: true, newStatus: shouldBeActive };
    }
    
    return { success: true, newStatus: sale.isActive };
  } catch (error) {
    console.error('Kampanya durumu güncelleme hatası:', error);
    return { success: false, error: 'Kampanya durumu güncellenemedi' };
  }
};
