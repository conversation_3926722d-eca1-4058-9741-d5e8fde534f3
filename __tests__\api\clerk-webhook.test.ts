import { POST } from '@/app/api/clerk-webhook/route'
import { client } from '@/sanity/lib/client'
import { Webhook } from 'svix'

// Mock dependencies
jest.mock('@/sanity/lib/client')
jest.mock('svix')

const mockClient = client as jest.Mocked<typeof client>
const mockWebhook = Webhook as jest.MockedClass<typeof Webhook>

describe('/api/clerk-webhook', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    process.env.CLERK_WEBHOOK_SECRET = 'test-webhook-secret'
  })

  it('should return 400 if webhook secret is missing', async () => {
    delete process.env.CLERK_WEBHOOK_SECRET

    await expect(async () => {
      const request = new Request('http://localhost/api/clerk-webhook', {
        method: 'POST',
        headers: {
          'svix-id': 'test-id',
          'svix-timestamp': '1234567890',
          'svix-signature': 'test-signature',
        },
        body: JSON.stringify({ type: 'user.created', data: { id: 'user-123' } }),
      })
      await POST(request)
    }).rejects.toThrow('Please add CLERK_WEBHOOK_SECRET')
  })

  it('should return 400 if svix headers are missing', async () => {
    const request = new Request('http://localhost/api/clerk-webhook', {
      method: 'POST',
      body: JSON.stringify({ type: 'user.created', data: { id: 'user-123' } }),
    })

    const response = await POST(request)
    expect(response.status).toBe(400)
    
    const text = await response.text()
    expect(text).toBe('Error occured -- no svix headers')
  })

  it('should create user in Sanity when user.created event is received', async () => {
    const mockVerify = jest.fn().mockReturnValue({
      type: 'user.created',
      data: {
        id: 'clerk-123',
        email_addresses: [{ email_address: '<EMAIL>' }],
        first_name: 'John',
        last_name: 'Doe',
        image_url: 'https://example.com/image.jpg'
      }
    })

    mockWebhook.mockImplementation(() => ({
      verify: mockVerify
    } as any))

    mockClient.createIfNotExists.mockResolvedValue({
      _id: 'user-123',
      _type: 'user',
      clerkId: 'clerk-123',
      name: 'John Doe',
      email: '<EMAIL>',
      isAdminApproved: false,
      imageUrl: 'https://example.com/image.jpg'
    } as any)

    const request = new Request('http://localhost/api/clerk-webhook', {
      method: 'POST',
      headers: {
        'svix-id': 'test-id',
        'svix-timestamp': '1234567890',
        'svix-signature': 'test-signature',
      },
      body: JSON.stringify({
        type: 'user.created',
        data: {
          id: 'clerk-123',
          email_addresses: [{ email_address: '<EMAIL>' }],
          first_name: 'John',
          last_name: 'Doe',
          image_url: 'https://example.com/image.jpg'
        }
      }),
    })

    const response = await POST(request)
    expect(response.status).toBe(201)

    const text = await response.text()
    expect(text).toBe('User created in Sanity')

    expect(mockClient.createIfNotExists).toHaveBeenCalledWith({
      _type: 'user',
      _id: 'clerk-123',
      clerkId: 'clerk-123',
      name: 'John Doe',
      email: '<EMAIL>',
      isAdminApproved: false,
      imageUrl: 'https://example.com/image.jpg'
    })
  })

  it('should return 400 if user id is missing in user.created event', async () => {
    const mockVerify = jest.fn().mockReturnValue({
      type: 'user.created',
      data: {
        email_addresses: [{ email_address: '<EMAIL>' }],
        first_name: 'John',
        last_name: 'Doe'
      }
    })

    mockWebhook.mockImplementation(() => ({
      verify: mockVerify
    } as any))

    const request = new Request('http://localhost/api/clerk-webhook', {
      method: 'POST',
      headers: {
        'svix-id': 'test-id',
        'svix-timestamp': '1234567890',
        'svix-signature': 'test-signature',
      },
      body: JSON.stringify({
        type: 'user.created',
        data: {
          email_addresses: [{ email_address: '<EMAIL>' }],
          first_name: 'John',
          last_name: 'Doe'
        }
      }),
    })

    const response = await POST(request)
    expect(response.status).toBe(400)

    const text = await response.text()
    expect(text).toBe('Error occured -- no user id')
  })

  it('should handle webhook verification errors', async () => {
    const mockVerify = jest.fn().mockImplementation(() => {
      throw new Error('Invalid signature')
    })

    mockWebhook.mockImplementation(() => ({
      verify: mockVerify
    } as any))

    const request = new Request('http://localhost/api/clerk-webhook', {
      method: 'POST',
      headers: {
        'svix-id': 'test-id',
        'svix-timestamp': '1234567890',
        'svix-signature': 'invalid-signature',
      },
      body: JSON.stringify({
        type: 'user.created',
        data: { id: 'clerk-123' }
      }),
    })

    const response = await POST(request)
    expect(response.status).toBe(400)

    const text = await response.text()
    expect(text).toBe('Error occured')
  })

  it('should return 200 for non-user.created events', async () => {
    const mockVerify = jest.fn().mockReturnValue({
      type: 'user.updated',
      data: { id: 'clerk-123' }
    })

    mockWebhook.mockImplementation(() => ({
      verify: mockVerify
    } as any))

    const request = new Request('http://localhost/api/clerk-webhook', {
      method: 'POST',
      headers: {
        'svix-id': 'test-id',
        'svix-timestamp': '1234567890',
        'svix-signature': 'test-signature',
      },
      body: JSON.stringify({
        type: 'user.updated',
        data: { id: 'clerk-123' }
      }),
    })

    const response = await POST(request)
    expect(response.status).toBe(200)
  })
})
