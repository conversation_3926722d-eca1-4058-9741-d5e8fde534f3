import { TagIcon } from '@sanity/icons';
import { defineArrayMember, defineField, defineType } from 'sanity';
import { nanoid } from 'nanoid';

// Helper function for image field
const defineImageField = (title = 'Görsel') =>
  defineField({
    name: 'image',
    title,
    type: 'image',
    options: { hotspot: true },
    fields: [
      {
        name: 'alt',
        title: 'Alternatif Metin',
        type: 'string',
        validation: (Rule) => Rule.required().error('Alternatif metin zorunludur.'),
      },
    ],
  });

export const Auction = defineType({
  name: 'auction',
  title: 'Açık Artırma',
  type: 'document',
  icon: TagIcon,
  groups: [
    { name: 'basic', title: 'Temel Bilgiler' },
    { name: 'bidding', title: 'Teklif Bilgileri' },
    { name: 'status', title: 'Durum' },
  ],
  fields: [
    defineField({
      name: 'id',
      title: 'Açık Artırma ID',
      type: 'string',
      group: 'basic',
      readOnly: true,
      initialValue: () => `AUC-${nanoid(8)}`,
      validation: (Rule) => Rule.required().error('Açık Artırma ID zorunludur.'),
    }),
    defineField({
      name: 'product',
      title: 'Ürün',
      type: 'reference',
      to: [{ type: 'product' }],
      group: 'basic',
      validation: (Rule) => Rule.required().error('Ürün seçimi zorunludur.'),
    }),
    defineField({
      name: 'description',
      title: 'Açıklama',
      type: 'object',
      group: 'basic',
      fields: [
        { name: 'tr', title: 'Türkçe', type: 'text' },
        { name: 'en', title: 'İngilizce', type: 'text' },
      ],
    }),
    defineImageField('Açık Artırma Görseli'),
    defineField({
      name: 'startingBid',
      title: 'Başlangıç Teklifi',
      type: 'number',
      group: 'bidding',
      validation: (Rule) => Rule.required().min(0).max(1000000).error('Başlangıç teklifi 0 ile 1.000.000 arasında olmalıdır.'),
    }),
    defineField({
      name: 'currentBid',
      title: 'Mevcut Teklif',
      type: 'number',
      group: 'bidding',
      initialValue: 0,
      validation: (Rule) =>
        Rule.min(0).custom((value, context) => {
          if (value && value < context.document?.startingBid) {
            return 'Mevcut teklif, başlangıç teklifinden düşük olamaz.';
          }
          return true;
        }),
    }),
    defineField({
      name: 'bidIncrementAmount',
      title: 'Minimum Teklif Artış Miktarı',
      type: 'number',
      group: 'bidding',
      initialValue: 1,
      validation: (Rule) => Rule.required().min(0).max(10000).error('Minimum teklif artış miktarı 0 ile 10.000 arasında olmalıdır.'),
    }),
    defineField({
      name: 'currency',
      title: 'Para Birimi',
      type: 'string',
      group: 'bidding',
      options: {
        list: [
          { title: 'Türk Lirası (TRY)', value: 'TRY' },
          { title: 'ABD Doları (USD)', value: 'USD' },
          { title: 'Euro (EUR)', value: 'EUR' },
        ],
      },
      initialValue: 'TRY',
      validation: (Rule) => Rule.required().error('Para birimi zorunludur.'),
    }),
    defineField({
      name: 'bidders',
      title: 'Teklif Verenler',
      type: 'array',
      group: 'bidding',
      of: [
        defineArrayMember({
          type: 'reference',
          to: [{ type: 'user' }],
          options: { filter: 'isAdminApproved == true' },
        }),
      ],
      description: 'Teklif veren kullanıcılar (otomatik olarak doldurulur)',
      readOnly: true,
    }),
    defineField({
      name: 'bidHistory',
      title: 'Teklif Geçmişi',
      type: 'array',
      group: 'bidding',
      of: [
        defineArrayMember({
          type: 'object',
          fields: [
            defineField({
              name: 'bidAmount',
              title: 'Teklif Miktarı',
              type: 'number',
              validation: (Rule) =>
                Rule.required()
                  .min(0)
                  .custom((value, context) => {
                    const parent = context.document;
                    const minBid = parent?.currentBid
                      ? parent.currentBid + (parent.bidIncrementAmount || 1)
                      : parent?.startingBid || 0;
                    if (value < minBid) {
                      return `Teklif miktarı en az ${minBid} olmalıdır.`;
                    }
                    return true;
                  }),
            }),
            defineField({
              name: 'bidder',
              title: 'Teklif Veren',
              type: 'reference',
              to: [{ type: 'user' }],
              options: { filter: 'isAdminApproved == true' },
              validation: (Rule) => Rule.required().error('Teklif veren zorunludur.'),
            }),
            defineField({
              name: 'bidTime',
              title: 'Teklif Zamanı',
              type: 'datetime',
              initialValue: () => new Date().toISOString(),
              validation: (Rule) => Rule.required(),
            }),
          ],
          preview: {
            select: {
              title: 'bidder.name.tr',
              subtitle: 'bidAmount',
              date: 'bidTime',
              currency: 'currency',
            },
            prepare({ title, subtitle, date, currency }) {
              return {
                title: `Teklif Veren: ${title || 'Bilinmiyor'}`,
                subtitle: `Miktar: ${subtitle || 0} ${currency || 'TRY'} - Zaman: ${
                  date ? new Date(date).toLocaleString('tr-TR') : 'Bilinmiyor'
                }`,
              };
            },
          },
        }),
      ],
    }),
    defineField({
      name: 'winner',
      title: 'Kazanan',
      type: 'reference',
      to: [{ type: 'user' }],
      group: 'bidding',
      options: { filter: 'isAdminApproved == true' },
      hidden: ({ document }) => document?.status !== 'completed',
    }),
    defineField({
      name: 'startTime',
      title: 'Başlangıç Zamanı',
      type: 'datetime',
      group: 'status',
      validation: (Rule) => Rule.required().error('Başlangıç zamanı zorunludur.'),
    }),
    defineField({
      name: 'endTime',
      title: 'Bitiş Zamanı',
      type: 'datetime',
      group: 'status',
      validation: (Rule) =>
        Rule.required()
          .min(Rule.valueOfField('startTime'))
          .error('Bitiş zamanı, başlangıç zamanından sonra olmalıdır.'),
    }),
    defineField({
      name: 'status',
      title: 'Durum',
      type: 'string',
      group: 'status',
      options: {
        list: [
          { title: 'Bekliyor', value: 'pending' },
          { title: 'Aktif', value: 'active' },
          { title: 'Tamamlandı', value: 'completed' },
          { title: 'İptal', value: 'cancelled' },
        ],
      },
      initialValue: 'pending',
      validation: (Rule) => Rule.required().error('Durum zorunludur.'),
    }),
    defineField({
      name: 'sale',
      title: 'Bağlı Kampanya',
      type: 'reference',
      to: [{ type: 'sales' }],
      group: 'status',
      description: 'Açık artırmaya bağlı kampanya (isteğe bağlı).',
    }),
  ],
  preview: {
    select: {
      title: 'product.name.tr',
      subtitle: 'currentBid',
      currency: 'currency',
      status: 'status',
      endTime: 'endTime',
      media: 'image',
    },
    prepare({ title, subtitle, currency, status, endTime, media }) {
      return {
        title: title || 'İsimsiz Açık Artırma',
        subtitle: `${subtitle ? `${subtitle} ${currency || 'TRY'}` : 'Teklif Yok'} - ${status || 'Durum Yok'} - Bitiş: ${
          endTime ? new Date(endTime).toLocaleString('tr-TR') : 'Tanımsız'
        }`,
        media,
      };
    },
  },
});