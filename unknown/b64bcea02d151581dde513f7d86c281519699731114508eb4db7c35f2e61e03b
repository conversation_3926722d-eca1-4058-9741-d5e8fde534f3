'use client';

import { useEffect } from 'react';

export default function ErrorSuppression() {
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      // Suppress console.error for known Sanity UI issues
      const originalError = console.error;
      console.error = (...args) => {
        const message = args[0];
        
        // Check if it's a React prop warning
        if (typeof message === 'string') {
          // Suppress disableTransition warnings
          if (message.includes('disableTransition') && message.includes('React does not recognize')) {
            return;
          }
          
          // Suppress other known Sanity UI warnings
          if (message.includes('React does not recognize') && 
              (message.includes('styled-components') || 
               message.includes('@sanity/ui'))) {
            return;
          }
          
          // Suppress transition-related warnings
          if (message.includes('transition') && message.includes('DOM element')) {
            return;
          }
        }
        
        // Call original error for other messages
        originalError.apply(console, args);
      };

      // Suppress console.warn for React warnings
      const originalWarn = console.warn;
      console.warn = (...args) => {
        const message = args[0];
        
        if (typeof message === 'string') {
          // Suppress React warnings about unknown props
          if (message.includes('React does not recognize') || 
              message.includes('disableTransition') ||
              message.includes('styled-components')) {
            return;
          }
        }
        
        originalWarn.apply(console, args);
      };

      // Cleanup function
      return () => {
        console.error = originalError;
        console.warn = originalWarn;
      };
    }
  }, []);

  return null;
}
