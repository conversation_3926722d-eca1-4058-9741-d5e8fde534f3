// Test file to verify our new API and types work correctly
import { useProducts, useCategories } from './hooks/useProducts';
import { useCategories as useCategoriesHook } from './hooks/useCategories';
import { Product, Category } from './types/sanity';

// Test function to verify types
function testTypes() {
  // Test Product type
  const product: Product = {
    _id: 'test-id',
    _type: 'product',
    _createdAt: '2024-01-01',
    _updatedAt: '2024-01-01',
    _rev: 'test-rev',
    id: 'PRD-12345678',
    name: {
      tr: 'Test Ürün',
      en: 'Test Product'
    },
    slug: {
      current: 'test-urun',
      _type: 'slug'
    },
    image: {
      asset: {
        _ref: 'image-ref',
        _type: 'reference'
      },
      alt: 'Test image'
    },
    description: {
      tr: 'Test açıklama',
      en: 'Test description'
    },
    category: {
      _ref: 'category-ref',
      _type: 'reference'
    },
    tags: ['test', 'örnek'],
    price: 100,
    currency: 'TRY',
    discount: 10,
    sku: 'PROD-123456',
    stock: 50,
    isAvailable: true,
    variants: [
      {
        name: 'Renk',
        value: 'Kırmızı',
        stock: 25,
        priceModifier: 0
      }
    ],
    flags: ['featured', 'new'],
    seoTitle: 'Test SEO Title',
    seoDescription: 'Test SEO Description'
  };

  // Test Category type
  const category: Category = {
    _id: 'category-id',
    _type: 'category',
    _createdAt: '2024-01-01',
    _updatedAt: '2024-01-01',
    _rev: 'category-rev',
    title: {
      tr: 'Test Kategori',
      en: 'Test Category'
    },
    slug: {
      current: 'test-kategori',
      _type: 'slug'
    },
    image: {
      asset: {
        _ref: 'category-image-ref',
        _type: 'reference'
      },
      alt: 'Category image'
    },
    description: {
      tr: 'Kategori açıklaması',
      en: 'Category description'
    },
    parentCategory: {
      _ref: 'parent-category-ref',
      _type: 'reference'
    },
    tags: ['kategori', 'test'],
    flags: ['featured'],
    seoTitle: 'Category SEO Title',
    seoDescription: 'Category SEO Description'
  };

  console.log('Types test passed!', { product, category });
}

// Test React Query hooks (this would be used in a React component)
function TestComponent() {
  // Test useProducts hook
  const { data: products, isLoading: productsLoading, error: productsError } = useProducts({
    category: 'electronics',
    flags: ['featured'],
    minPrice: 50,
    maxPrice: 500,
    inStock: true,
    search: 'laptop',
    page: 1,
    limit: 20,
    sortBy: 'name',
    sortOrder: 'asc',
    language: 'tr'
  });

  // Test useCategories hook
  const { data: categories, isLoading: categoriesLoading, error: categoriesError } = useCategoriesHook({
    parentCategory: 'electronics',
    flags: ['featured'],
    language: 'tr'
  });

  if (productsLoading || categoriesLoading) {
    return <div>Loading...</div>;
  }

  if (productsError || categoriesError) {
    return <div>Error: {productsError?.message || categoriesError?.message}</div>;
  }

  return (
    <div>
      <h2>Products ({products?.length || 0})</h2>
      {products?.map(product => (
        <div key={product._id}>
          <h3>{product.name.tr}</h3>
          <p>{product.price} {product.currency}</p>
          {product.discount && <span>%{product.discount} indirim</span>}
          <p>Stok: {product.stock}</p>
          {product.flags && <div>Özellikler: {product.flags.join(', ')}</div>}
        </div>
      ))}

      <h2>Categories ({categories?.length || 0})</h2>
      {categories?.map(category => (
        <div key={category._id}>
          <h3>{category.title.tr}</h3>
          {category.description?.tr && <p>{category.description.tr}</p>}
          {category.flags && <div>Özellikler: {category.flags.join(', ')}</div>}
        </div>
      ))}
    </div>
  );
}

export { testTypes, TestComponent };
