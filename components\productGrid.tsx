"use client";

import { Product } from "@/sanity.types";
import { AnimatePresence, motion } from "framer-motion";
import ProductThumb from "./ProductThumb";

function ProductGrid({ products }: { products: Product[] }) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 w-full">
      {products?.map((product) => (
        <div key={product._id} className="w-full">
          <ProductThumb product={product} />
        </div>
      ))}
    </div>
  );
}

export default ProductGrid;
