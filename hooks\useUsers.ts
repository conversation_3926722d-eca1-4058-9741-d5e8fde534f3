import { useQuery } from '@tanstack/react-query';
import { api } from '../lib/api';
import { 
  User, 
  UserWithDetails,
  ApiResponse 
} from '../types/sanity';

// Query parameters interface
export interface UserQueryParams {
  status?: 'active' | 'inactive' | 'suspended';
  role?: 'user' | 'moderator' | 'finance_manager' | 'admin';
  isAdminApproved?: boolean;
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'email' | 'createdAt' | 'lastLogin' | 'walletBalance';
  sortOrder?: 'asc' | 'desc';
}

// API functions
const fetchUsers = async (params?: UserQueryParams): Promise<User[]> => {
  const response = await api.get('/users', params);
  return response.data;
};

const fetchUserById = async (id: string): Promise<User | null> => {
  const response = await api.get(`/users/${id}`);
  return response.data;
};

const fetchUserByClerkId = async (clerkId: string): Promise<User | null> => {
  const response = await api.get(`/users/clerk/${clerkId}`);
  return response.data;
};

const fetchUserByEmail = async (email: string): Promise<User | null> => {
  const response = await api.get(`/users/email/${email}`);
  return response.data;
};

const fetchPendingUsers = async (): Promise<User[]> => {
  const response = await api.get('/users/pending-approval');
  return response.data;
};

const fetchAdminUsers = async (): Promise<User[]> => {
  const response = await api.get('/users/admins');
  return response.data;
};

// React Query hooks
export const useUsers = (params?: UserQueryParams) => {
  return useQuery<User[], Error>({
    queryKey: ['users', params],
    queryFn: () => fetchUsers(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    refetchOnWindowFocus: false,
  });
};

export const useUser = (id: string) => {
  return useQuery<User | null, Error>({
    queryKey: ['user', id],
    queryFn: () => fetchUserById(id),
    staleTime: 5 * 60 * 1000,
    retry: 2,
    enabled: !!id,
  });
};

export const useUserByClerkId = (clerkId: string) => {
  return useQuery<User | null, Error>({
    queryKey: ['user', 'clerk', clerkId],
    queryFn: () => fetchUserByClerkId(clerkId),
    staleTime: 5 * 60 * 1000,
    retry: 2,
    enabled: !!clerkId,
  });
};

export const useUserByEmail = (email: string) => {
  return useQuery<User | null, Error>({
    queryKey: ['user', 'email', email],
    queryFn: () => fetchUserByEmail(email),
    staleTime: 5 * 60 * 1000,
    retry: 2,
    enabled: !!email,
  });
};

export const usePendingUsers = () => {
  return useQuery<User[], Error>({
    queryKey: ['users', 'pending'],
    queryFn: fetchPendingUsers,
    staleTime: 2 * 60 * 1000, // 2 minutes for pending users
    retry: 2,
  });
};

export const useAdminUsers = () => {
  return useQuery<User[], Error>({
    queryKey: ['users', 'admins'],
    queryFn: fetchAdminUsers,
    staleTime: 10 * 60 * 1000, // 10 minutes for admin users
    retry: 2,
  });
};

// Utility hooks
export const useUserStatuses = () => {
  const statuses = [
    { value: 'active', label: 'Aktif', color: 'green' },
    { value: 'inactive', label: 'Pasif', color: 'gray' },
    { value: 'suspended', label: 'Askıya Alındı', color: 'red' },
  ] as const;

  return statuses;
};

export const useUserRoles = () => {
  const roles = [
    { value: 'user', label: 'Kullanıcı', permissions: ['read'] },
    { value: 'moderator', label: 'Moderatör', permissions: ['read', 'moderate'] },
    { value: 'finance_manager', label: 'Finans Yöneticisi', permissions: ['read', 'finance'] },
    { value: 'admin', label: 'Admin', permissions: ['read', 'write', 'delete', 'admin'] },
  ] as const;

  return roles;
};

// Helper functions
export const getLocalizedUserName = (
  name: { tr: string; en?: string } | undefined,
  language: 'tr' | 'en' = 'tr'
): string => {
  if (!name) return '';
  return language === 'en' && name.en ? name.en : name.tr;
};

export const getUserStatusColor = (status: string): string => {
  const statusColors: Record<string, string> = {
    active: '#10b981',
    inactive: '#6b7280',
    suspended: '#ef4444',
  };
  return statusColors[status] || '#6b7280';
};

export const getUserRoleColor = (role: string): string => {
  const roleColors: Record<string, string> = {
    user: '#3b82f6',
    moderator: '#8b5cf6',
    finance_manager: '#f59e0b',
    admin: '#ef4444',
  };
  return roleColors[role] || '#6b7280';
};

export const formatWalletBalance = (balance: number, currency: string = 'TRY'): string => {
  const symbols = { TRY: '₺', USD: '$', EUR: '€' };
  const symbol = symbols[currency as keyof typeof symbols] || currency;
  
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: currency,
    currencyDisplay: 'symbol',
  }).format(balance).replace(currency, symbol);
};

export const getUserDefaultAddress = (user: User) => {
  return user.addresses?.find(address => address.isDefault) || user.addresses?.[0];
};

export const formatUserAddress = (address: any): string => {
  if (!address) return '';
  return `${address.street}, ${address.city}${address.state ? `, ${address.state}` : ''} ${address.postalCode}, ${address.country}`;
};

export const isUserAdmin = (user: User): boolean => {
  return ['admin', 'finance_manager', 'moderator'].includes(user.role);
};

export const canUserAccessFinance = (user: User): boolean => {
  return ['admin', 'finance_manager'].includes(user.role);
};

export const canUserModerate = (user: User): boolean => {
  return ['admin', 'moderator'].includes(user.role);
};

export const getUserPermissions = (role: string): string[] => {
  const permissionMap: Record<string, string[]> = {
    user: ['read'],
    moderator: ['read', 'moderate'],
    finance_manager: ['read', 'finance'],
    admin: ['read', 'write', 'delete', 'admin', 'moderate', 'finance'],
  };
  return permissionMap[role] || ['read'];
};

export const calculateUserActivityScore = (user: User): number => {
  // Simple activity score calculation
  let score = 0;
  
  // Base score for active status
  if (user.status === 'active') score += 20;
  
  // Score for admin approval
  if (user.isAdminApproved) score += 30;
  
  // Score for wallet balance (normalized)
  score += Math.min(user.walletBalance / 100, 25);
  
  // Score for recent login (if available)
  if (user.lastLogin) {
    const daysSinceLogin = (Date.now() - new Date(user.lastLogin).getTime()) / (1000 * 60 * 60 * 24);
    if (daysSinceLogin < 7) score += 25;
    else if (daysSinceLogin < 30) score += 15;
    else if (daysSinceLogin < 90) score += 5;
  }
  
  return Math.min(score, 100);
};

// Filter and sort helpers
export const filterUsersBySearch = (users: User[], searchTerm: string): User[] => {
  if (!searchTerm) return users;
  
  const term = searchTerm.toLowerCase();
  return users.filter(user => 
    getLocalizedUserName(user.name).toLowerCase().includes(term) ||
    user.email.toLowerCase().includes(term) ||
    user.clerkId.toLowerCase().includes(term)
  );
};

export const sortUsers = (
  users: User[], 
  sortBy: 'name' | 'email' | 'createdAt' | 'lastLogin' | 'walletBalance' = 'createdAt',
  sortOrder: 'asc' | 'desc' = 'desc'
): User[] => {
  return [...users].sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'name':
        comparison = getLocalizedUserName(a.name).localeCompare(getLocalizedUserName(b.name), 'tr');
        break;
      case 'email':
        comparison = a.email.localeCompare(b.email);
        break;
      case 'walletBalance':
        comparison = a.walletBalance - b.walletBalance;
        break;
      case 'lastLogin':
        const aLogin = a.lastLogin ? new Date(a.lastLogin).getTime() : 0;
        const bLogin = b.lastLogin ? new Date(b.lastLogin).getTime() : 0;
        comparison = aLogin - bLogin;
        break;
      case 'createdAt':
      default:
        comparison = new Date(a._createdAt).getTime() - new Date(b._createdAt).getTime();
        break;
    }
    
    return sortOrder === 'desc' ? -comparison : comparison;
  });
};
