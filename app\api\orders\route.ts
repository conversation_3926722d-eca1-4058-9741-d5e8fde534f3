import { NextRequest, NextResponse } from 'next/server';
import { client } from '@/sanity/lib/client';
import { Order, OrderQueryParams } from '@/types/sanity';

// Enhanced GROQ query for orders
const ORDERS_QUERY = `
  *[_type == "order" 
    $customerFilter
    $statusFilter
    $paymentStatusFilter
    $sourceFilter
    $dateFilter
  ] | order($sortBy $sortOrder) [$start...$end] {
    _id,
    _type,
    _createdAt,
    _updatedAt,
    _rev,
    orderNumber,
    customer,
    orderNotes,
    orderItems[] {
      product->{
        _id,
        name,
        image,
        price,
        currency
      },
      quantity,
      price,
      currency,
      discount
    },
    totalAmount,
    discountAmount,
    currency,
    paymentStatus,
    paymentIntentId,
    orderStatus,
    source,
    campaignCode,
    shippingAddress,
    createdAt,
    updatedAt
  }
`;

const COUNT_QUERY = `
  count(*[_type == "order" 
    $customerFilter
    $statusFilter
    $paymentStatusFilter
    $sourceFilter
    $dateFilter
  ])
`;

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse query parameters
    const params: OrderQueryParams = {
      customer: searchParams.get('customer') || undefined,
      status: searchParams.get('status') as any || undefined,
      paymentStatus: searchParams.get('paymentStatus') as any || undefined,
      source: searchParams.get('source') as any || undefined,
      dateFrom: searchParams.get('dateFrom') || undefined,
      dateTo: searchParams.get('dateTo') || undefined,
      page: Number(searchParams.get('page')) || 1,
      limit: Number(searchParams.get('limit')) || 20,
      sortBy: (searchParams.get('sortBy') as any) || 'createdAt',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
    };

    // Build dynamic filters
    let customerFilter = '';
    let statusFilter = '';
    let paymentStatusFilter = '';
    let sourceFilter = '';
    let dateFilter = '';

    if (params.customer) {
      customerFilter = `&& customer.clerkUserId == "${params.customer}"`;
    }

    if (params.status) {
      statusFilter = `&& orderStatus == "${params.status}"`;
    }

    if (params.paymentStatus) {
      paymentStatusFilter = `&& paymentStatus == "${params.paymentStatus}"`;
    }

    if (params.source) {
      sourceFilter = `&& source == "${params.source}"`;
    }

    if (params.dateFrom || params.dateTo) {
      const dateConditions = [];
      if (params.dateFrom) dateConditions.push(`createdAt >= "${params.dateFrom}"`);
      if (params.dateTo) dateConditions.push(`createdAt <= "${params.dateTo}"`);
      dateFilter = `&& (${dateConditions.join(' && ')})`;
    }

    // Calculate pagination
    const start = (params.page - 1) * params.limit;
    const end = start + params.limit;

    // Build final queries
    const finalOrdersQuery = ORDERS_QUERY
      .replace('$customerFilter', customerFilter)
      .replace('$statusFilter', statusFilter)
      .replace('$paymentStatusFilter', paymentStatusFilter)
      .replace('$sourceFilter', sourceFilter)
      .replace('$dateFilter', dateFilter)
      .replace('$sortBy', params.sortBy || 'createdAt')
      .replace('$sortOrder', params.sortOrder || 'desc')
      .replace('$start', start.toString())
      .replace('$end', end.toString());

    const finalCountQuery = COUNT_QUERY
      .replace('$customerFilter', customerFilter)
      .replace('$statusFilter', statusFilter)
      .replace('$paymentStatusFilter', paymentStatusFilter)
      .replace('$sourceFilter', sourceFilter)
      .replace('$dateFilter', dateFilter);

    // Execute queries
    const [orders, total] = await Promise.all([
      client.fetch<Order[]>(finalOrdersQuery),
      client.fetch<number>(finalCountQuery)
    ]);

    // Return response
    return NextResponse.json({
      data: orders,
      pagination: {
        page: params.page,
        limit: params.limit,
        total,
        totalPages: Math.ceil(total / params.limit),
        hasNext: end < total,
        hasPrev: params.page > 1,
      },
      success: true,
    });

  } catch (error) {
    console.error('Error fetching orders:', error);
    return NextResponse.json(
      { 
        error: { 
          message: 'Failed to fetch orders',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        success: false 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const orderData = await request.json();
    
    // Calculate total amount automatically
    const totalAmount = orderData.orderItems.reduce((total: number, item: any) => {
      const itemTotal = item.price * item.quantity;
      const discountAmount = item.discount ? (itemTotal * item.discount) / 100 : 0;
      return total + (itemTotal - discountAmount);
    }, 0);

    const campaignDiscount = orderData.discountAmount || 0;
    const finalTotal = totalAmount - campaignDiscount;

    // Create order with calculated total
    const order = await client.create({
      _type: 'order',
      ...orderData,
      totalAmount: finalTotal,
      createdAt: new Date().toISOString(),
    });

    return NextResponse.json({
      data: order,
      success: true,
    });

  } catch (error) {
    console.error('Error creating order:', error);
    return NextResponse.json(
      { 
        error: { 
          message: 'Failed to create order',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        success: false 
      },
      { status: 500 }
    );
  }
}
