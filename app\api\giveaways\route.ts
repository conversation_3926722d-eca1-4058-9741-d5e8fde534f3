import { NextRequest, NextResponse } from 'next/server';
import { client } from '@/sanity/lib/client';
import { Giveaway, GiveawayQueryParams } from '@/types/sanity';

// Enhanced GROQ query for giveaways
const GIVEAWAYS_QUERY = `
  *[_type == "giveaway" 
    && defined(title.tr)
    $statusFilter
    $priceFilter
    $dateFilter
    $searchFilter
  ] | order($sortBy $sortOrder) [$start...$end] {
    _id,
    _type,
    _createdAt,
    _updatedAt,
    _rev,
    id,
    title,
    description,
    rules,
    image {
      asset->{
        _id,
        url
      },
      alt,
      hotspot,
      crop
    },
    sale->{
      _id,
      title,
      couponCode
    },
    ticketPrice,
    currency,
    totalTickets,
    ticketsSold,
    ticketSalePercentageForDraw,
    numbersPerCard,
    ticketDigitLength,
    maxTicketsPerUser,
    participants[] {
      user->{
        _id,
        name,
        email
      },
      tickets[] {
        ticketNumber,
        purchasedAt,
        chosenDigitCount,
        status
      }
    },
    prizes[] {
      rank,
      product->{
        _id,
        name,
        image
      },
      title,
      description,
      value,
      currency,
      image {
        asset->{
          _id,
          url
        },
        alt
      },
      prizeType
    },
    status,
    startDate,
    endDate,
    drawDate,
    winningNumbers,
    winners[] {
      user->{
        _id,
        name,
        email
      },
      ticketNumber,
      prize,
      rank
    }
  }
`;

const COUNT_QUERY = `
  count(*[_type == "giveaway" 
    && defined(title.tr)
    $statusFilter
    $priceFilter
    $dateFilter
    $searchFilter
  ])
`;

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Check for special endpoints
    const type = searchParams.get('type');
    
    if (type === 'active') {
      const activeGiveaways = await client.fetch(`
        *[_type == "giveaway" && status == "active" && startDate <= now() && endDate >= now()] | order(endDate asc) {
          _id, id, title, image, ticketPrice, currency, totalTickets, ticketsSold, endDate, status
        }
      `);
      return NextResponse.json({ data: activeGiveaways, success: true });
    }
    
    if (type === 'upcoming') {
      const upcomingGiveaways = await client.fetch(`
        *[_type == "giveaway" && status == "upcoming" && startDate > now()] | order(startDate asc) {
          _id, id, title, image, ticketPrice, currency, totalTickets, startDate, status
        }
      `);
      return NextResponse.json({ data: upcomingGiveaways, success: true });
    }
    
    if (type === 'completed') {
      const completedGiveaways = await client.fetch(`
        *[_type == "giveaway" && status == "completed"] | order(endDate desc) {
          _id, id, title, image, ticketPrice, currency, totalTickets, ticketsSold, winners, endDate, status
        }
      `);
      return NextResponse.json({ data: completedGiveaways, success: true });
    }

    // Parse query parameters for regular giveaway fetching
    const params: GiveawayQueryParams = {
      status: searchParams.get('status') as any || undefined,
      minPrice: searchParams.get('minPrice') ? Number(searchParams.get('minPrice')) : undefined,
      maxPrice: searchParams.get('maxPrice') ? Number(searchParams.get('maxPrice')) : undefined,
      dateFrom: searchParams.get('dateFrom') || undefined,
      dateTo: searchParams.get('dateTo') || undefined,
      search: searchParams.get('search') || undefined,
      page: Number(searchParams.get('page')) || 1,
      limit: Number(searchParams.get('limit')) || 20,
      sortBy: (searchParams.get('sortBy') as any) || 'endDate',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'asc',
      language: (searchParams.get('language') as 'tr' | 'en') || 'tr',
    };

    // Build dynamic filters
    let statusFilter = '';
    let priceFilter = '';
    let dateFilter = '';
    let searchFilter = '';

    if (params.status) {
      statusFilter = `&& status == "${params.status}"`;
    }

    if (params.minPrice !== undefined || params.maxPrice !== undefined) {
      const priceConditions = [];
      if (params.minPrice !== undefined) priceConditions.push(`ticketPrice >= ${params.minPrice}`);
      if (params.maxPrice !== undefined) priceConditions.push(`ticketPrice <= ${params.maxPrice}`);
      priceFilter = `&& (${priceConditions.join(' && ')})`;
    }

    if (params.dateFrom || params.dateTo) {
      const dateConditions = [];
      if (params.dateFrom) dateConditions.push(`startDate >= "${params.dateFrom}"`);
      if (params.dateTo) dateConditions.push(`endDate <= "${params.dateTo}"`);
      dateFilter = `&& (${dateConditions.join(' && ')})`;
    }

    if (params.search) {
      const searchTerm = params.search.toLowerCase();
      searchFilter = `&& (
        title.tr match "*${searchTerm}*" ||
        title.en match "*${searchTerm}*" ||
        description.tr match "*${searchTerm}*" ||
        description.en match "*${searchTerm}*"
      )`;
    }

    // Calculate pagination
    const start = (params.page - 1) * params.limit;
    const end = start + params.limit;

    // Build final queries
    const finalGiveawaysQuery = GIVEAWAYS_QUERY
      .replace('$statusFilter', statusFilter)
      .replace('$priceFilter', priceFilter)
      .replace('$dateFilter', dateFilter)
      .replace('$searchFilter', searchFilter)
      .replace('$sortBy', params.sortBy || 'endDate')
      .replace('$sortOrder', params.sortOrder || 'asc')
      .replace('$start', start.toString())
      .replace('$end', end.toString());

    const finalCountQuery = COUNT_QUERY
      .replace('$statusFilter', statusFilter)
      .replace('$priceFilter', priceFilter)
      .replace('$dateFilter', dateFilter)
      .replace('$searchFilter', searchFilter);

    // Execute queries
    const [giveaways, total] = await Promise.all([
      client.fetch<Giveaway[]>(finalGiveawaysQuery),
      client.fetch<number>(finalCountQuery)
    ]);

    // Return response
    return NextResponse.json({
      data: giveaways,
      pagination: {
        page: params.page,
        limit: params.limit,
        total,
        totalPages: Math.ceil(total / params.limit),
        hasNext: end < total,
        hasPrev: params.page > 1,
      },
      success: true,
    });

  } catch (error) {
    console.error('Error fetching giveaways:', error);
    return NextResponse.json(
      { 
        error: { 
          message: 'Failed to fetch giveaways',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        success: false 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const giveawayData = await request.json();
    
    // Create giveaway
    const giveaway = await client.create({
      _type: 'giveaway',
      ...giveawayData,
      ticketsSold: 0, // Initialize tickets sold
      participants: [], // Initialize empty participants
      status: 'upcoming', // Start as upcoming
    });

    return NextResponse.json({
      data: giveaway,
      success: true,
    });

  } catch (error) {
    console.error('Error creating giveaway:', error);
    return NextResponse.json(
      { 
        error: { 
          message: 'Failed to create giveaway',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        success: false 
      },
      { status: 500 }
    );
  }
}
