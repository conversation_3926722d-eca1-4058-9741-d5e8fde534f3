import { client } from '@/sanity/lib/client';
import { getGiveawayById } from '@/sanity/lib/giveaways/getGiveaways';
import { currentUser } from '@clerk/nextjs/server';
import type { NextRequest } from 'next/server';
import { getUserByClerkId } from '@/sanity/lib/users/getUser';

// Helper function to generate a random ticket number based on length and distinct digit count
const generateRandomTicket = (length: number, digitCount: number): string => {
  // length: kaç haneli olacak (örn: 3, 4, 5)
  // digitCount: kart başına kaç farklı rakam olacak (örn: 3)
  const min = Math.pow(10, length - 1);
  const max = Math.pow(10, length) - 1;
  let ticket = '';
  // Her hane için random rakam seç
  for (let i = 0; i < length; i++) {
    ticket += Math.floor(Math.random() * 10).toString();
  }
  // Başta sıfır olmaması için padStart ile düzelt
  ticket = (parseInt(ticket, 10) || min).toString().padStart(length, '0');
  // Unic olması için min-max arası random üret
  let uniqueNum = Math.floor(Math.random() * (max - min + 1)) + min;
  return uniqueNum.toString();
};

export async function POST(req: NextRequest) {
  const { giveawayId, numberOfTickets } = await req.json();
  const clerkUser = await currentUser();

  if (!clerkUser || !clerkUser.id || !clerkUser.emailAddresses[0]?.emailAddress) {
    return new Response(JSON.stringify({ error: 'Yetkisiz: Kullanıcı bilgileri eksik.' }), { status: 401 });
  }

  const clerkUserId = clerkUser.id;
  const clerkUserEmail = clerkUser.emailAddresses[0].emailAddress;
  const clerkUserName = clerkUser.fullName || clerkUser.firstName || clerkUser.emailAddresses[0].emailAddress;

  const user = await getUserByClerkId({ id: clerkUserId, email: clerkUserEmail, name: clerkUserName });
  if (!user || !user.isAdminApproved) {
    return new Response(JSON.stringify({ error: 'Bilet satın almak için yönetici onayı gerekiyor.' }), { status: 403 });
  }

  const giveaway = await getGiveawayById(giveawayId);
  if (!giveaway) {
    return new Response(JSON.stringify({ error: 'Çekiliş bulunamadı.' }), { status: 404 });
  }
  if (giveaway.status !== 'active') {
    return new Response(JSON.stringify({ error: 'Çekiliş aktif değil.' }), { status: 400 });
  }

  // Kart başına numara ve bilet uzunluğu admin tarafından tanımlanır
  const numbersPerCard = giveaway.numbersPerCard || 3;
  const ticketDigitLength = giveaway.ticketDigitLength || 3;
  const ticketPrice = giveaway.ticketPrice || 0;

  // Toplam kombinasyon
  const totalCombinations = Math.pow(10, ticketDigitLength);
  const maxCards = Math.floor(totalCombinations / numbersPerCard);
  const ticketsSold = giveaway.ticketsSold || 0;
  const availableCards = maxCards - ticketsSold;

  if (availableCards <= 0) {
    return new Response(JSON.stringify({ error: 'Bilet bitmiştir.' }), { status: 400 });
  }

  if (numberOfTickets > availableCards) {
    return new Response(JSON.stringify({ error: `Yeterli kart yok. Maksimum ${availableCards} kart alabilirsiniz.` }), { status: 400 });
  }

  // 10'dan fazla kart için admin onayı gereksinimi
  if (numberOfTickets > 10) {
    // Burada bir admin onay talebi oluşturulabilir veya hata dönebilir
    return new Response(JSON.stringify({ error: '10 adetten fazla kart almak için yönetici onayı gereklidir.' }), { status: 403 });
  }

  // Cüzdan bakiyesi kontrolü
  if ((user.walletBalance || 0) < (ticketPrice * numberOfTickets)) {
    return new Response(JSON.stringify({ error: 'Yetersiz cüzdan bakiyesi.' }), { status: 400 });
  }

  // Kullanıcıya atanacak kartlar için satılmamış numaraları bulmamız gerekiyor
  // Tüm satılmış numaraları toplayalım
  const soldNumbers = new Set<string>();
  if (giveaway.participants) {
    for (const participant of giveaway.participants) {
      if (participant.tickets) {
        for (const ticket of participant.tickets) {
          soldNumbers.add(ticket.ticketNumber);
        }
      }
    }
  }

  // Satılmamış numaralardan kartlar üret
  const allNumbers: string[] = [];
  for (let i = 0; i < totalCombinations; i++) {
    const num = i.toString().padStart(ticketDigitLength, '0');
    if (!soldNumbers.has(num)) {
      allNumbers.push(num);
    }
  }

  // Tüm satılmamış numaraları karıştır
  for (let i = allNumbers.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [allNumbers[i], allNumbers[j]] = [allNumbers[j], allNumbers[i]];
  }

  // Kartları oluştur
  const newCards: string[][] = [];
  let idx = 0;
  for (let c = 0; c < numberOfTickets; c++) {
    if (idx + numbersPerCard > allNumbers.length) break;
    // Her kart için ticketDigitLength kadar random numara üret
    const cardNumbers: string[] = [];
    for (let n = 0; n < numbersPerCard; n++) {
      let uniqueNum;
      do {
        uniqueNum = generateRandomTicket(ticketDigitLength, numbersPerCard);
      } while (soldNumbers.has(uniqueNum) || cardNumbers.includes(uniqueNum));
      cardNumbers.push(uniqueNum);
      soldNumbers.add(uniqueNum);
    }
    newCards.push(cardNumbers);
    idx += numbersPerCard;
  }

  if (newCards.length !== numberOfTickets) {
    return new Response(JSON.stringify({ error: 'Yeterli kart üretilemedi.' }), { status: 400 });
  }

  // Sanity transaction
  try {
    const transaction = client.transaction();
    // Katılımcı güncellemesi
    const participantPatchPath = `participants[user._ref == "${user._id}"]`;
    const userAlreadyParticipant = (giveaway.participants || []).some((p: any) => p.user?._ref === user._id);
    const newTicketObjs = newCards.flatMap(card => card.map(ticketNumber => ({
      _key: `${Date.now()}-${user._id}-${ticketNumber}`,
      ticketNumber,
      purchasedAt: new Date().toISOString(),
      chosenDigitCount: ticketDigitLength,
    })));

    if (userAlreadyParticipant) {
      // Sadece tickets dizisine ekle
      transaction.patch(giveaway._id, patch =>
        patch
          .setIfMissing({ participants: [] })
          .append(`${participantPatchPath}.tickets`, newTicketObjs)
          .inc({ ticketsSold: numberOfTickets })
      );
    } else {
      // Yeni participant olarak ekle
      transaction.patch(giveaway._id, patch =>
        patch
          .setIfMissing({ participants: [] })
          .append('participants', [
            {
              _key: `${Date.now()}-${user._id}`,
              user: { _ref: user._id, _type: 'reference' },
              tickets: newTicketObjs,
            },
          ])
          .inc({ ticketsSold: numberOfTickets })
      );
    }
    // Kullanıcı cüzdan bakiyesi düş
    transaction.patch(user._id, {
      set: { walletBalance: (user.walletBalance || 0) - (ticketPrice * numberOfTickets) }
    });
    await transaction.commit();
    return new Response(JSON.stringify({ success: true, purchasedTickets: newCards }), { status: 200 });
  } catch (error) {
    console.error('Bilet satın alırken hata oluştu:', error);
    return new Response(JSON.stringify({ error: 'Bilet satın alınamadı.' }), { status: 500 });
  }
} 