
import { searchProductsByName } from "@/sanity/lib/products/searchProductsByName";
import ProductGrid from "@/components/productGrid";

export default async function SearchPage(props: { searchParams: Promise<{ query?: string }> }) {
  const searchParams = await props.searchParams;
  const query = searchParams?.query || "";
  const products = await searchProductsByName(query);

  if (!products?.length) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-4xl">
          <h1 className="text-3xl font-bold mb-6 text-center">
            Search for Products: {query}
          </h1>
          <p className="text-gray-600 text-center">
            {query ? "No products found matching your search." : "Please enter a search term to find products."}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">
          Search results for: {query}
        </h1>
        <p className="text-gray-600">
          Found {products.length} product{products.length !== 1 ? 's' : ''}
        </p>
      </div>
      <ProductGrid products={products} />
    </div>
  );
}


