// Complete Auction & Giveaway Integration Example
import React, { useState, useEffect } from 'react';
import { 
  useAuctions, 
  useActiveAuctions,
  useAuction,
  canPlaceBid,
  formatBidAmount,
  getAuctionTimeRemaining,
  isAuctionActive 
} from '../hooks/useAuctions';
import { 
  useGiveaways, 
  useActiveGiveaways,
  useGiveaway,
  canPurchaseTickets,
  formatTicketPrice,
  getGiveawayTimeRemaining,
  getSalePercentage,
  getUserTicketCount 
} from '../hooks/useGiveaways';
import { 
  useNotifications,
  useUnreadNotifications,
  getNotificationTypeIcon,
  formatNotificationTime 
} from '../hooks/useNotifications';

// Auction & Giveaway Dashboard
export const AuctionGiveawayDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'auctions' | 'giveaways' | 'notifications'>('auctions');
  const [userId] = useState('user_123'); // This would come from auth context

  return (
    <div className="auction-giveaway-dashboard">
      <div className="dashboard-header">
        <h1>Açık Artırma & Çekiliş Merkezi</h1>
        <div className="tab-navigation">
          {['auctions', 'giveaways', 'notifications'].map(tab => (
            <button
              key={tab}
              className={`tab ${activeTab === tab ? 'active' : ''}`}
              onClick={() => setActiveTab(tab as any)}
            >
              {tab === 'auctions' && '🔨 Açık Artırmalar'}
              {tab === 'giveaways' && '🎁 Çekilişler'}
              {tab === 'notifications' && '🔔 Bildirimler'}
            </button>
          ))}
        </div>
      </div>

      <div className="dashboard-content">
        {activeTab === 'auctions' && <AuctionSection userId={userId} />}
        {activeTab === 'giveaways' && <GiveawaySection userId={userId} />}
        {activeTab === 'notifications' && <NotificationSection userId={userId} />}
      </div>
    </div>
  );
};

// Auction Section Component
const AuctionSection: React.FC<{ userId: string }> = ({ userId }) => {
  const { data: activeAuctions, isLoading: activeLoading } = useActiveAuctions();
  const { data: allAuctions } = useAuctions({ status: 'active', limit: 10 });

  if (activeLoading) return <div>Açık artırmalar yükleniyor...</div>;

  return (
    <div className="auction-section">
      <div className="section-header">
        <h2>🔨 Aktif Açık Artırmalar</h2>
        <p>{activeAuctions?.length || 0} aktif açık artırma</p>
      </div>

      <div className="auction-grid">
        {activeAuctions?.map(auction => (
          <AuctionCard key={auction._id} auction={auction} userId={userId} />
        ))}
      </div>

      {(!activeAuctions || activeAuctions.length === 0) && (
        <div className="empty-state">
          <p>Şu anda aktif açık artırma bulunmuyor.</p>
        </div>
      )}
    </div>
  );
};

// Auction Card Component
const AuctionCard: React.FC<{ auction: any; userId: string }> = ({ auction, userId }) => {
  const [bidAmount, setBidAmount] = useState(auction.currentBid + auction.bidIncrementAmount);
  const [timeRemaining, setTimeRemaining] = useState(getAuctionTimeRemaining(auction));

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeRemaining(getAuctionTimeRemaining(auction));
    }, 1000);

    return () => clearInterval(timer);
  }, [auction]);

  const handlePlaceBid = async () => {
    const validation = canPlaceBid(auction, bidAmount, userId);
    if (!validation.canBid) {
      alert(validation.reason);
      return;
    }

    try {
      // API call to place bid would go here
      console.log('Placing bid:', bidAmount);
      alert('Teklif verildi!');
    } catch (error) {
      alert('Teklif verilemedi!');
    }
  };

  const isActive = isAuctionActive(auction);
  const isUserWinning = auction.winner?._ref === userId;

  return (
    <div className={`auction-card ${isUserWinning ? 'winning' : ''}`}>
      <div className="auction-image">
        {auction.image && (
          <img src={auction.image.asset.url} alt={auction.image.alt} />
        )}
      </div>
      
      <div className="auction-content">
        <h3>{auction.product?.name?.tr || 'Ürün'}</h3>
        
        <div className="auction-stats">
          <div className="current-bid">
            <span className="label">Mevcut Teklif:</span>
            <span className="amount">{formatBidAmount(auction.currentBid, auction.currency)}</span>
          </div>
          
          <div className="time-remaining">
            <span className="label">Kalan Süre:</span>
            <span className="time">
              {timeRemaining.days}g {timeRemaining.hours}s {timeRemaining.minutes}d {timeRemaining.seconds}sn
            </span>
          </div>
        </div>

        {isUserWinning && (
          <div className="winning-badge">
            🏆 En Yüksek Teklif Sizin!
          </div>
        )}

        {isActive && (
          <div className="bid-section">
            <div className="bid-input">
              <input
                type="number"
                value={bidAmount}
                onChange={(e) => setBidAmount(Number(e.target.value))}
                min={auction.currentBid + auction.bidIncrementAmount}
                step={auction.bidIncrementAmount}
              />
              <span className="currency">{auction.currency}</span>
            </div>
            <button 
              className="bid-button"
              onClick={handlePlaceBid}
              disabled={!canPlaceBid(auction, bidAmount, userId).canBid}
            >
              Teklif Ver
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

// Giveaway Section Component
const GiveawaySection: React.FC<{ userId: string }> = ({ userId }) => {
  const { data: activeGiveaways, isLoading: activeLoading } = useActiveGiveaways();

  if (activeLoading) return <div>Çekilişler yükleniyor...</div>;

  return (
    <div className="giveaway-section">
      <div className="section-header">
        <h2>🎁 Aktif Çekilişler</h2>
        <p>{activeGiveaways?.length || 0} aktif çekiliş</p>
      </div>

      <div className="giveaway-grid">
        {activeGiveaways?.map(giveaway => (
          <GiveawayCard key={giveaway._id} giveaway={giveaway} userId={userId} />
        ))}
      </div>

      {(!activeGiveaways || activeGiveaways.length === 0) && (
        <div className="empty-state">
          <p>Şu anda aktif çekiliş bulunmuyor.</p>
        </div>
      )}
    </div>
  );
};

// Giveaway Card Component
const GiveawayCard: React.FC<{ giveaway: any; userId: string }> = ({ giveaway, userId }) => {
  const [ticketCount, setTicketCount] = useState(1);
  const [timeRemaining, setTimeRemaining] = useState(getGiveawayTimeRemaining(giveaway));

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeRemaining(getGiveawayTimeRemaining(giveaway));
    }, 1000);

    return () => clearInterval(timer);
  }, [giveaway]);

  const handlePurchaseTickets = async () => {
    const validation = canPurchaseTickets(giveaway, ticketCount, userId);
    if (!validation.canPurchase) {
      alert(validation.reason);
      return;
    }

    try {
      // API call to purchase tickets would go here
      console.log('Purchasing tickets:', ticketCount);
      alert(`${ticketCount} bilet satın alındı!`);
    } catch (error) {
      alert('Bilet satın alınamadı!');
    }
  };

  const salePercentage = getSalePercentage(giveaway);
  const userTickets = getUserTicketCount(giveaway, userId);
  const totalCost = ticketCount * giveaway.ticketPrice;

  return (
    <div className="giveaway-card">
      <div className="giveaway-image">
        {giveaway.image && (
          <img src={giveaway.image.asset.url} alt={giveaway.image.alt} />
        )}
      </div>
      
      <div className="giveaway-content">
        <h3>{giveaway.title.tr}</h3>
        
        <div className="giveaway-stats">
          <div className="ticket-info">
            <span className="label">Bilet Fiyatı:</span>
            <span className="price">{formatTicketPrice(giveaway.ticketPrice, giveaway.currency)}</span>
          </div>
          
          <div className="progress-info">
            <span className="label">Satış Oranı:</span>
            <div className="progress-bar">
              <div 
                className="progress-fill" 
                style={{ width: `${salePercentage}%` }}
              ></div>
            </div>
            <span className="percentage">{salePercentage.toFixed(1)}%</span>
          </div>
          
          <div className="time-remaining">
            <span className="label">Kalan Süre:</span>
            <span className="time">
              {timeRemaining.days}g {timeRemaining.hours}s {timeRemaining.minutes}d
            </span>
          </div>
        </div>

        {userTickets > 0 && (
          <div className="user-tickets">
            🎫 Sahip Olduğunuz Bilet: {userTickets}
          </div>
        )}

        <div className="ticket-purchase">
          <div className="ticket-input">
            <label>Bilet Sayısı:</label>
            <input
              type="number"
              value={ticketCount}
              onChange={(e) => setTicketCount(Number(e.target.value))}
              min={1}
              max={giveaway.maxTicketsPerUser - userTickets}
            />
          </div>
          
          <div className="total-cost">
            Toplam: {formatTicketPrice(totalCost, giveaway.currency)}
          </div>
          
          <button 
            className="purchase-button"
            onClick={handlePurchaseTickets}
            disabled={!canPurchaseTickets(giveaway, ticketCount, userId).canPurchase}
          >
            Bilet Satın Al
          </button>
        </div>

        <div className="prizes-preview">
          <h4>Ödüller:</h4>
          {giveaway.prizes?.slice(0, 3).map((prize: any, index: number) => (
            <div key={index} className="prize-item">
              🏆 {index + 1}. {prize.title.tr}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Notification Section Component
const NotificationSection: React.FC<{ userId: string }> = ({ userId }) => {
  const { data: notifications, isLoading } = useNotifications({ userId, limit: 20 });
  const { data: unreadNotifications } = useUnreadNotifications(userId);

  if (isLoading) return <div>Bildirimler yükleniyor...</div>;

  return (
    <div className="notification-section">
      <div className="section-header">
        <h2>🔔 Bildirimler</h2>
        <p>{unreadNotifications?.length || 0} okunmamış bildirim</p>
      </div>

      <div className="notification-list">
        {notifications?.map(notification => (
          <NotificationItem key={notification._id} notification={notification} />
        ))}
      </div>

      {(!notifications || notifications.length === 0) && (
        <div className="empty-state">
          <p>Henüz bildirim bulunmuyor.</p>
        </div>
      )}
    </div>
  );
};

// Notification Item Component
const NotificationItem: React.FC<{ notification: any }> = ({ notification }) => {
  const icon = getNotificationTypeIcon(notification.type);
  const timeAgo = formatNotificationTime(notification.sentAt);

  return (
    <div className={`notification-item ${notification.isRead ? 'read' : 'unread'}`}>
      <div className="notification-icon">
        {icon}
      </div>
      
      <div className="notification-content">
        <h4>{notification.title.tr}</h4>
        <p>{notification.message.tr}</p>
        <span className="notification-time">{timeAgo}</span>
      </div>
      
      {!notification.isRead && (
        <div className="unread-indicator">●</div>
      )}
    </div>
  );
};

export default AuctionGiveawayDashboard;
