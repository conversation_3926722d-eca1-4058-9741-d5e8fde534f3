import { NextRequest, NextResponse } from 'next/server';
import { client } from '@/sanity/lib/client';

export async function GET(request: NextRequest) {
  try {
    console.log('Testing Sanity connection...');
    console.log('Project ID:', process.env.NEXT_PUBLIC_SANITY_PROJECT_ID);
    console.log('Dataset:', process.env.NEXT_PUBLIC_SANITY_DATASET);
    console.log('Token exists:', !!process.env.SANITY_API_TOKEN);

    // Test basic connection
    const result = await client.fetch('*[_type == "product"][0...3]{_id, name}');
    
    console.log('Sanity connection successful:', result);

    return NextResponse.json({
      success: true,
      message: 'Sanity connection successful',
      data: result,
      config: {
        projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
        dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
        hasToken: !!process.env.SANITY_API_TOKEN,
      }
    });

  } catch (error) {
    console.error('Sanity connection failed:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        details: error,
      },
      { status: 500 }
    );
  }
}
