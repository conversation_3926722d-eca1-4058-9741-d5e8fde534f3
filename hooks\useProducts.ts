import { useQuery, UseQueryResult } from '@tanstack/react-query';
import { api } from '../lib/api';
import { 
  Product, 
  ProductWithCategory, 
  ProductsResponse, 
  ProductQueryParams,
  ApiResponse 
} from '../types/sanity';

// API functions
const fetchProducts = async (params?: ProductQueryParams): Promise<Product[]> => {
  const searchParams = new URLSearchParams();
  
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach(v => searchParams.append(key, v.toString()));
        } else {
          searchParams.append(key, value.toString());
        }
      }
    });
  }

  const response = await api.get(`/products?${searchParams.toString()}`);
  return response.data;
};

const fetchProductById = async (id: string): Promise<Product | null> => {
  const response = await api.get(`/products/${id}`);
  return response.data;
};

const fetchProductBySlug = async (slug: string): Promise<Product | null> => {
  const response = await api.get(`/products/slug/${slug}`);
  return response.data;
};

const fetchProductsByCategory = async (categorySlug: string): Promise<Product[]> => {
  const response = await api.get(`/products/category/${categorySlug}`);
  return response.data;
};

const fetchFeaturedProducts = async (limit: number = 10): Promise<Product[]> => {
  const response = await api.get(`/products/featured?limit=${limit}`);
  return response.data;
};

// React Query hooks
export const useProducts = (params?: ProductQueryParams) => {
  return useQuery<Product[], Error>({
    queryKey: ['products', params],
    queryFn: () => fetchProducts(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    refetchOnWindowFocus: false,
  });
};

export const useProduct = (id: string) => {
  return useQuery<Product | null, Error>({
    queryKey: ['product', id],
    queryFn: () => fetchProductById(id),
    staleTime: 5 * 60 * 1000,
    retry: 2,
    enabled: !!id,
  });
};

export const useProductBySlug = (slug: string) => {
  return useQuery<Product | null, Error>({
    queryKey: ['product', 'slug', slug],
    queryFn: () => fetchProductBySlug(slug),
    staleTime: 5 * 60 * 1000,
    retry: 2,
    enabled: !!slug,
  });
};

export const useProductsByCategory = (categorySlug: string) => {
  return useQuery<Product[], Error>({
    queryKey: ['products', 'category', categorySlug],
    queryFn: () => fetchProductsByCategory(categorySlug),
    staleTime: 5 * 60 * 1000,
    retry: 2,
    enabled: !!categorySlug,
  });
};

export const useFeaturedProducts = (limit: number = 10) => {
  return useQuery<Product[], Error>({
    queryKey: ['products', 'featured', limit],
    queryFn: () => fetchFeaturedProducts(limit),
    staleTime: 10 * 60 * 1000, // 10 minutes for featured products
    retry: 2,
  });
};

// Search hook with debouncing
export const useProductSearch = (searchTerm: string, params?: Omit<ProductQueryParams, 'search'>) => {
  return useQuery<Product[], Error>({
    queryKey: ['products', 'search', searchTerm, params],
    queryFn: () => fetchProducts({ ...params, search: searchTerm }),
    staleTime: 2 * 60 * 1000, // 2 minutes for search results
    retry: 1,
    enabled: searchTerm.length >= 2, // Only search if term is at least 2 characters
  });
};

// Infinite query for pagination
export const useInfiniteProducts = (params?: ProductQueryParams) => {
  return useQuery<Product[], Error>({
    queryKey: ['products', 'infinite', params],
    queryFn: () => fetchProducts(params),
    staleTime: 5 * 60 * 1000,
    retry: 2,
  });
};

// Utility hooks
export const useProductFlags = () => {
  const flags = [
    { value: 'featured', label: 'Öne Çıkan' },
    { value: 'new', label: 'Yeni' },
    { value: 'sale', label: 'İndirimli' },
    { value: 'limited', label: 'Sınırlı Sayıda' },
    { value: 'auction', label: 'Müzayede' },
    { value: 'giveaway', label: 'Çekiliş' },
    { value: 'banner', label: 'Banner' },
    { value: 'exchange', label: 'Ürün Takası' },
  ] as const;

  return flags;
};

export const useCurrencies = () => {
  const currencies = [
    { value: 'TRY', label: 'Türk Lirası', symbol: '₺' },
    { value: 'USD', label: 'ABD Doları', symbol: '$' },
    { value: 'EUR', label: 'Euro', symbol: '€' },
  ] as const;

  return currencies;
};

// Helper function to get localized text
export const getLocalizedText = (
  text: { tr: string; en?: string } | undefined,
  language: 'tr' | 'en' = 'tr'
): string => {
  if (!text) return '';
  return language === 'en' && text.en ? text.en : text.tr;
};

// Helper function to format price
export const formatPrice = (price: number, currency: string = 'TRY'): string => {
  const symbols = { TRY: '₺', USD: '$', EUR: '€' };
  const symbol = symbols[currency as keyof typeof symbols] || currency;
  
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: currency,
    currencyDisplay: 'symbol',
  }).format(price).replace(currency, symbol);
};

// Helper function to calculate discounted price
export const calculateDiscountedPrice = (price: number, discount?: number): number => {
  if (!discount || discount <= 0) return price;
  return price * (1 - discount / 100);
};

// Helper function to check if product is in stock
export const isProductInStock = (product: Product): boolean => {
  if (!product.isAvailable) return false;
  
  if (product.variants && product.variants.length > 0) {
    return product.variants.some(variant => variant.stock > 0);
  }
  
  return product.stock > 0;
};

// Helper function to get total stock including variants
export const getTotalStock = (product: Product): number => {
  if (product.variants && product.variants.length > 0) {
    return product.variants.reduce((total, variant) => total + variant.stock, 0);
  }
  
  return product.stock;
};
