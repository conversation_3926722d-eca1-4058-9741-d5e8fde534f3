import { NextResponse } from 'next/server';
import { client } from '@/sanity/lib/client';
import { v4 as uuidv4 } from 'uuid';

export async function POST(req: Request) {
  try {
    const { giveawayId, winnerCount } = await req.json();
    if (!giveawayId || !winnerCount) {
      return NextResponse.json({ error: 'giveawayId and winnerCount are required' }, { status: 400 });
    }
    const giveaway = await client.fetch(`*[_type == "giveaway" && _id == $id][0]{
      _id,
      participants[]{user, tickets[]},
      prizes[]
    }`, { id: giveawayId });
    if (!giveaway) {
      return NextResponse.json({ error: 'Giveaway not found' }, { status: 404 });
    }
    // Tüm kartlardaki tüm rakamları topla
    const allTicketNumbers = [];
    giveaway.participants?.forEach((participant: any) => {
      participant.tickets?.forEach((ticket: any) => {
        // ticketNumber birden fazla rakam içeriyorsa (ör: ['123', '456'])
        if (Array.isArray(ticket.ticketNumber)) {
          ticket.ticketNumber.forEach((num: string) => {
            allTicketNumbers.push({
              user: participant.user,
              ticketNumber: num,
            });
          });
        } else {
          allTicketNumbers.push({
            user: participant.user,
            ticketNumber: ticket.ticketNumber,
          });
        }
      });
    });
    if (allTicketNumbers.length < winnerCount) {
      return NextResponse.json({ error: 'Not enough ticket numbers for the number of winners' }, { status: 400 });
    }
    // Shuffle and pick winning numbers
    const shuffled = allTicketNumbers.sort(() => 0.5 - Math.random());
    const winningNumbers = shuffled.slice(0, winnerCount).map(t => t.ticketNumber);
    // Kazananlar listesini oluştur
    const winners = await Promise.all(
      shuffled.slice(0, winnerCount).map(async (t, i) => {
        // User detaylarını Sanity'den çek
        let userInfo = t.user;
        if (!userInfo.email || !userInfo.name) {
          const userDoc = await client.fetch(`*[_type == "user" && _id == $id][0]{email, name}`, { id: t.user._ref || t.user });
          userInfo = { ...userInfo, ...userDoc };
        }
        return {
          _key: uuidv4(),
          user: { _ref: t.user._ref || t.user, _type: 'reference' },
          userEmail: userInfo.email,
          userName: userInfo.name,
          ticketNumber: t.ticketNumber,
          prize: giveaway.prizes?.[i]?.title || '',
          rank: i + 1,
        };
      })
    );
    // Sanity'de güncelle
    await client.patch(giveawayId)
      .set({ winners, winningNumbers, status: 'completed' })
      .commit();
    return NextResponse.json({ winners, winningNumbers }, { status: 200 });
  } catch (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
} 