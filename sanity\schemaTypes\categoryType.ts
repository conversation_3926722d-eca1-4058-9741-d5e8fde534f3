import { TagIcon } from '@sanity/icons';
import { defineField, defineType } from 'sanity';
import { nanoid } from 'nanoid';

// Ortak alanlar için yardımcı fonksiyonlar (productType ile uyumlu)
const defineSlugField = (source = 'title.tr') =>
  defineField({
    name: 'slug',
    title: 'Slug',
    type: 'slug',
    options: { source, maxLength: 96 },
    validation: (Rule) => Rule.required().error('Slug zorunludur.'),
  });

const defineImageField = (title = 'Görsel') =>
  defineField({
    name: 'image',
    title,
    type: 'image',
    options: { hotspot: true },
    fields: [
      {
        name: 'alt',
        title: 'Alternatif Metin (SEO ve Erişilebilirlik)',
        type: 'string',
        validation: (Rule) => Rule.required().error('Alternatif metin zorunludur.'),
      },
    ],
  });

export const categoryType = defineType({
  name: 'category',
  title: 'Kate<PERSON><PERSON>',
  type: 'document',
  icon: TagIcon,
  groups: [
    { name: 'basic', title: 'Temel Bilgiler' },
    { name: 'seo', title: 'SEO' },
  ],
  fields: [
    // Temel Bilgiler
    defineField({
      name: 'title',
      title: 'Kategori Adı',
      type: 'object',
      group: 'basic',
      fields: [
        {
          name: 'tr',
          title: 'Türkçe',
          type: 'string',
          validation: (Rule) => Rule.required().error('Türkçe kategori adı zorunludur.'),
        },
        {
          name: 'en',
          title: 'İngilizce',
          type: 'string',
        },
      ],
    }),
    defineSlugField(),
    defineImageField('Kategori Görseli'),
    defineField({
      name: 'description',
      title: 'Açıklama',
      type: 'object',
      group: 'basic',
      fields: [
        {
          name: 'tr',
          title: 'Türkçe',
          type: 'text',
        },
        {
          name: 'en',
          title: 'İngilizce',
          type: 'text',
        },
      ],
    }),
    defineField({
      name: 'parentCategory',
      title: 'Üst Kategori',
      type: 'reference',
      to: [{ type: 'category' }],
      group: 'basic',
      description: 'Bu kategori bir alt kategori ise, üst kategoriyi seçin.',
    }),
    defineField({
      name: 'tags',
      title: 'Etiketler',
      type: 'array',
      of: [{ type: 'string' }],
      options: {
        layout: 'tags',
      },
      group: 'basic',
      description: 'Kategoriyi tanımlayan etiketler (örn. yeni, popüler).',
    }),
    defineField({
      name: 'flags',
      title: 'Kategori Özellikleri',
      type: 'array',
      group: 'basic',
      of: [
        {
          type: 'string',
          options: {
            list: [
              { title: 'Öne Çıkan', value: 'featured' },
              { title: 'Sezonluk', value: 'seasonal' },
              { title: 'Promosyon', value: 'promotion' },
              { title: 'Yeni', value: 'new' },
            ],
          },
        },
      ],
      description: 'Kategorinin özel durumlarını belirtir (örn. öne çıkan kategori).',
    }),

    // SEO
    defineField({
      name: 'seoTitle',
      title: 'SEO Başlığı',
      type: 'string',
      group: 'seo',
      validation: (Rule) =>
        Rule.max(60).warning('SEO başlığı 60 karakterden kısa olmalıdır.'),
    }),
    defineField({
      name: 'seoDescription',
      title: 'SEO Açıklaması',
      type: 'text',
      group: 'seo',
      validation: (Rule) =>
        Rule.max(160).warning('SEO açıklaması 160 karakterden kısa olmalıdır.'),
    }),
  ],
  preview: {
    select: {
      title: 'title.tr',
      subtitle: 'description.tr',
      media: 'image',
      parent: 'parentCategory.title.tr',
    },
    prepare({ title, subtitle, media, parent }) {
      return {
        title: title || 'İsimsiz Kategori',
        subtitle: parent ? `${parent} > ${subtitle || 'Açıklama Yok'}` : subtitle || 'Açıklama Yok',
        media,
      };
    },
  },
});