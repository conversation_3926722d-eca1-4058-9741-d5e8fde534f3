import { defineQuery } from "next-sanity";
import { sanityFetch } from "../live";
import { client } from "../client";

export interface Giveaway {
  _id: string;
  title: string;
  description?: string;
  status: 'active' | 'inactive' | 'completed';
  startDate: string;
  endDate: string;
  maxTickets?: number;
  ticketPrice?: number;
  createdAt: string;
  updatedAt: string;
  image?: any;
  participants?: any[];
  ticketsSold?: number;
  numbersPerCard?: number;
  ticketDigitLength?: number;
  prizes?: any[];
  winners?: any[];
}

export async function getActiveGiveaways(): Promise<Giveaway[]> {
  const ACTIVE_GIVEAWAYS_QUERY = defineQuery(`
    *[_type == "giveaway" && status == "active"] | order(createdAt desc) {
      _id,
      title,
      description,
      status,
      startDate,
      endDate,
      maxTickets,
      ticketPrice,
      createdAt,
      updatedAt,
      image {
        asset->{url}
      },
      participants[]{user, tickets},
      ticketsSold,
      numbersPerCard,
      ticketDigitLength,
      prizes,
      winners[]{user->{_id, name, email}, ticketNumber, prize, rank}
    }
  `);

  try {
    const giveaways = await sanityFetch({
      query: ACTIVE_GIVEAWAYS_QUERY,
    });
    return giveaways.data || [];
  } catch (error) {
    console.error('Error fetching active giveaways:', error);
    return [];
  }
}

export async function getGiveawayById(id: string): Promise<Giveaway | null> {
  const GIVEAWAY_BY_ID_QUERY = defineQuery(`
    *[_type == "giveaway" && _id == $id][0] {
      _id,
      title,
      description,
      status,
      startDate,
      endDate,
      maxTickets,
      ticketPrice,
      createdAt,
      updatedAt,
      image {
        asset->{url}
      },
      participants[]{user, tickets},
      ticketsSold,
      numbersPerCard,
      ticketDigitLength,
      prizes,
      winners[]{user->{_id, name, email}, ticketNumber, prize, rank}
    }
  `);

  try {
    const giveaway = await sanityFetch({
      query: GIVEAWAY_BY_ID_QUERY,
      params: { id },
    });
    return giveaway.data || null;
  } catch (error) {
    console.error('Error fetching giveaway by ID:', error);
    return null;
  }
}

export async function getAllGiveaways(): Promise<Giveaway[]> {
  const ALL_GIVEAWAYS_QUERY = defineQuery(`
    *[_type == "giveaway"] | order(createdAt desc) {
      _id,
      title,
      description,
      status,
      startDate,
      endDate,
      maxTickets,
      ticketPrice,
      createdAt,
      updatedAt,
      image {
        asset->{url}
      },
      participants[]{user, tickets},
      ticketsSold,
      numbersPerCard,
      ticketDigitLength,
      prizes,
      winners[]{user->{_id, name, email}, ticketNumber, prize, rank}
    }
  `);

  try {
    const giveaways = await sanityFetch({
      query: ALL_GIVEAWAYS_QUERY,
    });
    return giveaways.data || [];
  } catch (error) {
    console.error('Error fetching all giveaways:', error);
    return [];
  }
}
