// pages/index.tsx
import Image from 'next/image';
import Link from 'next/link';
import { getActiveAuctions } from '@/sanity/lib/auctions/getAuctions';
import { urlForImage } from '@/sanity/lib/image';
import { Auction } from '@/sanity.types';

export default async function Auctions() {
  const auctions = await getActiveAuctions();

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Açık Artırmalar</h1>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {auctions.map((auction) => (
          <div key={auction.id} className="border p-4 rounded">
            {auction.image && (
              <Image
                src={urlForImage(auction.image).url()}
                alt={auction.name || 'Ürün Görseli'}
                width={200}
                height={150}
                className="rounded"
              />
            )}
            <h2 className="text-lg font-semibold">{auction.name}</h2>
            <p className="text-blue-600">Teklif: ₺{auction.currentBid}</p>
            <p className="text-sm text-gray-500">
              Bitiş: {new Date(auction.endTime).toLocaleString('tr-TR')}
            </p>
            <Link href={`/auctions/${auction.id}`}>
              <button className="mt-2 bg-blue-500 text-white px-3 py-1 rounded">
                Teklif Ver
              </button>
            </Link>
          </div>
        ))}
      </div>
    </div>
  );
}

