import { NextRequest, NextResponse } from 'next/server';
import { client } from '@/sanity/lib/client';
import { Sale, SaleQueryParams } from '@/types/sanity';

// Enhanced GROQ query for sales
const SALES_QUERY = `
  *[_type == "sales" 
    && defined(title.tr)
    $activeFilter
    $discountTypeFilter
    $validityFilter
    $searchFilter
  ] | order($sortBy $sortOrder) [$start...$end] {
    _id,
    _type,
    _createdAt,
    _updatedAt,
    _rev,
    id,
    title,
    description,
    image {
      asset->{
        _id,
        url
      },
      alt,
      hotspot,
      crop
    },
    products[]->{
      _id,
      name,
      slug,
      price,
      currency
    },
    categories[]->{
      _id,
      title,
      slug
    },
    discountType,
    discountAmount,
    currency,
    couponCode,
    validityFrom,
    validityUntil,
    isActive,
    usageLimit,
    usedCount,
    targetUsers[]->{
      _id,
      name,
      email
    }
  }
`;

const COUNT_QUERY = `
  count(*[_type == "sales" 
    && defined(title.tr)
    $activeFilter
    $discountTypeFilter
    $validityFilter
    $searchFilter
  ])
`;

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Check for special endpoints
    const type = searchParams.get('type');
    
    if (type === 'active') {
      const activeSales = await client.fetch(`
        *[_type == "sales" && isActive == true && validityFrom <= now() && validityUntil >= now()] | order(validityFrom desc) {
          _id, title, description, image, discountType, discountAmount, currency, couponCode, validityFrom, validityUntil, usageLimit, usedCount
        }
      `);
      return NextResponse.json({ data: activeSales, success: true });
    }
    
    if (type === 'expired') {
      const expiredSales = await client.fetch(`
        *[_type == "sales" && validityUntil < now()] | order(validityUntil desc) {
          _id, title, description, image, discountType, discountAmount, currency, couponCode, validityFrom, validityUntil, usageLimit, usedCount
        }
      `);
      return NextResponse.json({ data: expiredSales, success: true });
    }
    
    if (type === 'upcoming') {
      const upcomingSales = await client.fetch(`
        *[_type == "sales" && validityFrom > now()] | order(validityFrom asc) {
          _id, title, description, image, discountType, discountAmount, currency, couponCode, validityFrom, validityUntil, usageLimit, usedCount
        }
      `);
      return NextResponse.json({ data: upcomingSales, success: true });
    }

    // Parse query parameters for regular sales fetching
    const params: SaleQueryParams = {
      isActive: searchParams.get('isActive') === 'true' ? true : 
                searchParams.get('isActive') === 'false' ? false : undefined,
      discountType: searchParams.get('discountType') as any || undefined,
      validFrom: searchParams.get('validFrom') || undefined,
      validTo: searchParams.get('validTo') || undefined,
      search: searchParams.get('search') || undefined,
      page: Number(searchParams.get('page')) || 1,
      limit: Number(searchParams.get('limit')) || 20,
      sortBy: (searchParams.get('sortBy') as any) || 'createdAt',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
      language: (searchParams.get('language') as 'tr' | 'en') || 'tr',
    };

    // Build dynamic filters
    let activeFilter = '';
    let discountTypeFilter = '';
    let validityFilter = '';
    let searchFilter = '';

    if (params.isActive !== undefined) {
      activeFilter = `&& isActive == ${params.isActive}`;
    }

    if (params.discountType) {
      discountTypeFilter = `&& discountType == "${params.discountType}"`;
    }

    if (params.validFrom || params.validTo) {
      const validityConditions = [];
      if (params.validFrom) validityConditions.push(`validityFrom >= "${params.validFrom}"`);
      if (params.validTo) validityConditions.push(`validityUntil <= "${params.validTo}"`);
      validityFilter = `&& (${validityConditions.join(' && ')})`;
    }

    if (params.search) {
      const searchTerm = params.search.toLowerCase();
      searchFilter = `&& (
        title.tr match "*${searchTerm}*" ||
        title.en match "*${searchTerm}*" ||
        description.tr match "*${searchTerm}*" ||
        description.en match "*${searchTerm}*" ||
        couponCode match "*${searchTerm}*"
      )`;
    }

    // Calculate pagination
    const start = (params.page - 1) * params.limit;
    const end = start + params.limit;

    // Build final queries
    const finalSalesQuery = SALES_QUERY
      .replace('$activeFilter', activeFilter)
      .replace('$discountTypeFilter', discountTypeFilter)
      .replace('$validityFilter', validityFilter)
      .replace('$searchFilter', searchFilter)
      .replace('$sortBy', params.sortBy || 'title.tr')
      .replace('$sortOrder', params.sortOrder || 'asc')
      .replace('$start', start.toString())
      .replace('$end', end.toString());

    const finalCountQuery = COUNT_QUERY
      .replace('$activeFilter', activeFilter)
      .replace('$discountTypeFilter', discountTypeFilter)
      .replace('$validityFilter', validityFilter)
      .replace('$searchFilter', searchFilter);

    // Execute queries
    const [sales, total] = await Promise.all([
      client.fetch<Sale[]>(finalSalesQuery),
      client.fetch<number>(finalCountQuery)
    ]);

    // Return response
    return NextResponse.json({
      data: sales,
      pagination: {
        page: params.page,
        limit: params.limit,
        total,
        totalPages: Math.ceil(total / params.limit),
        hasNext: end < total,
        hasPrev: params.page > 1,
      },
      success: true,
    });

  } catch (error) {
    console.error('Error fetching sales:', error);
    return NextResponse.json(
      { 
        error: { 
          message: 'Failed to fetch sales',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        success: false 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const saleData = await request.json();
    
    // Validate coupon code uniqueness
    const existingSale = await client.fetch(
      `*[_type == "sales" && couponCode == $couponCode][0]`,
      { couponCode: saleData.couponCode }
    );

    if (existingSale) {
      return NextResponse.json(
        { 
          error: { message: 'Bu kupon kodu zaten kullanılıyor' },
          success: false 
        },
        { status: 400 }
      );
    }

    // Create sale
    const sale = await client.create({
      _type: 'sales',
      ...saleData,
      usedCount: 0, // Initialize used count
    });

    return NextResponse.json({
      data: sale,
      success: true,
    });

  } catch (error) {
    console.error('Error creating sale:', error);
    return NextResponse.json(
      { 
        error: { 
          message: 'Failed to create sale',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        success: false 
      },
      { status: 500 }
    );
  }
}
