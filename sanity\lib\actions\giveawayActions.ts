// Sanity Actions for Giveaway Management
import { client } from '../client';
import { nanoid } from 'nanoid';

// Generate ticket number
export const generateTicketNumber = (digitLength: number): string => {
  const max = Math.pow(10, digitLength) - 1;
  const min = Math.pow(10, digitLength - 1);
  return String(Math.floor(Math.random() * (max - min + 1)) + min).padStart(digitLength, '0');
};

// Purchase tickets for giveaway
export const purchaseTickets = async (
  giveawayId: string,
  userId: string,
  ticketCount: number
): Promise<{ success: boolean; tickets?: string[]; error?: string }> => {
  try {
    // Get current giveaway state
    const giveaway = await client.fetch(
      `*[_type == "giveaway" && _id == $giveawayId][0]{
        totalTickets,
        ticketsSold,
        maxTicketsPerUser,
        ticketDigitLength,
        status,
        startDate,
        endDate,
        participants,
        ticketPrice,
        currency
      }`,
      { giveawayId }
    );
    
    if (!giveaway) {
      return { success: false, error: 'Çekiliş bulunamadı' };
    }
    
    // Validate giveaway status and timing
    const now = new Date();
    const startDate = new Date(giveaway.startDate);
    const endDate = new Date(giveaway.endDate);
    
    if (giveaway.status !== 'active' || now < startDate || now > endDate) {
      return { success: false, error: 'Çekiliş aktif değil' };
    }
    
    // Check ticket availability
    if (giveaway.ticketsSold + ticketCount > giveaway.totalTickets) {
      return { success: false, error: 'Yeterli bilet kalmamış' };
    }
    
    // Check user's current ticket count
    const userParticipation = giveaway.participants?.find((p: any) => p.user._ref === userId);
    const currentUserTickets = userParticipation?.tickets?.length || 0;
    
    if (currentUserTickets + ticketCount > giveaway.maxTicketsPerUser) {
      return { success: false, error: `Maksimum ${giveaway.maxTicketsPerUser} bilet alabilirsiniz` };
    }
    
    // Generate unique ticket numbers
    const newTickets: string[] = [];
    const existingTicketNumbers = new Set();
    
    // Collect existing ticket numbers
    giveaway.participants?.forEach((participant: any) => {
      participant.tickets?.forEach((ticket: any) => {
        existingTicketNumbers.add(ticket.ticketNumber);
      });
    });
    
    // Generate new unique ticket numbers
    for (let i = 0; i < ticketCount; i++) {
      let ticketNumber: string;
      do {
        ticketNumber = generateTicketNumber(giveaway.ticketDigitLength);
      } while (existingTicketNumbers.has(ticketNumber));
      
      newTickets.push(ticketNumber);
      existingTicketNumbers.add(ticketNumber);
    }
    
    // Prepare ticket objects
    const ticketObjects = newTickets.map(ticketNumber => ({
      ticketNumber,
      purchasedAt: new Date().toISOString(),
      chosenDigitCount: giveaway.ticketDigitLength,
      status: 'active'
    }));
    
    // Update participants array
    const updatedParticipants = [...(giveaway.participants || [])];
    const participantIndex = updatedParticipants.findIndex((p: any) => p.user._ref === userId);
    
    if (participantIndex >= 0) {
      // User already exists, add new tickets
      updatedParticipants[participantIndex].tickets = [
        ...(updatedParticipants[participantIndex].tickets || []),
        ...ticketObjects
      ];
    } else {
      // New user, create new participant
      updatedParticipants.push({
        user: { _type: 'reference', _ref: userId },
        tickets: ticketObjects
      });
    }
    
    // Update giveaway
    await client
      .patch(giveawayId)
      .set({
        participants: updatedParticipants,
        ticketsSold: giveaway.ticketsSold + ticketCount
      })
      .commit();
    
    // Create order for ticket purchase
    await createOrderForTickets(giveawayId, userId, ticketCount, giveaway.ticketPrice, giveaway.currency);
    
    // Check if draw should be scheduled
    const newSalePercentage = ((giveaway.ticketsSold + ticketCount) / giveaway.totalTickets) * 100;
    if (newSalePercentage >= giveaway.ticketSalePercentageForDraw) {
      await scheduleGiveawayDraw(giveawayId);
    }
    
    return { success: true, tickets: newTickets };
  } catch (error) {
    console.error('Bilet satın alma hatası:', error);
    return { success: false, error: 'Bilet satın alınamadı' };
  }
};

// Schedule giveaway draw
export const scheduleGiveawayDraw = async (giveawayId: string): Promise<{ success: boolean; error?: string }> => {
  try {
    const giveaway = await client.fetch(
      `*[_type == "giveaway" && _id == $giveawayId][0]{endDate, drawDate, status}`,
      { giveawayId }
    );
    
    if (!giveaway) {
      return { success: false, error: 'Çekiliş bulunamadı' };
    }
    
    if (giveaway.drawDate) {
      return { success: true }; // Already scheduled
    }
    
    // Set draw date to 1 day after end date
    const drawDate = new Date(giveaway.endDate);
    drawDate.setDate(drawDate.getDate() + 1);
    
    await client
      .patch(giveawayId)
      .set({ 
        drawDate: drawDate.toISOString(),
        status: 'draw_ready'
      })
      .commit();
    
    return { success: true };
  } catch (error) {
    console.error('Çekiliş tarihi ayarlama hatası:', error);
    return { success: false, error: 'Çekiliş tarihi ayarlanamadı' };
  }
};

// Conduct giveaway draw
export const conductGiveawayDraw = async (giveawayId: string): Promise<{ success: boolean; winners?: any[]; error?: string }> => {
  try {
    const giveaway = await client.fetch(
      `*[_type == "giveaway" && _id == $giveawayId][0]{
        participants,
        prizes,
        status,
        drawDate,
        winningNumbers,
        winners
      }`,
      { giveawayId }
    );
    
    if (!giveaway) {
      return { success: false, error: 'Çekiliş bulunamadı' };
    }
    
    if (giveaway.status !== 'draw_ready') {
      return { success: false, error: 'Çekiliş hazır değil' };
    }
    
    if (giveaway.winners && giveaway.winners.length > 0) {
      return { success: false, error: 'Çekiliş zaten yapılmış' };
    }
    
    // Collect all ticket numbers
    const allTickets: { ticketNumber: string; userId: string }[] = [];
    giveaway.participants?.forEach((participant: any) => {
      participant.tickets?.forEach((ticket: any) => {
        if (ticket.status === 'active') {
          allTickets.push({
            ticketNumber: ticket.ticketNumber,
            userId: participant.user._ref
          });
        }
      });
    });
    
    if (allTickets.length === 0) {
      return { success: false, error: 'Aktif bilet bulunamadı' };
    }
    
    // Generate winning numbers and select winners
    const winningNumbers: string[] = [];
    const winners: any[] = [];
    
    // Sort prizes by rank
    const sortedPrizes = [...giveaway.prizes].sort((a, b) => a.rank - b.rank);
    
    for (const prize of sortedPrizes) {
      if (allTickets.length === 0) break;
      
      // Randomly select a winning ticket
      const randomIndex = Math.floor(Math.random() * allTickets.length);
      const winningTicket = allTickets[randomIndex];
      
      winningNumbers.push(winningTicket.ticketNumber);
      winners.push({
        user: { _type: 'reference', _ref: winningTicket.userId },
        ticketNumber: winningTicket.ticketNumber,
        prize: { _type: 'reference', _ref: prize._id || `prize-${prize.rank}` },
        rank: prize.rank
      });
      
      // Remove the winning ticket from available tickets
      allTickets.splice(randomIndex, 1);
    }
    
    // Update giveaway with results
    await client
      .patch(giveawayId)
      .set({
        winningNumbers,
        winners,
        status: 'completed'
      })
      .commit();
    
    // Create orders for winners
    for (const winner of winners) {
      await createOrderForWinner(giveawayId, winner);
      
      // Create notification for winner
      await createNotification({
        type: 'giveaway_won',
        title: { tr: 'Çekiliş Kazandınız!', en: 'You Won the Giveaway!' },
        message: { tr: 'Tebrikler! Çekilişi kazandınız', en: 'Congratulations! You won the giveaway' },
        user: { _type: 'reference', _ref: winner.user._ref },
        relatedDocument: { _type: 'reference', _ref: giveawayId }
      });
    }
    
    return { success: true, winners };
  } catch (error) {
    console.error('Çekiliş yapma hatası:', error);
    return { success: false, error: 'Çekiliş yapılamadı' };
  }
};

// Create order for ticket purchase
export const createOrderForTickets = async (
  giveawayId: string,
  userId: string,
  ticketCount: number,
  ticketPrice: number,
  currency: string
): Promise<void> => {
  try {
    const orderData = {
      _type: 'order',
      orderNumber: `ORD-${nanoid(8)}`,
      customer: {
        name: 'Giveaway Participant', // This should be populated from user data
        email: '<EMAIL>', // This should be populated from user data
        clerkUserId: userId
      },
      orderItems: [{
        product: { _type: 'reference', _ref: giveawayId },
        quantity: ticketCount,
        price: ticketPrice,
        currency
      }],
      totalAmount: ticketPrice * ticketCount,
      currency,
      paymentStatus: 'pending',
      orderStatus: 'pending',
      source: 'giveaway',
      createdAt: new Date().toISOString(),
    };
    
    await client.create(orderData);
  } catch (error) {
    console.error('Bilet siparişi oluşturma hatası:', error);
  }
};

// Create order for giveaway winner
export const createOrderForWinner = async (giveawayId: string, winner: any): Promise<void> => {
  try {
    const orderData = {
      _type: 'order',
      orderNumber: `ORD-${nanoid(8)}`,
      customer: {
        name: 'Giveaway Winner', // This should be populated from user data
        email: '<EMAIL>', // This should be populated from user data
        clerkUserId: winner.user._ref
      },
      orderItems: [{
        product: { _type: 'reference', _ref: winner.prize._ref },
        quantity: 1,
        price: 0, // Free for winners
        currency: 'TRY'
      }],
      totalAmount: 0,
      currency: 'TRY',
      paymentStatus: 'paid',
      orderStatus: 'confirmed',
      source: 'giveaway',
      createdAt: new Date().toISOString(),
    };
    
    await client.create(orderData);
  } catch (error) {
    console.error('Kazanan için sipariş oluşturma hatası:', error);
  }
};

// Create notification
export const createNotification = async (notificationData: any): Promise<void> => {
  try {
    await client.create({
      _type: 'notification',
      id: `NOT-${nanoid(8)}`,
      ...notificationData,
      sentAt: new Date().toISOString(),
      isRead: false
    });
  } catch (error) {
    console.error('Bildirim oluşturma hatası:', error);
  }
};

// Update giveaway status based on time
export const updateGiveawayStatus = async (giveawayId: string): Promise<{ success: boolean; newStatus?: string; error?: string }> => {
  try {
    const giveaway = await client.fetch(
      `*[_type == "giveaway" && _id == $giveawayId][0]{
        startDate, 
        endDate, 
        drawDate, 
        status, 
        ticketsSold, 
        totalTickets, 
        ticketSalePercentageForDraw
      }`,
      { giveawayId }
    );
    
    if (!giveaway) {
      return { success: false, error: 'Çekiliş bulunamadı' };
    }
    
    const now = new Date();
    const startDate = new Date(giveaway.startDate);
    const endDate = new Date(giveaway.endDate);
    const drawDate = giveaway.drawDate ? new Date(giveaway.drawDate) : null;
    
    let newStatus = giveaway.status;
    
    // Determine new status based on time and conditions
    if (now < startDate && giveaway.status !== 'upcoming') {
      newStatus = 'upcoming';
    } else if (now >= startDate && now <= endDate && giveaway.status !== 'active') {
      newStatus = 'active';
    } else if (now > endDate && giveaway.status === 'active') {
      // Check if draw should be ready
      const salePercentage = (giveaway.ticketsSold / giveaway.totalTickets) * 100;
      if (salePercentage >= giveaway.ticketSalePercentageForDraw) {
        newStatus = 'draw_ready';
        if (!giveaway.drawDate) {
          await scheduleGiveawayDraw(giveawayId);
        }
      } else {
        newStatus = 'completed'; // Not enough tickets sold
      }
    } else if (drawDate && now >= drawDate && giveaway.status === 'draw_ready') {
      // Auto-conduct draw if time has come
      const drawResult = await conductGiveawayDraw(giveawayId);
      if (drawResult.success) {
        newStatus = 'completed';
      }
    }
    
    // Update status if it has changed
    if (newStatus !== giveaway.status) {
      await client
        .patch(giveawayId)
        .set({ status: newStatus })
        .commit();
      
      return { success: true, newStatus };
    }
    
    return { success: true, newStatus: giveaway.status };
  } catch (error) {
    console.error('Çekiliş durumu güncelleme hatası:', error);
    return { success: false, error: 'Durum güncellenemedi' };
  }
};

// Get giveaway statistics
export const getGiveawayStatistics = async (giveawayId: string) => {
  try {
    const giveaway = await client.fetch(
      `*[_type == "giveaway" && _id == $giveawayId][0]{
        totalTickets,
        ticketsSold,
        ticketPrice,
        currency,
        participants,
        prizes
      }`,
      { giveawayId }
    );
    
    if (!giveaway) {
      return null;
    }
    
    const salePercentage = (giveaway.ticketsSold / giveaway.totalTickets) * 100;
    const totalRevenue = giveaway.ticketsSold * giveaway.ticketPrice;
    const uniqueParticipants = giveaway.participants?.length || 0;
    const remainingTickets = giveaway.totalTickets - giveaway.ticketsSold;
    
    return {
      totalTickets: giveaway.totalTickets,
      ticketsSold: giveaway.ticketsSold,
      remainingTickets,
      salePercentage: salePercentage.toFixed(2),
      totalRevenue,
      uniqueParticipants,
      totalPrizes: giveaway.prizes?.length || 0,
      averageTicketsPerUser: uniqueParticipants > 0 ? (giveaway.ticketsSold / uniqueParticipants).toFixed(2) : '0'
    };
  } catch (error) {
    console.error('Çekiliş istatistikleri alma hatası:', error);
    return null;
  }
};
