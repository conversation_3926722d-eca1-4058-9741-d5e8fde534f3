import { getAllCategories } from "@/sanity/lib/products/getAllCategories";
import { getProductsByCategory } from "@/sanity/lib/products/getProductsByCategory";
import ProductsView from "@/components/productsView";

async function CategoryPage({ params }: { params: { slug: string } }) {
  const { slug } = await params;
  const products = await getProductsByCategory(slug);
  const categories = await getAllCategories();

  return <div
  className="flex flex-col items-center justify-top min-h-screen bg-gray-100 p-4">
    <div className="flex flex-col items-center justify-center w-full max-w-7xl">
        <h1 className="text-2xl font-bold text-center">
            {slug
            ?.split("-")
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
            .join(" ")
            }
            Collection
        </h1>
    </div>
    <ProductsView products={products} categories={categories} />
  </div>;
}

export default CategoryPage;
