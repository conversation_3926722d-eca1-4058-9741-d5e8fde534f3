/**
 * Unit tests for giveaway/lottery system functions
 */

describe('Giveaway Functions Unit Tests', () => {
  describe('Number generation', () => {
    it('should generate random numbers within specified range', () => {
      const generateRandomNumber = (min: number, max: number) => {
        return Math.floor(Math.random() * (max - min + 1)) + min
      }

      // Test 3-digit numbers (100-999)
      for (let i = 0; i < 100; i++) {
        const num = generateRandomNumber(100, 999)
        expect(num).toBeGreaterThanOrEqual(100)
        expect(num).toBeLessThanOrEqual(999)
      }

      // Test 4-digit numbers (1000-9999)
      for (let i = 0; i < 100; i++) {
        const num = generateRandomNumber(1000, 9999)
        expect(num).toBeGreaterThanOrEqual(1000)
        expect(num).toBeLessThanOrEqual(9999)
      }
    })

    it('should generate unique numbers for a ticket', () => {
      const generateUniqueNumbers = (count: number, min: number, max: number) => {
        const numbers = new Set<number>()
        while (numbers.size < count) {
          const num = Math.floor(Math.random() * (max - min + 1)) + min
          numbers.add(num)
        }
        return Array.from(numbers)
      }

      const numbers = generateUniqueNumbers(6, 100, 999)
      
      expect(numbers).toHaveLength(6)
      expect(new Set(numbers).size).toBe(6) // All numbers should be unique
      
      numbers.forEach(num => {
        expect(num).toBeGreaterThanOrEqual(100)
        expect(num).toBeLessThanOrEqual(999)
      })
    })

    it('should validate ticket number configuration', () => {
      const validateTicketConfig = (digitCount: number, numberCount: number) => {
        const validDigitCounts = [3, 4, 5]
        const validNumberCounts = [3, 4, 6, 9, 12]
        
        return validDigitCounts.includes(digitCount) && 
               validNumberCounts.includes(numberCount)
      }

      // Valid configurations
      expect(validateTicketConfig(3, 6)).toBe(true)
      expect(validateTicketConfig(4, 9)).toBe(true)
      expect(validateTicketConfig(5, 12)).toBe(true)

      // Invalid configurations
      expect(validateTicketConfig(2, 6)).toBe(false)
      expect(validateTicketConfig(3, 5)).toBe(false)
      expect(validateTicketConfig(6, 9)).toBe(false)
    })
  })

  describe('Winning conditions', () => {
    it('should check last digit match', () => {
      const checkLastDigitMatch = (ticketNumber: number, winningNumber: number) => {
        return ticketNumber % 10 === winningNumber % 10
      }

      expect(checkLastDigitMatch(123, 453)).toBe(true) // Both end with 3
      expect(checkLastDigitMatch(456, 789)).toBe(false) // 6 vs 9
      expect(checkLastDigitMatch(100, 200)).toBe(true) // Both end with 0
    })

    it('should check last three digits match', () => {
      const checkLastThreeDigitsMatch = (ticketNumber: number, winningNumber: number) => {
        return ticketNumber % 1000 === winningNumber % 1000
      }

      expect(checkLastThreeDigitsMatch(1234, 5234)).toBe(true) // Both end with 234
      expect(checkLastThreeDigitsMatch(1234, 1235)).toBe(false) // 234 vs 235
      expect(checkLastThreeDigitsMatch(123, 123)).toBe(true) // Exact match
    })

    it('should check exact number match', () => {
      const checkExactMatch = (ticketNumber: number, winningNumber: number) => {
        return ticketNumber === winningNumber
      }

      expect(checkExactMatch(12345, 12345)).toBe(true)
      expect(checkExactMatch(12345, 12346)).toBe(false)
      expect(checkExactMatch(123, 123)).toBe(true)
    })

    it('should determine winning type', () => {
      const determineWinningType = (ticketNumber: number, winningNumber: number) => {
        if (ticketNumber === winningNumber) return 'exact'
        if (ticketNumber % 1000 === winningNumber % 1000) return 'lastThree'
        if (ticketNumber % 100 === winningNumber % 100) return 'lastTwo'
        if (ticketNumber % 10 === winningNumber % 10) return 'lastOne'
        return 'none'
      }

      expect(determineWinningType(12345, 12345)).toBe('exact')
      expect(determineWinningType(12345, 98345)).toBe('lastThree')
      expect(determineWinningType(12345, 98745)).toBe('lastTwo')
      expect(determineWinningType(12345, 98765)).toBe('lastOne')
      expect(determineWinningType(12345, 98764)).toBe('none')
    })
  })

  describe('Ticket validation', () => {
    it('should validate ticket price', () => {
      const validateTicketPrice = (price: number) => {
        return price > 0 && price <= 1000 && Number.isFinite(price)
      }

      expect(validateTicketPrice(10)).toBe(true)
      expect(validateTicketPrice(0.5)).toBe(true)
      expect(validateTicketPrice(1000)).toBe(true)

      expect(validateTicketPrice(0)).toBe(false)
      expect(validateTicketPrice(-5)).toBe(false)
      expect(validateTicketPrice(1001)).toBe(false)
      expect(validateTicketPrice(Infinity)).toBe(false)
      expect(validateTicketPrice(NaN)).toBe(false)
    })

    it('should validate giveaway status', () => {
      const validateGiveawayStatus = (status: string) => {
        const validStatuses = ['active', 'inactive', 'completed']
        return validStatuses.includes(status)
      }

      expect(validateGiveawayStatus('active')).toBe(true)
      expect(validateGiveawayStatus('inactive')).toBe(true)
      expect(validateGiveawayStatus('completed')).toBe(true)

      expect(validateGiveawayStatus('pending')).toBe(false)
      expect(validateGiveawayStatus('cancelled')).toBe(false)
      expect(validateGiveawayStatus('')).toBe(false)
    })

    it('should validate giveaway dates', () => {
      const validateGiveawayDates = (startDate: string, endDate: string) => {
        const start = new Date(startDate)
        const end = new Date(endDate)
        const now = new Date()

        return start < end && end > now
      }

      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      
      const nextWeek = new Date()
      nextWeek.setDate(nextWeek.getDate() + 7)

      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)

      expect(validateGiveawayDates(tomorrow.toISOString(), nextWeek.toISOString())).toBe(true)
      expect(validateGiveawayDates(nextWeek.toISOString(), tomorrow.toISOString())).toBe(false) // end before start
      expect(validateGiveawayDates(yesterday.toISOString(), tomorrow.toISOString())).toBe(true) // can start in past
      expect(validateGiveawayDates(yesterday.toISOString(), yesterday.toISOString())).toBe(false) // end in past
    })
  })

  describe('Prize calculation', () => {
    it('should calculate prize amounts based on winning type', () => {
      const calculatePrize = (winningType: string, baseAmount: number) => {
        const multipliers = {
          'exact': 1000,
          'lastThree': 100,
          'lastTwo': 10,
          'lastOne': 2,
          'none': 0
        }
        
        return baseAmount * (multipliers[winningType as keyof typeof multipliers] || 0)
      }

      expect(calculatePrize('exact', 10)).toBe(10000)
      expect(calculatePrize('lastThree', 10)).toBe(1000)
      expect(calculatePrize('lastTwo', 10)).toBe(100)
      expect(calculatePrize('lastOne', 10)).toBe(20)
      expect(calculatePrize('none', 10)).toBe(0)
      expect(calculatePrize('invalid', 10)).toBe(0)
    })
  })
})
