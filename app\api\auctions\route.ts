// app/api/bid/route.ts
import { client } from '@/sanity/lib/client';
import { getAuctionById } from '@/sanity/lib/auctions/getAuctions';
import { getAuth } from '@clerk/nextjs/server';
import type { NextRequest } from 'next/server';
import { getUserByClerkId } from '@/sanity/lib/users/getUser';

export async function POST(req: NextRequest) {
  const { auctionId, userId, bidAmount } = await req.json();
  const { userId: clerkUserId } = getAuth(req);

  // Kullanıcı doğrulaması
  if (!clerkUserId || clerkUserId !== userId) {
    return new Response(JSON.stringify({ error: 'Yetkisiz' }), { status: 401 });
  }

  // Kullanıcının yönetici tarafından onaylı olup olmadığını kontrol et
  const user = await getUserByClerkId(clerkUserId);
  if (!user || !user.isAdminApproved) {
    return new Response(JSON.stringify({ error: 'Teklif vermek için onaylanmanız gerekiyor.' }), { status: 403 });
  }

  // Açık artırma kontrolü
  const auction = await getAuctionById(auctionId);
  if (!auction) {
    return new Response(JSON.stringify({ error: 'Açık artırma bulunamadı' }), { status: 404 });
  }
  if (auction.status !== 'active') {
    return new Response(JSON.stringify({ error: 'Açık artırma aktif değil' }), { status: 400 });
  }
  if (bidAmount <= auction.currentBid) {
    return new Response(JSON.stringify({ error: 'Daha yüksek teklif gerekli' }), { status: 400 });
  }

  // Teklifi kaydet
  try {
    await client.patch(auctionId).set({ currentBid: bidAmount }).commit();
    return new Response(JSON.stringify({ success: true }), { status: 200 });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Teklif kaydedilemedi' }), { status: 500 });
  }
}