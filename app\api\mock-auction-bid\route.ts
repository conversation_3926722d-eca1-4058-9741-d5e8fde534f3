import { NextRequest, NextResponse } from 'next/server'
import { currentUser } from '@clerk/nextjs/server'

// Geçici mock sistem - Sanity permissions sorunu ç<PERSON>ü<PERSON> kadar
export async function POST(request: NextRequest) {
  try {
    const { auctionId, bidAmount } = await request.json();
    const clerkUser = await currentUser();

    if (!clerkUser) {
      return NextResponse.json({
        success: false,
        error: '<PERSON><PERSON><PERSON> ya<PERSON>n<PERSON>z'
      }, { status: 401 });
    }

    console.log(`💰 Mock teklif verme:`, {
      user: clerkUser.emailAddresses[0]?.emailAddress,
      auctionId,
      bidAmount
    });

    // Simulate successful bid
    return NextResponse.json({
      success: true,
      message: `₺${bidAmount} teklifi ba<PERSON> verildi (Mock)`,
      data: {
        bidAmount,
        auctionId,
        userId: clerkUser.id,
        bidDate: new Date().toISOString(),
        note: "Bu mock bir teklif işlemidir. Sanity permissions düzeltilince gerçek sistem aktif olacak."
      }
    });

  } catch (error) {
    console.error('❌ Mock bid error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
