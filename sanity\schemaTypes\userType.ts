import { UserIcon } from '@sanity/icons';
import { defineArrayMember, defineField, defineType } from 'sanity';

// Helper function for image field
const defineImageField = (title = 'Görsel') =>
  defineField({
    name: 'image',
    title,
    type: 'image',
    options: { hotspot: true },
    fields: [
      {
        name: 'alt',
        title: 'Alternatif Metin',
        type: 'string',
        validation: (Rule) => Rule.required().error('Alternatif metin zorunludur.'),
      },
    ],
  });

export const userType = defineType({
  name: 'user',
  title: 'Kullanıc<PERSON>',
  type: 'document',
  icon: UserIcon,
  groups: [
    { name: 'basic', title: 'Temel Bilgiler' },
    { name: 'contact', title: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { name: 'address', title: 'Adres Bilgileri' },
    { name: 'wallet', title: 'Cüzdan' },
    { name: 'permissions', title: '<PERSON><PERSON><PERSON>' },
    { name: 'activity', title: 'Aktivite' },
  ],
  fields: [
    defineField({
      name: 'clerkId',
      title: 'Clerk <PERSON><PERSON>ı<PERSON>ı ID',
      type: 'string',
      group: 'basic',
      validation: (Rule) => Rule.required().error('Clerk <PERSON><PERSON><PERSON><PERSON><PERSON> ID zorunludur.'),
      description: 'Clerk’ten gelen benzersiz kullanıcı ID’si.',
    }),
    defineField({
      name: 'name',
      title: 'İsim',
      type: 'object',
      group: 'basic',
      fields: [
        {
          name: 'tr',
          title: 'Türkçe',
          type: 'string',
          validation: (Rule) => Rule.required().error('Türkçe isim zorunludur.')
        },
        {
          name: 'en',
          title: 'İngilizce',
          type: 'string'
        },
      ],
      validation: (Rule) => Rule.required().error('İsim bilgisi zorunludur.'),
    }),
    defineField({
      name: 'email',
      title: 'E-posta',
      type: 'string',
      group: 'contact',
      validation: (Rule) => Rule.required().email().error('Geçerli bir e-posta adresi giriniz.'),
    }),
    defineImageField('Profil Resmi'),
    defineField({
      name: 'phoneNumber',
      title: 'Telefon Numarası',
      type: 'string',
      group: 'contact',
      validation: (Rule) =>
        Rule.regex(/^\+?[1-9]\d{1,14}$/, { name: 'phone' }).error('Geçerli bir telefon numarası giriniz.'),
    }),

    // Adres Bilgileri
    defineField({
      name: 'addresses',
      title: 'Adresler',
      type: 'array',
      group: 'address',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'street',
              title: 'Sokak/Cadde',
              type: 'string',
              validation: (Rule) => Rule.required().error('Sokak/Cadde zorunludur.'),
            },
            {
              name: 'city',
              title: 'Şehir',
              type: 'string',
              validation: (Rule) => Rule.required().error('Şehir zorunludur.'),
            },
            {
              name: 'state',
              title: 'İl/Eyalet',
              type: 'string',
            },
            {
              name: 'postalCode',
              title: 'Posta Kodu',
              type: 'string',
              validation: (Rule) => Rule.required().error('Posta kodu zorunludur.'),
            },
            {
              name: 'country',
              title: 'Ülke',
              type: 'string',
              initialValue: 'Türkiye',
              validation: (Rule) => Rule.required().error('Ülke zorunludur.'),
            },
            {
              name: 'isDefault',
              title: 'Varsayılan Adres',
              type: 'boolean',
              initialValue: false,
            },
          ],
          preview: {
            select: {
              street: 'street',
              city: 'city',
              isDefault: 'isDefault',
            },
            prepare({ street, city, isDefault }) {
              return {
                title: `${street}, ${city}`,
                subtitle: isDefault ? 'Varsayılan Adres' : 'Adres',
              };
            },
          },
        },
      ],
      description: 'Kullanıcının kayıtlı adresleri.',
    }),

    defineField({
      name: 'status',
      title: 'Kullanıcı Durumu',
      type: 'string',
      group: 'basic',
      options: {
        list: [
          { title: 'Aktif', value: 'active' },
          { title: 'Pasif', value: 'inactive' },
          { title: 'Askıya Alındı', value: 'suspended' },
        ],
      },
      initialValue: 'active',
      validation: (Rule) => Rule.required().error('Kullanıcı durumu zorunludur.'),
    }),
    defineField({
      name: 'lastLogin',
      title: 'Son Giriş Tarihi',
      type: 'datetime',
      group: 'activity',
      readOnly: true,
    }),
    defineField({
      name: 'walletBalance',
      title: 'Cüzdan Bakiyesi',
      type: 'number',
      group: 'wallet',
      initialValue: 0,
      validation: (Rule) => Rule.required().min(0).max(1000000).error('Cüzdan bakiyesi 0 ile 1.000.000 arasında olmalıdır.'),
    }),
    defineField({
      name: 'currency',
      title: 'Para Birimi',
      type: 'string',
      group: 'wallet',
      options: {
        list: [
          { title: 'Türk Lirası (TRY)', value: 'TRY' },
          { title: 'ABD Doları (USD)', value: 'USD' },
          { title: 'Euro (EUR)', value: 'EUR' },
        ],
      },
      initialValue: 'TRY',
      validation: (Rule) => Rule.required().error('Para birimi zorunludur.'),
    }),
    defineField({
      name: 'isAdminApproved',
      title: 'Yönetici Onaylı',
      type: 'boolean',
      group: 'permissions',
      initialValue: false,
      description: 'Bu kullanıcı açık artırmalara ve çekilişlere katılabilir mi?',
    }),
    defineField({
      name: 'role',
      title: 'Kullanıcı Rolü',
      type: 'string',
      group: 'permissions',
      options: {
        list: [
          { title: 'Kullanıcı', value: 'user' },
          { title: 'Moderatör', value: 'moderator' },
          { title: 'Finans Yöneticisi', value: 'finance_manager' },
          { title: 'Admin', value: 'admin' },
        ],
      },
      initialValue: 'user',
      validation: (Rule) => Rule.required().error('Rol zorunludur.'),
    }),
  ],
  preview: {
    select: {
      title: 'name.tr',
      subtitle: 'email',
      media: 'image',
      balance: 'walletBalance',
      currency: 'currency',
      status: 'status',
    },
    prepare({ title, subtitle, media, balance, currency, status }) {
      return {
        title: title || 'İsimsiz Kullanıcı',
        subtitle: `${subtitle || 'E-posta Yok'} - ${balance ? `${balance} ${currency || 'TRY'}` : 'Bakiye Yok'} - ${status || 'Durum Yok'}`,
        media,
      };
    },
  },
});