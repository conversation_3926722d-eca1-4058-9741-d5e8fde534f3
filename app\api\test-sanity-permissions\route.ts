import { NextRequest, NextResponse } from 'next/server'
import { writeClient } from '@/sanity/lib/client'

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Testing Sanity permissions...');
    console.log('Project ID:', process.env.NEXT_PUBLIC_SANITY_PROJECT_ID);
    console.log('Dataset:', process.env.NEXT_PUBLIC_SANITY_DATASET);
    console.log('Token exists:', !!process.env.SANITY_API_TOKEN);

    // Test read permissions
    console.log('📖 Testing read permissions...');
    const readResult = await writeClient.fetch('*[_type == "user"][0...3]{_id, name, email}');
    console.log('✅ Read test successful:', readResult);

    // Test write permissions with a simple create operation
    console.log('✏️ Testing write permissions...');
    const testUser = {
      _type: 'user',
      _id: `test-user-${Date.now()}`,
      clerkId: `test-clerk-${Date.now()}`,
      name: 'Test User',
      email: '<EMAIL>',
      isAdminApproved: false,
      walletBalance: 0,
    };

    const writeResult = await writeClient.create(testUser);
    console.log('✅ Write test successful:', writeResult);

    // Clean up test user
    await writeClient.delete(writeResult._id);
    console.log('🗑️ Test user cleaned up');

    return NextResponse.json({
      success: true,
      message: 'Sanity permissions test successful',
      readTest: readResult,
      writeTest: 'User created and deleted successfully',
      config: {
        projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
        dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
        hasToken: !!process.env.SANITY_API_TOKEN,
      }
    });

  } catch (error) {
    console.error('❌ Sanity permissions test failed:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      config: {
        projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
        dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
        hasToken: !!process.env.SANITY_API_TOKEN,
      }
    }, { status: 500 });
  }
}
