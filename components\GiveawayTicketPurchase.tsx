"use client";

import React, { useState, useEffect } from "react";
import { useUser } from "@clerk/nextjs";
import { FiGift, FiUser, FiCreditCard, FiCheck } from "react-icons/fi";

interface GiveawayTicketPurchaseProps {
  giveawayId: string;
  ticketPrice?: number;
  maxTicketsPerUser?: number; // admin onayı için
}

export default function GiveawayTicketPurchase({
  giveawayId,
  ticketPrice = 10,
  maxTicketsPerUser = 100,
}: GiveawayTicketPurchaseProps) {
  const { user, isLoaded } = useUser();
  const [numberOfTickets, setNumberOfTickets] = useState(1);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [purchasedTickets, setPurchasedTickets] = useState<string[][]>([]); // her kart bir dizi numara
  const [allPurchasedTickets, setAllPurchasedTickets] = useState<string[][]>(
    []
  );

  const totalPrice = numberOfTickets * ticketPrice;

  const handlePurchase = async () => {
    if (!user) {
      setError("Bilet satın almak için giriş yapmalısınız.");
      return;
    }
    if (numberOfTickets > maxTicketsPerUser) {
      setError("10 adetten fazla bilet almak için yönetici onayı gereklidir.");
      return;
    }
    setLoading(true);
    setError(null);
    try {
      // Geçici olarak mock API kullan - Sanity permissions sorunu çözülene kadar
      const response = await fetch("/api/mock-ticket-purchase", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          giveawayId,
          numberOfTickets,
        }),
      });
      const data = await response.json();
      if (data.success) {
        setPurchasedTickets(data.purchasedTickets || []);
        setSuccess(true);
        setError(null);
      } else {
        setError(data.error || "Bilet satın alınamadı.");
        setSuccess(false);
      }
    } catch (err) {
      setError("Bilet satın alınırken bir hata oluştu.");
      setSuccess(false);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (success && purchasedTickets.length > 0) {
      setAllPurchasedTickets((prev) => [...prev, ...purchasedTickets]);
    }
  }, [success, purchasedTickets]);

  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Yükleniyor...</span>
      </div>
    );
  }
  if (!user) {
    return (
      <div className="text-center p-8 bg-gray-50 rounded-lg">
        <FiUser size={48} className="mx-auto text-gray-400 mb-4" />
        <h3 className="text-lg font-semibold text-gray-700 mb-2">
          Giriş Yapın
        </h3>
        <p className="text-gray-600 mb-4">
          Çekilişe katılmak için giriş yapmalısınız.
        </p>
        <button className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-lg transition-colors">
          Giriş Yap
        </button>
      </div>
    );
  }
  if (success) {
    return (
      <div className="text-center p-8 bg-green-50 rounded-lg border border-green-200">
        <FiCheck size={48} className="mx-auto text-green-600 mb-4" />
        <h3 className="text-lg font-semibold text-green-800 mb-2">
          Biletleriniz Başarıyla Satın Alındı!
        </h3>
        <p className="text-green-700 mb-4">
          {numberOfTickets} Adet Bilet satın aldınız.
        </p>
        {purchasedTickets.length > 0 && (
          <div className="bg-white p-4 rounded-lg border border-green-200 mb-4">
            <h4 className="font-semibold text-gray-800 mb-2">
              Biletinizdeki Numaralar:
            </h4>
            <div className="flex flex-wrap gap-2 justify-center">
              {purchasedTickets.map((card, idx) => (
                <span
                  key={idx}
                  className="bg-green-100 text-green-800 px-3 py-1 rounded-full font-mono font-bold"
                >
                  {card.join(", ")}
                </span>
              ))}
            </div>
          </div>
        )}
        <div className="flex flex-col md:flex-row gap-4 justify-center mt-6">
          <a
            href="/"
            className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-lg"
          >
            Ana Sayfa
          </a>
          <button
            onClick={() => window.location.reload()}
            className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 rounded-lg"
          >
            Bilet Al Sayfasına Dön
          </button>
          <a
            href="/wallet"
            className="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-2 px-6 rounded-lg"
          >
            Biletlerimi Gör
          </a>
        </div>
        <p className="text-sm text-green-600 mt-4">
          Çekiliş sonuçları açıklandığında bilgilendirileceksiniz.
        </p>
      </div>
    );
  }
  return (
    <div className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-700">{error}</p>
        </div>
      )}
      <div className="flex items-center justify-between mb-4">
        <label className="block text-sm font-medium text-gray-700">
          Kaç kart almak istiyorsunuz?
        </label>
        <input
          type="number"
          min={1}
          max={maxTicketsPerUser}
          value={numberOfTickets}
          onChange={(e) => setNumberOfTickets(Number(e.target.value))}
          className="w-24 p-2 border border-gray-300 rounded-lg text-center"
        />
      </div>
      <div className="bg-gray-50 p-4 rounded-lg">
        <div className="flex items-center justify-between mb-2">
          <span className="text-gray-600">Bilet Fiyatı:</span>
          <span className="font-semibold">₺{ticketPrice}</span>
        </div>
        <div className="flex items-center justify-between mb-2">
          <span className="text-gray-600">Bilet Sayısı:</span>
          <span className="font-semibold">{numberOfTickets}</span>
        </div>
        <hr className="my-2" />
        <div className="flex items-center justify-between text-lg font-bold">
          <span>Toplam:</span>
          <span className="text-blue-600">₺{totalPrice}</span>
        </div>
      </div>
      <button
        onClick={handlePurchase}
        disabled={loading}
        className={`w-full py-4 px-6 rounded-lg font-bold text-white transition-all duration-300 flex items-center justify-center space-x-2 ${loading ? "bg-gray-400 cursor-not-allowed" : "bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 transform hover:scale-105 shadow-lg"}`}
      >
        {loading ? (
          <>
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
            <span>İşleniyor...</span>
          </>
        ) : (
          <>
            <FiGift />
            <span>Bilet Al - ₺{totalPrice}</span>
          </>
        )}
      </button>
      <div className="text-sm text-gray-600 bg-blue-50 p-4 rounded-lg">
        <h4 className="font-semibold mb-2">Bilgilendirme:</h4>
        <ul className="space-y-1">
          <li>• Her kart, adminin belirlediği kadar numara içerir</li>
          <li>
            • Satın alınan kartlar ve üzerindeki numaralar size özel ve tektir
          </li>
          <li>• 10 adetten fazla kart almak için yönetici onayı gereklidir</li>
          <li>• Cüzdan bakiyenizden ödeme yapılır</li>
        </ul>
      </div>
      {allPurchasedTickets.length > 0 && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h4 className="font-semibold text-gray-800 mb-2">
            Aldığınız Biletler:
          </h4>
          <div className="flex flex-wrap gap-2 justify-center">
            {allPurchasedTickets.map((card, idx) => (
              <span
                key={idx}
                className="bg-green-100 text-green-800 px-3 py-1 rounded-full font-mono font-bold"
              >
                {card.join(", ")}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
