import { useQuery } from '@tanstack/react-query';
import { api } from '../lib/api';
import { 
  Category, 
  CategoryWithParent, 
  CategoriesResponse, 
  CategoryQueryParams,
  ApiResponse 
} from '../types/sanity';

// API functions
const fetchCategories = async (params?: CategoryQueryParams): Promise<Category[]> => {
  const searchParams = new URLSearchParams();
  
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach(v => searchParams.append(key, v.toString()));
        } else {
          searchParams.append(key, value.toString());
        }
      }
    });
  }

  const response = await api.get(`/categories?${searchParams.toString()}`);
  return response.data;
};

const fetchCategoryById = async (id: string): Promise<Category | null> => {
  const response = await api.get(`/categories/${id}`);
  return response.data;
};

const fetchCategoryBySlug = async (slug: string): Promise<Category | null> => {
  const response = await api.get(`/categories/slug/${slug}`);
  return response.data;
};

const fetchRootCategories = async (): Promise<Category[]> => {
  const response = await api.get('/categories/root');
  return response.data;
};

const fetchSubCategories = async (parentId: string): Promise<Category[]> => {
  const response = await api.get(`/categories/${parentId}/subcategories`);
  return response.data;
};

const fetchFeaturedCategories = async (): Promise<Category[]> => {
  const response = await api.get('/categories/featured');
  return response.data;
};

// React Query hooks
export const useCategories = (params?: CategoryQueryParams) => {
  return useQuery<Category[], Error>({
    queryKey: ['categories', params],
    queryFn: () => fetchCategories(params),
    staleTime: 10 * 60 * 1000, // 10 minutes - categories change less frequently
    retry: 2,
    refetchOnWindowFocus: false,
  });
};

export const useCategory = (id: string) => {
  return useQuery<Category | null, Error>({
    queryKey: ['category', id],
    queryFn: () => fetchCategoryById(id),
    staleTime: 10 * 60 * 1000,
    retry: 2,
    enabled: !!id,
  });
};

export const useCategoryBySlug = (slug: string) => {
  return useQuery<Category | null, Error>({
    queryKey: ['category', 'slug', slug],
    queryFn: () => fetchCategoryBySlug(slug),
    staleTime: 10 * 60 * 1000,
    retry: 2,
    enabled: !!slug,
  });
};

export const useRootCategories = () => {
  return useQuery<Category[], Error>({
    queryKey: ['categories', 'root'],
    queryFn: fetchRootCategories,
    staleTime: 15 * 60 * 1000, // 15 minutes for root categories
    retry: 2,
  });
};

export const useSubCategories = (parentId: string) => {
  return useQuery<Category[], Error>({
    queryKey: ['categories', 'subcategories', parentId],
    queryFn: () => fetchSubCategories(parentId),
    staleTime: 10 * 60 * 1000,
    retry: 2,
    enabled: !!parentId,
  });
};

export const useFeaturedCategories = () => {
  return useQuery<Category[], Error>({
    queryKey: ['categories', 'featured'],
    queryFn: fetchFeaturedCategories,
    staleTime: 15 * 60 * 1000,
    retry: 2,
  });
};

// Utility hooks
export const useCategoryFlags = () => {
  const flags = [
    { value: 'featured', label: 'Öne Çıkan' },
    { value: 'seasonal', label: 'Sezonluk' },
    { value: 'promotion', label: 'Promosyon' },
    { value: 'new', label: 'Yeni' },
  ] as const;

  return flags;
};

// Helper functions
export const getLocalizedCategoryText = (
  text: { tr: string; en?: string } | undefined,
  language: 'tr' | 'en' = 'tr'
): string => {
  if (!text) return '';
  return language === 'en' && text.en ? text.en : text.tr;
};

// Helper function to build category hierarchy
export const buildCategoryHierarchy = (categories: Category[]): CategoryWithParent[] => {
  const categoryMap = new Map<string, CategoryWithParent>();
  const rootCategories: CategoryWithParent[] = [];

  // First pass: create all categories
  categories.forEach(category => {
    categoryMap.set(category._id, { ...category });
  });

  // Second pass: build hierarchy
  categories.forEach(category => {
    const categoryWithParent = categoryMap.get(category._id)!;
    
    if (category.parentCategory) {
      const parent = categoryMap.get(category.parentCategory._ref);
      if (parent) {
        categoryWithParent.parentCategory = parent;
      }
    } else {
      rootCategories.push(categoryWithParent);
    }
  });

  return rootCategories;
};

// Helper function to get category breadcrumbs
export const getCategoryBreadcrumbs = (category: Category, allCategories: Category[]): Category[] => {
  const breadcrumbs: Category[] = [];
  let currentCategory: Category | undefined = category;

  while (currentCategory) {
    breadcrumbs.unshift(currentCategory);
    
    if (currentCategory.parentCategory) {
      currentCategory = allCategories.find(cat => cat._id === currentCategory!.parentCategory!._ref);
    } else {
      currentCategory = undefined;
    }
  }

  return breadcrumbs;
};

// Helper function to check if category has subcategories
export const hasSubCategories = (categoryId: string, allCategories: Category[]): boolean => {
  return allCategories.some(category => 
    category.parentCategory && category.parentCategory._ref === categoryId
  );
};

// Helper function to get all subcategories (recursive)
export const getAllSubCategories = (categoryId: string, allCategories: Category[]): Category[] => {
  const directSubCategories = allCategories.filter(category => 
    category.parentCategory && category.parentCategory._ref === categoryId
  );

  const allSubCategories = [...directSubCategories];

  directSubCategories.forEach(subCategory => {
    const nestedSubCategories = getAllSubCategories(subCategory._id, allCategories);
    allSubCategories.push(...nestedSubCategories);
  });

  return allSubCategories;
};

// Helper function to filter categories by flags
export const filterCategoriesByFlags = (categories: Category[], flags: string[]): Category[] => {
  if (flags.length === 0) return categories;
  
  return categories.filter(category => 
    category.flags && category.flags.some(flag => flags.includes(flag))
  );
};

// Helper function to sort categories
export const sortCategories = (
  categories: Category[], 
  sortBy: 'title' | 'createdAt' = 'title',
  language: 'tr' | 'en' = 'tr'
): Category[] => {
  return [...categories].sort((a, b) => {
    if (sortBy === 'title') {
      const titleA = getLocalizedCategoryText(a.title, language).toLowerCase();
      const titleB = getLocalizedCategoryText(b.title, language).toLowerCase();
      return titleA.localeCompare(titleB, 'tr');
    } else {
      return new Date(b._createdAt).getTime() - new Date(a._createdAt).getTime();
    }
  });
};
