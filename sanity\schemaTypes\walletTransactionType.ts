import { CreditCardIcon } from '@sanity/icons';
import { defineField, defineType } from 'sanity';
import { nanoid } from 'nanoid';

export const walletTransactionType = defineType({
  name: 'walletTransaction',
  title: 'Cüzdan İşlemi',
  type: 'document',
  icon: CreditCardIcon,
  groups: [
    { name: 'basic', title: 'Temel Bilgiler' },
    { name: 'details', title: 'İşlem Detayları' },
    { name: 'status', title: 'Durum' },
  ],
  fields: [
    defineField({
      name: 'transactionId',
      title: 'İşlem ID',
      type: 'string',
      group: 'basic',
      readOnly: true,
      initialValue: () => `TXN-${nanoid(10)}`,
      validation: (Rule) => Rule.required().error('İşlem ID zorunludur.'),
    }),
    defineField({
      name: 'user',
      title: 'Kullanıcı',
      type: 'reference',
      to: [{ type: 'user' }],
      group: 'basic',
      validation: (Rule) => Rule.required().error('<PERSON><PERSON><PERSON><PERSON><PERSON> seçimi zorunludur.'),
    }),
    defineField({
      name: 'amount',
      title: 'Tutar',
      type: 'number',
      group: 'details',
      validation: (Rule) => Rule.required().min(0).error('Tutar sıfır veya pozitif olmalıdır.'),
    }),
    defineField({
      name: 'currency',
      title: 'Para Birimi',
      type: 'string',
      group: 'details',
      options: {
        list: [
          { title: 'Türk Lirası (TRY)', value: 'TRY' },
          { title: 'ABD Doları (USD)', value: 'USD' },
          { title: 'Euro (EUR)', value: 'EUR' },
        ],
      },
      initialValue: 'TRY',
      validation: (Rule) => Rule.required().error('Para birimi zorunludur.'),
    }),
    defineField({
      name: 'type',
      title: 'İşlem Türü',
      type: 'string',
      group: 'details',
      options: {
        list: [
          { title: 'Yükleme', value: 'deposit' },
          { title: 'Harcama', value: 'withdrawal' },
          { title: 'İade', value: 'refund' },
          { title: 'Bonus', value: 'bonus' },
          { title: 'Komisyon', value: 'commission' },
          { title: 'Ceza', value: 'penalty' },
        ],
      },
      validation: (Rule) => Rule.required().error('İşlem türü zorunludur.'),
    }),
    defineField({
      name: 'description',
      title: 'İşlem Açıklaması',
      type: 'text',
      group: 'details',
      description: 'İşlemle ilgili detaylı açıklama',
    }),
    defineField({
      name: 'relatedOrder',
      title: 'İlgili Sipariş',
      type: 'reference',
      to: [{ type: 'order' }],
      group: 'details',
      description: 'Bu işlemle ilgili sipariş (varsa)',
    }),
    defineField({
      name: 'relatedTopUpRequest',
      title: 'İlgili Yükleme Talebi',
      type: 'reference',
      to: [{ type: 'walletTopUpRequest' }],
      group: 'details',
      description: 'Bu işlemle ilgili cüzdan yükleme talebi (varsa)',
    }),
    defineField({
      name: 'status',
      title: 'İşlem Durumu',
      type: 'string',
      group: 'status',
      options: {
        list: [
          { title: 'Bekliyor', value: 'pending' },
          { title: 'Tamamlandı', value: 'completed' },
          { title: 'İptal Edildi', value: 'cancelled' },
          { title: 'Başarısız', value: 'failed' },
        ],
      },
      initialValue: 'pending',
      validation: (Rule) => Rule.required().error('İşlem durumu zorunludur.'),
    }),
    defineField({
      name: 'balanceBefore',
      title: 'İşlem Öncesi Bakiye',
      type: 'number',
      group: 'details',
      readOnly: true,
      description: 'İşlem öncesi kullanıcının cüzdan bakiyesi',
    }),
    defineField({
      name: 'balanceAfter',
      title: 'İşlem Sonrası Bakiye',
      type: 'number',
      group: 'details',
      readOnly: true,
      description: 'İşlem sonrası kullanıcının cüzdan bakiyesi',
    }),
    defineField({
      name: 'createdAt',
      title: 'İşlem Tarihi',
      type: 'datetime',
      group: 'basic',
      initialValue: () => new Date().toISOString(),
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'processedAt',
      title: 'İşlenme Tarihi',
      type: 'datetime',
      group: 'status',
      description: 'İşlemin tamamlandığı tarih',
    }),
    defineField({
      name: 'processedBy',
      title: 'İşleyen Kullanıcı',
      type: 'reference',
      to: [{ type: 'user' }],
      group: 'status',
      description: 'İşlemi onaylayan/işleyen admin kullanıcı',
    }),
    defineField({
      name: 'notes',
      title: 'Admin Notları',
      type: 'text',
      group: 'status',
      description: 'Admin tarafından eklenen özel notlar',
    }),
  ],
  preview: {
    select: {
      transactionId: 'transactionId',
      user: 'user.name.tr',
      amount: 'amount',
      currency: 'currency',
      type: 'type',
      status: 'status',
    },
    prepare({ transactionId, user, amount, currency, type, status }) {
      const typeText = getTransactionTypeText(type);
      const statusText = getTransactionStatusText(status);
      
      return {
        title: transactionId || 'İsimsiz İşlem',
        subtitle: `${user || 'Kullanıcı Yok'} - ${typeText} - ${amount || 0} ${currency || 'TRY'} - ${statusText}`,
        media: CreditCardIcon,
      };
    },
  },
});

// Helper functions for preview
function getTransactionTypeText(type: string): string {
  const typeMap: Record<string, string> = {
    deposit: 'Yükleme',
    withdrawal: 'Harcama',
    refund: 'İade',
    bonus: 'Bonus',
    commission: 'Komisyon',
    penalty: 'Ceza',
  };
  return typeMap[type] || type || 'Tür Yok';
}

function getTransactionStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    pending: 'Bekliyor',
    completed: 'Tamamlandı',
    cancelled: 'İptal',
    failed: 'Başarısız',
  };
  return statusMap[status] || status || 'Durum Yok';
}
