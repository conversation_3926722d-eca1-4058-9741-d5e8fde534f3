import Image from 'next/image';
import Link from 'next/link';
import { getActiveGiveaways } from '@/sanity/lib/giveaways/getGiveaways';
// import { urlForImage } from '@/sanity/lib/image';
import { FiGift, FiUsers, FiClock, FiCalendar } from 'react-icons/fi';

export default async function GiveawaysPage() {
  const giveaways = await getActiveGiveaways();

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="container mx-auto p-4">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-gray-800 mb-4">Aktif Çekilişler</h1>
        <p className="text-lg text-gray-600"><PERSON><PERSON> ö<PERSON>üller kazanma şansını kaçırmayın!</p>
      </div>

      {giveaways.length === 0 ? (
        <div className="text-center py-12">
          <FiGift size={64} className="mx-auto text-gray-400 mb-4" />
          <h2 className="text-2xl font-semibold text-gray-600 mb-2">
            Şu anda aktif çekiliş bulunmamaktadır
          </h2>
          <p className="text-gray-500">
            Yeni çekilişler için takipte kalın!
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {giveaways.map((giveaway) => (
            <Link 
              href={`/giveaways/${giveaway._id}`} 
              key={giveaway._id}
              className="group"
            >
              <div className="bg-white border rounded-lg shadow-lg overflow-hidden cursor-pointer hover:shadow-xl transition-all duration-300 transform group-hover:scale-105">
                {/* Giveaway Image */}
                <div className="relative h-48 bg-gray-100 flex items-center justify-center">
                  {giveaway.image ? (
                    <Image
                      src={giveaway.image.asset?.url || 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=500&h=300&fit=crop'}
                      alt={giveaway.title || 'Çekiliş Görseli'}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="flex flex-col items-center justify-center text-gray-400">
                      <FiGift size={48} />
                      <span className="mt-2 text-sm">Çekiliş Görseli</span>
                    </div>
                  )}
                  
                  {/* Status Badge */}
                  <div className="absolute top-3 right-3">
                    <span className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                      Aktif
                    </span>
                  </div>
                </div>

                {/* Giveaway Details */}
                <div className="p-6">
                  <h3 className="text-xl font-semibold mb-2 text-gray-800 group-hover:text-blue-600 transition-colors">
                    {giveaway.title}
                  </h3>
                  
                  {giveaway.description && (
                    <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                      {giveaway.description}
                    </p>
                  )}

                  {/* Giveaway Stats */}
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center text-green-600">
                      <FiGift className="mr-2 flex-shrink-0" size={16} />
                      <span className="text-sm font-medium">
                        Bilet Fiyatı: ₺{giveaway.ticketPrice || 0}
                      </span>
                    </div>
                    
                    <div className="flex items-center text-blue-600">
                      <FiUsers className="mr-2 flex-shrink-0" size={16} />
                      <span className="text-sm font-medium">
                        Satılan: {giveaway.ticketsSold || 0}
                        {giveaway.maxTickets && ` / ${giveaway.maxTickets}`}
                      </span>
                    </div>
                    
                    {giveaway.startDate && (
                      <div className="flex items-center text-purple-600">
                        <FiCalendar className="mr-2 flex-shrink-0" size={16} />
                        <span className="text-sm font-medium">
                          Başlangıç: {formatDate(giveaway.startDate)}
                        </span>
                      </div>
                    )}
                    
                    {giveaway.endDate && (
                      <div className="flex items-center text-orange-600">
                        <FiClock className="mr-2 flex-shrink-0" size={16} />
                        <span className="text-sm font-medium">
                          Bitiş: {formatDate(giveaway.endDate)}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Action Button */}
                  <div className="mt-4">
                    <span className="inline-block w-full bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white font-bold py-2 px-4 rounded-lg transition-all duration-300 text-center text-sm">
                      Çekilişe Katıl
                    </span>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      )}

      {/* Call to Action */}
      {giveaways.length > 0 && (
        <div className="text-center mt-12 p-8 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">
            Daha Fazla Çekiliş İçin Takipte Kalın!
          </h2>
          <p className="text-gray-600 mb-6">
            Yeni çekilişlerden haberdar olmak için bültenimize abone olun.
          </p>
          <button className="bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white font-bold py-3 px-8 rounded-lg transition-all duration-300 transform hover:scale-105">
            Bültene Abone Ol
          </button>
        </div>
      )}
    </div>
  );
}
