import { Webhook } from 'svix';
import { WebhookEvent } from '@clerk/nextjs/server';
import { writeClient } from '@/sanity/lib/client';

export async function POST(req: Request) {
  console.log('🔔 Clerk webhook received');

  const WEBHOOK_SECRET = process.env.CLERK_WEBHOOK_SECRET;

  if (!WEBHOOK_SECRET) {
    console.error('❌ CLERK_WEBHOOK_SECRET not found');
    throw new Error('Please add CLERK_WEBHOOK_SECRET from Clerk Dashboard to .env or .env.local');
  }

  const svix_id = req.headers.get("svix-id");
  const svix_timestamp = req.headers.get("svix-timestamp");
  const svix_signature = req.headers.get("svix-signature");

  if (!svix_id || !svix_timestamp || !svix_signature) {
    return new Response('Error occured -- no svix headers', {
      status: 400
    });
  }

  const payload = await req.json();
  const body = JSON.stringify(payload);

  console.log('📦 Webhook payload:', JSON.stringify(payload, null, 2));

  const wh = new Webhook(WEBHOOK_SECRET);

  let evt: WebhookEvent;

  try {
    evt = wh.verify(body, {
      "svix-id": svix_id,
      "svix-timestamp": svix_timestamp,
      "svix-signature": svix_signature,
    }) as WebhookEvent;
    console.log('✅ Webhook verified successfully');
  } catch (err) {
    console.error('❌ Error verifying webhook:', err);
    return new Response('Error occured', {
      status: 400
    });
  }

  const { id } = evt.data;
  const eventType = evt.type;

  console.log(`🎯 Event type: ${eventType}, User ID: ${id}`);

  if (eventType === 'user.created') {
    console.log('👤 Processing user.created event');

    if (!id) {
      console.error('❌ No user ID in event data');
      return new Response('Error occured -- no user id', {
        status: 400
      });
    }

    const { email_addresses, image_url, first_name, last_name } = evt.data;
    console.log('📧 User data:', {
      id,
      email: email_addresses?.[0]?.email_address,
      name: `${first_name || ''} ${last_name || ''}`.trim(),
      imageUrl: image_url
    });

    const user = {
      _type: 'user',
      _id: id,
      clerkId: id,
      name: `${first_name || ''} ${last_name || ''}`.trim(),
      email: email_addresses?.[0]?.email_address,
      isAdminApproved: false, // Default to not approved
      imageUrl: image_url,
    };

    console.log('💾 Creating user in Sanity:', user);

    try {
      const result = await writeClient.createIfNotExists(user);
      console.log('✅ User created/updated in Sanity:', result);
      return new Response('User created in Sanity', { status: 201 });
    } catch (error) {
      console.error('❌ Error creating user in Sanity:', error);
      return new Response('Error creating user', { status: 500 });
    }
  }

  console.log(`ℹ️ Event type ${eventType} not handled`);
  return new Response('', { status: 200 });
}