import { getUserByClerkId } from '@/sanity/lib/users/getUser'
import { client } from '@/sanity/lib/client'
import { sanityFetch } from '@/sanity/lib/live'

// Mock the dependencies
jest.mock('@/sanity/lib/live')
jest.mock('@/sanity/lib/client')

const mockSanityFetch = sanityFetch as jest.MockedFunction<typeof sanityFetch>
const mockClient = client as jest.Mocked<typeof client>

describe('getUserByClerkId', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should return null if clerkUserId is not provided', async () => {
    const result = await getUserByClerkId({ id: '', email: '<EMAIL>' })
    expect(result).toBeNull()
  })

  it('should return existing user if found in Sanity', async () => {
    const mockUser = {
      _id: 'user-123',
      clerkId: 'clerk-123',
      name: 'Test User',
      email: '<EMAIL>',
      isAdminApproved: false
    }

    mockSanityFetch.mockResolvedValue({ data: mockUser })

    const result = await getUserByClerkId({
      id: 'clerk-123',
      email: '<EMAIL>',
      name: 'Test User'
    })

    expect(result).toEqual(mockUser)
    expect(mockSanityFetch).toHaveBeenCalledWith({
      query: expect.stringContaining('*[_type == "user" && clerkId == $clerkUserId][0]'),
      params: { clerkUserId: 'clerk-123' }
    })
  })

  it('should update user info if name or email changed', async () => {
    const mockUser = {
      _id: 'user-123',
      clerkId: 'clerk-123',
      name: 'Old Name',
      email: '<EMAIL>',
      isAdminApproved: false
    }

    mockSanityFetch.mockResolvedValue({ data: mockUser })
    const mockPatch = {
      set: jest.fn().mockReturnValue({
        commit: jest.fn().mockResolvedValue(undefined)
      })
    }
    mockClient.patch.mockReturnValue(mockPatch as any)

    const result = await getUserByClerkId({
      id: 'clerk-123',
      email: '<EMAIL>',
      name: 'New Name'
    })

    expect(mockClient.patch).toHaveBeenCalledWith('user-123')
    expect(mockPatch.set).toHaveBeenCalledWith({
      name: 'New Name',
      email: '<EMAIL>'
    })
    expect(result?.name).toBe('New Name')
    expect(result?.email).toBe('<EMAIL>')
  })

  it('should create new user if not found in Sanity', async () => {
    const newUser = {
      _id: 'user-456',
      _type: 'user',
      clerkId: 'clerk-456',
      name: 'New User',
      email: '<EMAIL>',
      isAdminApproved: false
    }

    mockSanityFetch.mockResolvedValue({ data: null })
    mockClient.create.mockResolvedValue(newUser as any)

    const result = await getUserByClerkId({
      id: 'clerk-456',
      email: '<EMAIL>',
      name: 'New User'
    })

    expect(mockClient.create).toHaveBeenCalledWith({
      _type: 'user',
      clerkId: 'clerk-456',
      name: 'New User',
      email: '<EMAIL>',
      isAdminApproved: false
    })
    expect(result).toEqual(newUser)
  })

  it('should handle errors gracefully', async () => {
    mockSanityFetch.mockRejectedValue(new Error('Sanity error'))

    const result = await getUserByClerkId({
      id: 'clerk-123',
      email: '<EMAIL>',
      name: 'Test User'
    })

    expect(result).toBeNull()
  })
})
