# Sanity API Token Permissions Düzeltme Rehberi

## Problem
Sanity API token'ınız sadece "read" yetkisine sahip. Teklif verme ve bilet satın alma için "update" yetkisi gerekiyor.

## Hızlı Çözüm

### 1. Sanity Management Console'a Git
🔗 **Link:** https://www.sanity.io/manage/personal/project/mz841rpk

### 2. API Tokens Sayfasına Git
- Sol menüden **"API"** seçin
- **"Tokens"** sekmesine tıklayın

### 3. Mevcut Token'ı Güncelle veya Yeni Token Oluştur

#### Seçenek A: Mevcut Token'ı Güncelle
1. Mevcut token'ınızı bulun
2. **"Edit"** butonuna tıklayın
3. Permissions'ı **"Editor"** veya **"Admin"** yapın
4. **"Save"** butonuna tıklayın

#### Seçenek B: Yeni Token Oluştur
1. **"Add API Token"** butonuna tıklayın
2. Token adı: `Production Write Token`
3. Permissions: **"Editor"** seçin
4. Dataset: **"production"** seçin
5. **"Add token"** butonuna tıklayın
6. Token'ı kopyalayın

### 4. .env.local Dosyasını Güncelle
```env
SANITY_API_TOKEN=your_new_token_here
```

### 5. Development Server'ı Yeniden Başlat
```bash
npm run dev
```

## Permissions Açıklaması

| Permission | Açıklama | Gerekli Mi? |
|------------|----------|-------------|
| **Viewer** | Sadece okuma | ❌ Yetersiz |
| **Editor** | Okuma + Yazma + Güncelleme | ✅ Yeterli |
| **Admin** | Tüm yetkiler | ✅ Yeterli |

## Test Etme

Token'ı güncelledikten sonra test edin:

1. **Permissions Test:**
   ```
   http://localhost:3000/api/test-sanity-permissions
   ```

2. **Çekiliş Bilet Satın Alma:**
   - Çekiliş sayfasına gidin
   - Bilet satın almayı deneyin

3. **Açık Artırma Teklif:**
   - Açık artırma sayfasına gidin
   - Teklif vermeyi deneyin

## Güvenlik Notları

- ⚠️ **Token'ı güvenli tutun** - .env.local dosyasında saklayın
- ⚠️ **Token'ı GitHub'a commit etmeyin**
- ✅ **Production için ayrı token kullanın**

## Sorun Devam Ederse

Eğer token'ı güncelledikten sonra hala sorun yaşıyorsanız:

1. Browser cache'ini temizleyin
2. Development server'ı yeniden başlatın
3. Token'ın doğru kopyalandığından emin olun
4. Sanity console'da token'ın aktif olduğunu kontrol edin

## Hızlı Test Komutu

```bash
# Terminal'de test edin
curl -X GET "http://localhost:3000/api/test-sanity-permissions"
```

Başarılı olursa şu mesajı görmelisiniz:
```json
{
  "success": true,
  "message": "Sanity permissions test successful"
}
```
