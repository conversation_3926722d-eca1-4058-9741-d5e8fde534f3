/**
 * Integration test for Sanity connection and user operations
 */

import { client } from '@/sanity/lib/client'

// Mock Sanity client for testing
jest.mock('@/sanity/lib/client', () => ({
  client: {
    createIfNotExists: jest.fn(),
    fetch: jest.fn(),
    create: jest.fn(),
    patch: jest.fn(() => ({
      set: jest.fn(() => ({
        commit: jest.fn(),
      })),
    })),
  },
}))

const mockClient = client as jest.Mocked<typeof client>

describe('Sanity Connection Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('User creation', () => {
    it('should create user with correct structure', async () => {
      const userData = {
        _type: 'user',
        _id: 'user_test123',
        clerkId: 'user_test123',
        name: 'Test User',
        email: '<EMAIL>',
        isAdminApproved: false,
        imageUrl: 'https://example.com/image.jpg'
      }

      mockClient.createIfNotExists.mockResolvedValue(userData as any)

      const result = await client.createIfNotExists(userData)

      expect(mockClient.createIfNotExists).toHaveBeenCalledWith(userData)
      expect(result).toEqual(userData)
    })

    it('should handle user creation errors', async () => {
      const userData = {
        _type: 'user',
        _id: 'user_test123',
        clerkId: 'user_test123',
        name: 'Test User',
        email: '<EMAIL>',
        isAdminApproved: false
      }

      const error = new Error('Sanity connection failed')
      mockClient.createIfNotExists.mockRejectedValue(error)

      await expect(client.createIfNotExists(userData)).rejects.toThrow('Sanity connection failed')
    })

    it('should validate required user fields', () => {
      const validUser = {
        _type: 'user',
        _id: 'user_test123',
        clerkId: 'user_test123',
        name: 'Test User',
        email: '<EMAIL>',
        isAdminApproved: false
      }

      const invalidUsers = [
        { ...validUser, _type: undefined }, // missing _type
        { ...validUser, _id: undefined }, // missing _id
        { ...validUser, clerkId: undefined }, // missing clerkId
        { ...validUser, email: undefined }, // missing email
      ]

      // Valid user should have all required fields
      expect(validUser._type).toBe('user')
      expect(validUser._id).toBeTruthy()
      expect(validUser.clerkId).toBeTruthy()
      expect(validUser.email).toBeTruthy()

      // Invalid users should be missing required fields
      invalidUsers.forEach(user => {
        const hasAllRequired = user._type && user._id && user.clerkId && user.email
        expect(hasAllRequired).toBeFalsy()
      })
    })
  })

  describe('User queries', () => {
    it('should fetch user by clerkId', async () => {
      const mockUser = {
        _id: 'user_123',
        clerkId: 'clerk_123',
        name: 'Test User',
        email: '<EMAIL>',
        isAdminApproved: true
      }

      mockClient.fetch.mockResolvedValue([mockUser])

      const query = '*[_type == "user" && clerkId == $clerkId][0]'
      const params = { clerkId: 'clerk_123' }

      const result = await client.fetch(query, params)

      expect(mockClient.fetch).toHaveBeenCalledWith(query, params)
      expect(result).toEqual([mockUser])
    })

    it('should handle empty query results', async () => {
      mockClient.fetch.mockResolvedValue([])

      const query = '*[_type == "user" && clerkId == $clerkId][0]'
      const params = { clerkId: 'nonexistent' }

      const result = await client.fetch(query, params)

      expect(result).toEqual([])
    })
  })

  describe('User updates', () => {
    it('should update user information', async () => {
      const mockPatch = {
        set: jest.fn().mockReturnValue({
          commit: jest.fn().mockResolvedValue(undefined)
        })
      }

      mockClient.patch.mockReturnValue(mockPatch as any)

      const userId = 'user_123'
      const updateData = { name: 'Updated Name', email: '<EMAIL>' }

      await client.patch(userId).set(updateData).commit()

      expect(mockClient.patch).toHaveBeenCalledWith(userId)
      expect(mockPatch.set).toHaveBeenCalledWith(updateData)
      expect(mockPatch.set().commit).toHaveBeenCalled()
    })
  })

  describe('Environment validation', () => {
    it('should validate required environment variables', () => {
      const requiredEnvVars = [
        'NEXT_PUBLIC_SANITY_PROJECT_ID',
        'NEXT_PUBLIC_SANITY_DATASET',
        'SANITY_API_TOKEN'
      ]

      requiredEnvVars.forEach(envVar => {
        // In test environment, these should be mocked
        expect(process.env[envVar]).toBeDefined()
      })
    })

    it('should validate Sanity client configuration', () => {
      // Test that client is properly configured
      expect(client).toBeDefined()
      expect(typeof client.createIfNotExists).toBe('function')
      expect(typeof client.fetch).toBe('function')
      expect(typeof client.patch).toBe('function')
    })
  })
})
