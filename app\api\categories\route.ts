import { NextRequest, NextResponse } from 'next/server';
import { client } from '@/sanity/lib/client';
import { Category, CategoryQueryParams } from '@/types/sanity';

// Enhanced GROQ query for categories with multilingual support
const CATEGORIES_QUERY = `
  *[_type == "category" 
    ${`&& defined(title.tr)`}
    $parentFilter
    $flagsFilter
  ] | order(title.tr asc) {
    _id,
    _type,
    _createdAt,
    _updatedAt,
    _rev,
    title,
    slug,
    image {
      asset->{
        _id,
        url
      },
      alt,
      hotspot,
      crop
    },
    description,
    parentCategory->{
      _id,
      title,
      slug
    },
    tags,
    flags,
    seoTitle,
    seoDescription
  }
`;

const ROOT_CATEGORIES_QUERY = `
  *[_type == "category" && !defined(parentCategory) && defined(title.tr)] | order(title.tr asc) {
    _id,
    _type,
    _createdAt,
    _updatedAt,
    _rev,
    title,
    slug,
    image {
      asset->{
        _id,
        url
      },
      alt,
      hotspot,
      crop
    },
    description,
    tags,
    flags,
    seoTitle,
    seoDescription
  }
`;

const FEATURED_CATEGORIES_QUERY = `
  *[_type == "category" && "featured" in flags && defined(title.tr)] | order(title.tr asc) {
    _id,
    _type,
    _createdAt,
    _updatedAt,
    _rev,
    title,
    slug,
    image {
      asset->{
        _id,
        url
      },
      alt,
      hotspot,
      crop
    },
    description,
    parentCategory->{
      _id,
      title,
      slug
    },
    tags,
    flags,
    seoTitle,
    seoDescription
  }
`;

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Check for special endpoints
    const type = searchParams.get('type');
    
    if (type === 'root') {
      const categories = await client.fetch<Category[]>(ROOT_CATEGORIES_QUERY);
      return NextResponse.json({
        data: categories,
        success: true,
      });
    }
    
    if (type === 'featured') {
      const categories = await client.fetch<Category[]>(FEATURED_CATEGORIES_QUERY);
      return NextResponse.json({
        data: categories,
        success: true,
      });
    }

    // Parse query parameters for regular category fetching
    const params: CategoryQueryParams = {
      parentCategory: searchParams.get('parentCategory') || undefined,
      flags: searchParams.getAll('flags'),
      language: (searchParams.get('language') as 'tr' | 'en') || 'tr',
    };

    // Build dynamic filters
    let parentFilter = '';
    let flagsFilter = '';

    if (params.parentCategory) {
      parentFilter = `&& parentCategory->slug.current == "${params.parentCategory}"`;
    }

    if (params.flags && params.flags.length > 0) {
      const flagConditions = params.flags.map(flag => `"${flag}" in flags`).join(' || ');
      flagsFilter = `&& (${flagConditions})`;
    }

    // Build final query
    const finalQuery = CATEGORIES_QUERY
      .replace('$parentFilter', parentFilter)
      .replace('$flagsFilter', flagsFilter);

    // Execute query
    const categories = await client.fetch<Category[]>(finalQuery);

    // Return response
    return NextResponse.json({
      data: categories,
      total: categories.length,
      success: true,
    });

  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      { 
        error: { 
          message: 'Failed to fetch categories',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        success: false 
      },
      { status: 500 }
    );
  }
}
