import React from "react";
import type { Metadata } from "next";
import { draftMode } from "next/headers";
import { <PERSON><PERSON>rovider } from "@clerk/nextjs";

import "../globals.css";

// Suppress React warnings for Sanity UI
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  const originalError = console.error;
  console.error = (...args) => {
    const message = args[0];
    if (
      typeof message === 'string' &&
      (message.includes('disableTransition') ||
       message.includes('React does not recognize'))
    ) {
      return;
    }
    originalError.apply(console, args);
  };
}

import Header from "@/components/Header";
import Footer from "@/components/stabilPage/footer";
import { DisableDraftMode } from "@/components/DisableDraftMode";
import ErrorSuppression from "@/components/ErrorSuppression";

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const { isEnabled } = await draftMode();

  return (
    <ClerkProvider>
      <html lang="en">
        <body>
          <ErrorSuppression />
          {isEnabled && <DisableDraftMode />}
          <main>
            <Header />
            {children}
          </main>
          <Footer />
        </body>
      </html>
    </ClerkProvider>
  );
}
