/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "cdn.sanity.io",
      },
      {
        protocol: "https",
        hostname: "via.placeholder.com",
      },
    ],
  },
  transpilePackages: [
    "framer-motion",
    "@sanity/ui",
    "@sanity/visual-editing"
  ],
  webpack: (config, { dev, isServer }) => {
    // Suppress warnings in development
    if (dev && !isServer) {
      config.stats = {
        ...config.stats,
        warningsFilter: [
          /React does not recognize.*disableTransition/,
          /styled-components/,
        ],
      };

      // Fix chunk loading issues with more aggressive splitting
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          minSize: 20000,
          maxSize: 244000,
          cacheGroups: {
            default: {
              minChunks: 2,
              priority: -20,
              reuseExistingChunk: true,
            },
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              priority: -10,
              chunks: 'all',
            },
            sanity: {
              test: /[\\/]node_modules[\\/](@sanity|sanity)[\\/]/,
              name: 'sanity',
              chunks: 'all',
              priority: 30,
              enforce: true,
            },
            react: {
              test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
              name: 'react',
              chunks: 'all',
              priority: 20,
            },
          },
        },
      };
    }

    // Resolve fallbacks for better compatibility
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false,
    };

    return config;
  },
  /* config options here */
};

module.exports = nextConfig;