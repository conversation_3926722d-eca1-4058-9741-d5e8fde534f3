/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "cdn.sanity.io",
      },
    ],
  },
  transpilePackages: [
    "framer-motion",
    "@sanity/ui",
    "@sanity/visual-editing"
  ],
  webpack: (config, { dev, isServer }) => {
    // Suppress warnings in development
    if (dev && !isServer) {
      config.stats = {
        ...config.stats,
        warningsFilter: [
          /React does not recognize.*disableTransition/,
          /styled-components/,
        ],
      };

      // Fix chunk loading issues
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          ...config.optimization.splitChunks,
          cacheGroups: {
            ...config.optimization.splitChunks?.cacheGroups,
            sanity: {
              test: /[\\/]node_modules[\\/](@sanity|sanity)[\\/]/,
              name: 'sanity',
              chunks: 'all',
              priority: 30,
            },
          },
        },
      };
    }
    return config;
  },
  /* config options here */
};

module.exports = nextConfig;