// lib/client.ts
import { createClient } from 'next-sanity';
import imageUrlBuilder from '@sanity/image-url';
import { apiVersion, dataset, projectId } from '../env';

export const client = createClient({
  projectId,
  dataset,
  apiVersion,
  useCdn: true, // Public read için CD<PERSON>ull<PERSON>
  perspective: 'published',
  ignoreBrowserTokenWarning: true,
  stega: {
    studioUrl: '/Studio',
  },
});

// Write operations için ayrı client
export const writeClient = createClient({
  projectId,
  dataset,
  apiVersion,
  useCdn: false,
  token: process.env.SANITY_API_TOKEN,
  perspective: 'published',
  ignoreBrowserTokenWarning: true,
});

const builder = imageUrlBuilder(client);

export function urlFor(source: any) {
  return builder.image(source).url();
}
