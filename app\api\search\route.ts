import { searchProductsByName } from "@/sanity/lib/products/searchProductsByName";
import { NextResponse } from "next/server";

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const query = searchParams.get("query");

  if (!query) {
    return NextResponse.json({ products: [] });
  }

  try {
    const products = await searchProductsByName(query);
    return NextResponse.json({ products });
  } catch (error) {
    console.error("Search error:", error);
    return NextResponse.json({ products: [] });
  }
} 