import { NextRequest, NextResponse } from 'next/server'
import { writeClient } from '@/sanity/lib/client'

export async function POST(request: NextRequest) {
  try {
    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'userId is required'
      }, { status: 400 });
    }

    console.log(`🔄 Approving user ${userId}`);

    // Update user approval status
    const result = await writeClient
      .patch(userId)
      .set({ isAdminApproved: true })
      .commit();

    console.log('✅ User approved:', result);

    return NextResponse.json({
      success: true,
      message: `User ${userId} approved successfully`,
      data: result
    });

  } catch (error) {
    console.error('❌ Error approving user:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get all users with their approval status
    const users = await writeClient.fetch(`
      *[_type == "user"] | order(_createdAt desc) {
        _id,
        name,
        email,
        clerkId,
        isAdminApproved,
        walletBalance,
        _createdAt
      }
    `);

    return NextResponse.json({
      success: true,
      data: users
    });

  } catch (error) {
    console.error('❌ Error fetching users:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
