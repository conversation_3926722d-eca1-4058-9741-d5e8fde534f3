# Sanity API Token Permissions Fix

## Problem
The current Sanity API token doesn't have sufficient permissions to create or update user documents. This causes "Insufficient permissions" errors when users try to log in.

## Solution

### Step 1: Access Sanity Management Console
1. Go to [Sanity Management Console](https://www.sanity.io/manage)
2. Select your project: `mz841rpk`
3. Go to **API** section
4. Click on **Tokens**

### Step 2: Create a New Token with Proper Permissions
1. Click **Add API Token**
2. Give it a name like: `Production Write Token`
3. Set permissions to **Editor** or **Admin** (not Viewer)
4. For dataset, select: `production`
5. Copy the generated token

### Step 3: Update Environment Variables
Replace the current `SANITY_API_TOKEN` in your `.env.local` file with the new token:

```env
SANITY_API_TOKEN=your_new_token_here
```

### Step 4: Alternative - Update Existing Token Permissions
If you want to keep the existing token:
1. Find your current token in the Sanity console
2. Edit the token permissions
3. Change from **Viewer** to **Editor** or **Admin**
4. Save changes

### Step 5: Verify Permissions
After updating the token, test it by visiting:
```
http://localhost:3000/api/test-sanity-permissions
```

You should see a success message if permissions are correct.

## Current Fallback Implementation
I've implemented a fallback mechanism that:
- ✅ Continues to work even with limited permissions
- ✅ Creates temporary user objects for the session
- ✅ Logs warnings instead of crashing
- ✅ Allows users to continue using the app

## Required Permissions for Full Functionality
Your Sanity API token needs these permissions:
- **Read**: Query user data
- **Create**: Create new user documents
- **Update**: Update existing user information
- **Delete**: Remove test data (optional)

## Testing
After fixing permissions, test these features:
1. User login/registration
2. Wallet functionality
3. Order creation
4. User profile updates

## Notes
- The app will continue to work with the fallback mechanism
- However, user data won't be persisted to Sanity without proper permissions
- Fix the token permissions for full functionality
