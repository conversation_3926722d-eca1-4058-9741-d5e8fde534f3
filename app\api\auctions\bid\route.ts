import { client } from '@/sanity/lib/client';
import { getAuctionById } from '@/sanity/lib/auctions/getAuctions';
import { currentUser } from '@clerk/nextjs/server';
import type { NextRequest } from 'next/server';
import { getUserByClerkId } from '@/sanity/lib/users/getUser';

export async function POST(req: NextRequest) {
  const { auctionId, bidAmount } = await req.json();
  const clerkUser = await currentUser();

  if (!clerkUser || !clerkUser.id) {
    return new Response(JSON.stringify({ error: 'Yetkisiz: Kullanıcı kimliği bulunamadı.' }), { status: 401 });
  }

  const userEmail = clerkUser.emailAddresses[0]?.emailAddress;
  if (!userEmail) {
    return new Response(JSON.stringify({ error: 'Kullanıcı e-posta adresi bulunamadı.' }), { status: 400 });
  }

  const userName = `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim() || userEmail;

  const user = await getUserByClerkId({
    id: clerkUser.id,
    email: userEmail,
    name: userName
  });
  if (!user || !user.isAdminApproved) {
    return new Response(JSON.stringify({ error: 'Teklif vermek için yönetici onayı gerekiyor.' }), { status: 403 });
  }

  // auctionId is _id, but getAuctionById expects the 'id' field
  // Let's try to get auction by _id first
  let auction = null;
  try {
    auction = await client.fetch(
      `*[_type == "auction" && _id == $auctionId][0]{ _id, id, name, image, currentBid, endTime, status, bidIncrementAmount }`,
      { auctionId }
    );
  } catch (error) {
    console.error('Error fetching auction by _id:', error);
  }

  // If not found by _id, try by id field
  if (!auction) {
    auction = await getAuctionById(auctionId);
  }
  if (!auction) {
    return new Response(JSON.stringify({ error: 'Açık artırma bulunamadı.' }), { status: 404 });
  }
  if (auction.status !== 'active') {
    return new Response(JSON.stringify({ error: 'Açık artırma aktif değil.' }), { status: 400 });
  }

  const currentBid = auction.currentBid || 0;
  const startingBid = auction.startingBid || 0;
  const bidIncrementAmount = auction.bidIncrementAmount || 1;

  // Determine the base amount for validation. If there are bids, it's the current bid. If not, it's the starting bid.
  const baseBid = currentBid > 0 ? currentBid : startingBid;

  if (bidAmount <= baseBid) {
    const message = currentBid > 0
      ? `Teklifiniz mevcut tekliften (₺${currentBid}) yüksek olmalıdır.`
      : `Teklifiniz başlangıç fiyatından (₺${startingBid}) yüksek olmalıdır.`;
    return new Response(JSON.stringify({ error: message }), { status: 400 });
  }

  // The new bid must be at least the base bid plus the increment amount.
  if (bidAmount < baseBid + bidIncrementAmount && currentBid > 0) { // Only enforce increment on existing bids
    return new Response(JSON.stringify({ error: `Teklifiniz en az ₺${bidIncrementAmount} artışla (${baseBid + bidIncrementAmount}) olmalıdır.` }), { status: 400 });
  }

  try {
    const transaction = client.transaction();

    transaction.patch(auction._id, (patch) =>
      patch
        .set({ currentBid: bidAmount })
        .setIfMissing({ bidHistory: [] })
        .append('bidHistory', [
          {
            _key: `${Date.now()}-${user._id}`,
            bidAmount: bidAmount,
            bidTime: new Date().toISOString(),
            bidder: { _ref: user._id, _type: 'reference' },
          },
        ])
    );
    
    // Optionally, update the highest bidder if needed, though bidHistory is more comprehensive
    // transaction.patch(auction._id, (patch) =>
    //   patch.set({ highestBidder: { _ref: user._id, _type: 'reference' }})
    // );

    await transaction.commit();

    return new Response(JSON.stringify({ success: true, newBid: bidAmount }), { status: 200 });
  } catch (error) {
    console.error('Teklif kaydedilirken hata oluştu:', error);
    return new Response(JSON.stringify({ error: 'Teklif kaydedilemedi.' }), { status: 500 });
  }
} 