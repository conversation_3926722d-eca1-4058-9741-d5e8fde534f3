import { NextRequest, NextResponse } from 'next/server';
import { client } from '@/sanity/lib/client';
import { Product, ProductQueryParams } from '@/types/sanity';

// Enhanced GROQ query for products with multilingual support
const PRODUCTS_QUERY = `
  *[_type == "product" 
    ${`&& defined(name.tr)`}
    $categoryFilter
    $flagsFilter
    $priceFilter
    $stockFilter
    $searchFilter
  ] | order($sortBy $sortOrder) [$start...$end] {
    _id,
    _type,
    _createdAt,
    _updatedAt,
    _rev,
    id,
    name,
    slug,
    image {
      asset->{
        _id,
        url
      },
      alt,
      hotspot,
      crop
    },
    description,
    category->{
      _id,
      title,
      slug
    },
    tags,
    price,
    currency,
    discount,
    sku,
    stock,
    isAvailable,
    variants,
    flags,
    seoTitle,
    seoDescription
  }
`;

const COUNT_QUERY = `
  count(*[_type == "product" 
    ${`&& defined(name.tr)`}
    $categoryFilter
    $flagsFilter
    $priceFilter
    $stockFilter
    $searchFilter
  ])
`;

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse query parameters
    const params: ProductQueryParams = {
      category: searchParams.get('category') || undefined,
      tags: searchParams.getAll('tags'),
      flags: searchParams.getAll('flags'),
      minPrice: searchParams.get('minPrice') ? Number(searchParams.get('minPrice')) : undefined,
      maxPrice: searchParams.get('maxPrice') ? Number(searchParams.get('maxPrice')) : undefined,
      inStock: searchParams.get('inStock') === 'true' ? true : undefined,
      search: searchParams.get('search') || undefined,
      page: Number(searchParams.get('page')) || 1,
      limit: Number(searchParams.get('limit')) || 20,
      sortBy: (searchParams.get('sortBy') as any) || 'name.tr',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'asc',
      language: (searchParams.get('language') as 'tr' | 'en') || 'tr',
    };

    // Build dynamic filters
    let categoryFilter = '';
    let flagsFilter = '';
    let priceFilter = '';
    let stockFilter = '';
    let searchFilter = '';

    if (params.category) {
      categoryFilter = `&& category->slug.current == "${params.category}"`;
    }

    if (params.flags && params.flags.length > 0) {
      const flagConditions = params.flags.map(flag => `"${flag}" in flags`).join(' || ');
      flagsFilter = `&& (${flagConditions})`;
    }

    if (params.minPrice !== undefined || params.maxPrice !== undefined) {
      const priceConditions = [];
      if (params.minPrice !== undefined) priceConditions.push(`price >= ${params.minPrice}`);
      if (params.maxPrice !== undefined) priceConditions.push(`price <= ${params.maxPrice}`);
      priceFilter = `&& (${priceConditions.join(' && ')})`;
    }

    if (params.inStock) {
      stockFilter = `&& isAvailable == true && stock > 0`;
    }

    if (params.search) {
      const searchTerm = params.search.toLowerCase();
      searchFilter = `&& (
        name.tr match "*${searchTerm}*" ||
        name.en match "*${searchTerm}*" ||
        description.tr match "*${searchTerm}*" ||
        description.en match "*${searchTerm}*" ||
        "${searchTerm}" in tags
      )`;
    }

    // Calculate pagination
    const start = (params.page - 1) * params.limit;
    const end = start + params.limit;

    // Build final queries
    const finalProductsQuery = PRODUCTS_QUERY
      .replace('$categoryFilter', categoryFilter)
      .replace('$flagsFilter', flagsFilter)
      .replace('$priceFilter', priceFilter)
      .replace('$stockFilter', stockFilter)
      .replace('$searchFilter', searchFilter)
      .replace('$sortBy', params.sortBy || 'name.tr')
      .replace('$sortOrder', params.sortOrder || 'asc')
      .replace('$start', start.toString())
      .replace('$end', end.toString());

    const finalCountQuery = COUNT_QUERY
      .replace('$categoryFilter', categoryFilter)
      .replace('$flagsFilter', flagsFilter)
      .replace('$priceFilter', priceFilter)
      .replace('$stockFilter', stockFilter)
      .replace('$searchFilter', searchFilter);

    // Execute queries
    const [products, total] = await Promise.all([
      client.fetch<Product[]>(finalProductsQuery),
      client.fetch<number>(finalCountQuery)
    ]);

    // Return response
    return NextResponse.json({
      data: products,
      pagination: {
        page: params.page,
        limit: params.limit,
        total,
        totalPages: Math.ceil(total / params.limit),
        hasNext: end < total,
        hasPrev: params.page > 1,
      },
      success: true,
    });

  } catch (error) {
    console.error('Error fetching products:', error);
    return NextResponse.json(
      { 
        error: { 
          message: 'Failed to fetch products',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        success: false 
      },
      { status: 500 }
    );
  }
}
