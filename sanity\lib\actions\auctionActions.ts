// Sanity Actions for Auction Management
import { client } from '../client';
import { nanoid } from 'nanoid';

// Update auction status based on time
export const updateAuctionStatus = async (auctionId: string): Promise<{ success: boolean; newStatus?: string; error?: string }> => {
  try {
    const auction = await client.fetch(
      `*[_type == "auction" && _id == $auctionId][0]{startTime, endTime, status}`,
      { auctionId }
    );
    
    if (!auction) {
      return { success: false, error: 'Açık artırma bulunamadı' };
    }
    
    const now = new Date();
    const startTime = new Date(auction.startTime);
    const endTime = new Date(auction.endTime);
    
    let newStatus = auction.status;
    
    // Determine new status based on time
    if (now < startTime && auction.status !== 'upcoming') {
      newStatus = 'upcoming';
    } else if (now >= startTime && now <= endTime && auction.status !== 'active') {
      newStatus = 'active';
    } else if (now > endTime && auction.status !== 'completed') {
      newStatus = 'completed';
    }
    
    // Update status if it has changed
    if (newStatus !== auction.status) {
      await client
        .patch(auctionId)
        .set({ status: newStatus })
        .commit();
      
      // If auction is completed, handle completion logic
      if (newStatus === 'completed') {
        await handleAuctionCompletion(auctionId);
      }
      
      return { success: true, newStatus };
    }
    
    return { success: true, newStatus: auction.status };
  } catch (error) {
    console.error('Açık artırma durumu güncelleme hatası:', error);
    return { success: false, error: 'Durum güncellenemedi' };
  }
};

// Place a bid in auction
export const placeBid = async (
  auctionId: string, 
  bidderId: string, 
  bidAmount: number
): Promise<{ success: boolean; error?: string }> => {
  try {
    // Get current auction state
    const auction = await client.fetch(
      `*[_type == "auction" && _id == $auctionId][0]{
        currentBid, 
        bidIncrementAmount, 
        status, 
        startTime, 
        endTime, 
        bidders, 
        bidHistory,
        winner
      }`,
      { auctionId }
    );
    
    if (!auction) {
      return { success: false, error: 'Açık artırma bulunamadı' };
    }
    
    // Validate auction status and timing
    const now = new Date();
    const startTime = new Date(auction.startTime);
    const endTime = new Date(auction.endTime);
    
    if (auction.status !== 'active' || now < startTime || now > endTime) {
      return { success: false, error: 'Açık artırma aktif değil' };
    }
    
    // Validate bid amount
    if (bidAmount <= auction.currentBid) {
      return { success: false, error: 'Teklif mevcut tekliften yüksek olmalıdır' };
    }
    
    if (bidAmount < auction.currentBid + auction.bidIncrementAmount) {
      return { success: false, error: `Minimum artış miktarı: ${auction.bidIncrementAmount}` };
    }
    
    // Check if user is already the highest bidder
    if (auction.winner && auction.winner._ref === bidderId) {
      return { success: false, error: 'Zaten en yüksek teklifi verdiniz' };
    }
    
    // Prepare bid history entry
    const bidHistoryEntry = {
      bidAmount,
      bidder: { _type: 'reference', _ref: bidderId },
      bidTime: new Date().toISOString()
    };
    
    // Update auction with new bid
    const updatedBidders = auction.bidders || [];
    if (!updatedBidders.some((bidder: any) => bidder._ref === bidderId)) {
      updatedBidders.push({ _type: 'reference', _ref: bidderId });
    }
    
    const updatedBidHistory = [...(auction.bidHistory || []), bidHistoryEntry];
    
    await client
      .patch(auctionId)
      .set({
        currentBid: bidAmount,
        winner: { _type: 'reference', _ref: bidderId },
        bidders: updatedBidders,
        bidHistory: updatedBidHistory
      })
      .commit();
    
    // Create notification for previous winner (if exists)
    if (auction.winner && auction.winner._ref !== bidderId) {
      await createNotification({
        type: 'auction_outbid',
        title: { tr: 'Teklifiniz Aşıldı', en: 'Your Bid Was Outbid' },
        message: { tr: 'Açık artırmada teklifiniz aşıldı', en: 'Your bid in the auction was outbid' },
        user: { _type: 'reference', _ref: auction.winner._ref },
        relatedDocument: { _type: 'reference', _ref: auctionId }
      });
    }
    
    // Create notification for new bidder
    await createNotification({
      type: 'auction_bid',
      title: { tr: 'Teklif Verildi', en: 'Bid Placed' },
      message: { tr: 'Açık artırmaya teklif verdiniz', en: 'You placed a bid in the auction' },
      user: { _type: 'reference', _ref: bidderId },
      relatedDocument: { _type: 'reference', _ref: auctionId }
    });
    
    return { success: true };
  } catch (error) {
    console.error('Teklif verme hatası:', error);
    return { success: false, error: 'Teklif verilemedi' };
  }
};

// Handle auction completion
export const handleAuctionCompletion = async (auctionId: string): Promise<{ success: boolean; error?: string }> => {
  try {
    const auction = await client.fetch(
      `*[_type == "auction" && _id == $auctionId][0]{
        winner, 
        product, 
        currentBid, 
        currency
      }`,
      { auctionId }
    );
    
    if (!auction) {
      return { success: false, error: 'Açık artırma bulunamadı' };
    }
    
    // If there's a winner, create order
    if (auction.winner) {
      await createOrderForWinner(auctionId, auction);
      
      // Create notification for winner
      await createNotification({
        type: 'auction_won',
        title: { tr: 'Açık Artırma Kazandınız!', en: 'You Won the Auction!' },
        message: { tr: 'Tebrikler! Açık artırmayı kazandınız', en: 'Congratulations! You won the auction' },
        user: { _type: 'reference', _ref: auction.winner._ref },
        relatedDocument: { _type: 'reference', _ref: auctionId }
      });
    }
    
    return { success: true };
  } catch (error) {
    console.error('Açık artırma tamamlama hatası:', error);
    return { success: false, error: 'Tamamlama işlemi başarısız' };
  }
};

// Create order for auction winner
export const createOrderForWinner = async (auctionId: string, auction: any): Promise<void> => {
  try {
    const orderData = {
      _type: 'order',
      orderNumber: `ORD-${nanoid(8)}`,
      customer: {
        name: 'Auction Winner', // This should be populated from user data
        email: '<EMAIL>', // This should be populated from user data
        clerkUserId: auction.winner._ref
      },
      orderItems: [{
        product: { _type: 'reference', _ref: auction.product._ref },
        quantity: 1,
        price: auction.currentBid,
        currency: auction.currency
      }],
      totalAmount: auction.currentBid,
      currency: auction.currency,
      paymentStatus: 'pending',
      orderStatus: 'pending',
      source: 'auction',
      createdAt: new Date().toISOString(),
    };
    
    await client.create(orderData);
  } catch (error) {
    console.error('Kazanan için sipariş oluşturma hatası:', error);
  }
};

// Create notification
export const createNotification = async (notificationData: any): Promise<void> => {
  try {
    await client.create({
      _type: 'notification',
      id: `NOT-${nanoid(8)}`,
      ...notificationData,
      sentAt: new Date().toISOString(),
      isRead: false
    });
  } catch (error) {
    console.error('Bildirim oluşturma hatası:', error);
  }
};

// Get auction statistics
export const getAuctionStatistics = async (auctionId: string) => {
  try {
    const auction = await client.fetch(
      `*[_type == "auction" && _id == $auctionId][0]{
        startingBid,
        currentBid,
        bidHistory,
        bidders
      }`,
      { auctionId }
    );
    
    if (!auction) {
      return null;
    }
    
    const totalBids = auction.bidHistory?.length || 0;
    const uniqueBidders = auction.bidders?.length || 0;
    const priceIncrease = auction.currentBid - auction.startingBid;
    const priceIncreasePercentage = auction.startingBid > 0 
      ? ((priceIncrease / auction.startingBid) * 100).toFixed(2)
      : '0';
    
    return {
      totalBids,
      uniqueBidders,
      priceIncrease,
      priceIncreasePercentage,
      averageBidIncrease: totalBids > 0 ? (priceIncrease / totalBids).toFixed(2) : '0'
    };
  } catch (error) {
    console.error('Açık artırma istatistikleri alma hatası:', error);
    return null;
  }
};

// Validate auction before starting
export const validateAuctionForStart = async (auctionId: string): Promise<{ valid: boolean; errors: string[] }> => {
  const errors: string[] = [];
  
  try {
    const auction = await client.fetch(
      `*[_type == "auction" && _id == $auctionId][0]{
        product,
        startingBid,
        bidIncrementAmount,
        startTime,
        endTime,
        status
      }`,
      { auctionId }
    );
    
    if (!auction) {
      errors.push('Açık artırma bulunamadı');
      return { valid: false, errors };
    }
    
    if (!auction.product) {
      errors.push('Ürün bilgisi eksik');
    }
    
    if (auction.startingBid <= 0) {
      errors.push('Başlangıç teklifi sıfırdan büyük olmalıdır');
    }
    
    if (auction.bidIncrementAmount <= 0) {
      errors.push('Artış miktarı sıfırdan büyük olmalıdır');
    }
    
    const startTime = new Date(auction.startTime);
    const endTime = new Date(auction.endTime);
    
    if (startTime >= endTime) {
      errors.push('Bitiş tarihi başlangıç tarihinden sonra olmalıdır');
    }
    
    if (auction.status !== 'upcoming') {
      errors.push('Açık artırma başlatılabilir durumda değil');
    }
    
  } catch (error) {
    console.error('Açık artırma doğrulama hatası:', error);
    errors.push('Doğrulama yapılamadı');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
};

// Cancel auction
export const cancelAuction = async (auctionId: string, reason?: string): Promise<{ success: boolean; error?: string }> => {
  try {
    const auction = await client.fetch(
      `*[_type == "auction" && _id == $auctionId][0]{status, bidders}`,
      { auctionId }
    );
    
    if (!auction) {
      return { success: false, error: 'Açık artırma bulunamadı' };
    }
    
    if (auction.status === 'completed') {
      return { success: false, error: 'Tamamlanmış açık artırma iptal edilemez' };
    }
    
    // Update auction status
    await client
      .patch(auctionId)
      .set({ 
        status: 'cancelled',
        cancelReason: reason || 'İptal edildi'
      })
      .commit();
    
    // Notify all bidders
    if (auction.bidders && auction.bidders.length > 0) {
      for (const bidder of auction.bidders) {
        await createNotification({
          type: 'general',
          title: { tr: 'Açık Artırma İptal Edildi', en: 'Auction Cancelled' },
          message: { tr: 'Katıldığınız açık artırma iptal edildi', en: 'The auction you participated in was cancelled' },
          user: { _type: 'reference', _ref: bidder._ref },
          relatedDocument: { _type: 'reference', _ref: auctionId }
        });
      }
    }
    
    return { success: true };
  } catch (error) {
    console.error('Açık artırma iptal etme hatası:', error);
    return { success: false, error: 'İptal işlemi başarısız' };
  }
};
