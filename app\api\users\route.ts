import { NextRequest, NextResponse } from 'next/server';
import { client } from '@/sanity/lib/client';
import { User, UserQueryParams } from '@/types/sanity';

// Enhanced GROQ query for users
const USERS_QUERY = `
  *[_type == "user" 
    $statusFilter
    $roleFilter
    $approvalFilter
    $searchFilter
  ] | order($sortBy $sortOrder) [$start...$end] {
    _id,
    _type,
    _createdAt,
    _updatedAt,
    _rev,
    clerkId,
    name,
    email,
    image {
      asset->{
        _id,
        url
      },
      alt,
      hotspot,
      crop
    },
    phoneNumber,
    addresses,
    status,
    lastLogin,
    walletBalance,
    currency,
    isAdminApproved,
    role
  }
`;

const COUNT_QUERY = `
  count(*[_type == "user" 
    $statusFilter
    $roleFilter
    $approvalFilter
    $searchFilter
  ])
`;

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse query parameters
    const params: UserQueryParams = {
      status: searchParams.get('status') as any || undefined,
      role: searchParams.get('role') as any || undefined,
      isAdminApproved: searchParams.get('isAdminApproved') === 'true' ? true : 
                       searchParams.get('isAdminApproved') === 'false' ? false : undefined,
      search: searchParams.get('search') || undefined,
      page: Number(searchParams.get('page')) || 1,
      limit: Number(searchParams.get('limit')) || 20,
      sortBy: (searchParams.get('sortBy') as any) || 'createdAt',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
    };

    // Build dynamic filters
    let statusFilter = '';
    let roleFilter = '';
    let approvalFilter = '';
    let searchFilter = '';

    if (params.status) {
      statusFilter = `&& status == "${params.status}"`;
    }

    if (params.role) {
      roleFilter = `&& role == "${params.role}"`;
    }

    if (params.isAdminApproved !== undefined) {
      approvalFilter = `&& isAdminApproved == ${params.isAdminApproved}`;
    }

    if (params.search) {
      const searchTerm = params.search.toLowerCase();
      searchFilter = `&& (
        name.tr match "*${searchTerm}*" ||
        name.en match "*${searchTerm}*" ||
        email match "*${searchTerm}*" ||
        clerkId match "*${searchTerm}*"
      )`;
    }

    // Calculate pagination
    const start = (params.page - 1) * params.limit;
    const end = start + params.limit;

    // Build final queries
    const finalUsersQuery = USERS_QUERY
      .replace('$statusFilter', statusFilter)
      .replace('$roleFilter', roleFilter)
      .replace('$approvalFilter', approvalFilter)
      .replace('$searchFilter', searchFilter)
      .replace('$sortBy', params.sortBy || 'name.tr')
      .replace('$sortOrder', params.sortOrder || 'asc')
      .replace('$start', start.toString())
      .replace('$end', end.toString());

    const finalCountQuery = COUNT_QUERY
      .replace('$statusFilter', statusFilter)
      .replace('$roleFilter', roleFilter)
      .replace('$approvalFilter', approvalFilter)
      .replace('$searchFilter', searchFilter);

    // Execute queries
    const [users, total] = await Promise.all([
      client.fetch<User[]>(finalUsersQuery),
      client.fetch<number>(finalCountQuery)
    ]);

    // Return response
    return NextResponse.json({
      data: users,
      pagination: {
        page: params.page,
        limit: params.limit,
        total,
        totalPages: Math.ceil(total / params.limit),
        hasNext: end < total,
        hasPrev: params.page > 1,
      },
      success: true,
    });

  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { 
        error: { 
          message: 'Failed to fetch users',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        success: false 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const userData = await request.json();
    
    // Create user
    const user = await client.create({
      _type: 'user',
      ...userData,
      walletBalance: 0, // Initialize wallet balance
      isAdminApproved: false, // Require admin approval by default
      status: 'active',
      role: 'user',
    });

    return NextResponse.json({
      data: user,
      success: true,
    });

  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { 
        error: { 
          message: 'Failed to create user',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        success: false 
      },
      { status: 500 }
    );
  }
}
