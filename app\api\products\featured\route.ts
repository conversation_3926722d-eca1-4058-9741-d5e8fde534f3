import { NextRequest, NextResponse } from 'next/server';
import { client } from '@/sanity/lib/client';
import { Product } from '@/types/sanity';

const FEATURED_PRODUCTS_QUERY = `
  *[_type == "product" && "featured" in flags && defined(name.tr)] | order(_createdAt desc) [0...$limit] {
    _id,
    _type,
    _createdAt,
    _updatedAt,
    _rev,
    id,
    name,
    slug,
    image {
      asset->{
        _id,
        url
      },
      alt,
      hotspot,
      crop
    },
    description,
    category->{
      _id,
      title,
      slug
    },
    tags,
    price,
    currency,
    discount,
    sku,
    stock,
    isAvailable,
    variants,
    flags,
    seoTitle,
    seoDescription
  }
`;

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = Number(searchParams.get('limit')) || 10;

    const products = await client.fetch<Product[]>(FEATURED_PRODUCTS_QUERY, { limit });

    return NextResponse.json({
      data: products,
      total: products.length,
      success: true,
    });

  } catch (error) {
    console.error('Error fetching featured products:', error);
    return NextResponse.json(
      { 
        error: { 
          message: 'Failed to fetch featured products',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        success: false 
      },
      { status: 500 }
    );
  }
}
