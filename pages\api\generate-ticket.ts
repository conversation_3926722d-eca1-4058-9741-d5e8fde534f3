import { NextApiRequest, NextApiResponse } from 'next'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { type } = req.query

  const ticketMap: Record<string, { digits: number; count: number }> = {
    A: { digits: 3, count: 3 },
    B: { digits: 3, count: 4 },
    C: { digits: 3, count: 6 },
    D: { digits: 3, count: 9 },
    E: { digits: 3, count: 12 },
    F: { digits: 4, count: 3 },
    G: { digits: 4, count: 4 },
    H: { digits: 4, count: 6 },
    I: { digits: 4, count: 9 },
    J: { digits: 4, count: 12 },
  }

  const config = ticketMap[type as string]
  if (!config) {
    return res.status(400).json({ error: 'Geçersiz kart tipi.' })
  }

  // Bu API artık kullanılmıyor - yeni çekiliş sistemi için /api/giveaways/buyTickets kullanın
  return res.status(410).json({ error: 'Bu API artık kullanılmıyor. Lütfen yeni çekiliş sistemini kullanın.' })

} 