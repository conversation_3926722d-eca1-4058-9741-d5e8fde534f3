// System Status and Health Check API
import { NextRequest, NextResponse } from 'next/server';
import { client } from '@/sanity/lib/client';
import CronManager from '@/lib/scheduler/cron-manager';
import WebhookManager from '@/lib/webhooks/manager';
import AnalyticsManager from '@/lib/analytics/analytics-manager';
import EmailManager from '@/lib/email/email-manager';
import { FCMTokenManager } from '@/lib/firebase/fcm';
import { ErrorTracker } from '@/lib/sentry/config';

// System status interface
interface SystemStatus {
  status: 'healthy' | 'degraded' | 'down';
  timestamp: string;
  version: string;
  uptime: number;
  services: {
    sanity: ServiceStatus;
    database: ServiceStatus;
    email: ServiceStatus;
    notifications: ServiceStatus;
    webhooks: ServiceStatus;
    analytics: ServiceStatus;
    scheduler: ServiceStatus;
    sentry: ServiceStatus;
  };
  metrics: {
    totalUsers: number;
    activeAuctions: number;
    activeGiveaways: number;
    pendingOrders: number;
    systemLoad: number;
    memoryUsage: number;
  };
  scheduledTasks: Array<{
    id: string;
    name: string;
    isActive: boolean;
    lastRun?: string;
    runCount: number;
    errorCount: number;
  }>;
}

interface ServiceStatus {
  status: 'healthy' | 'degraded' | 'down';
  responseTime?: number;
  lastCheck: string;
  error?: string;
}

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    console.log('🔍 Performing system health check...');

    // Initialize managers
    const cronManager = CronManager.getInstance();
    const webhookManager = WebhookManager.getInstance();
    const analyticsManager = AnalyticsManager.getInstance();
    const emailManager = EmailManager.getInstance();

    // Check all services in parallel
    const [
      sanityStatus,
      emailStatus,
      notificationStatus,
      scheduledTasks,
      systemMetrics
    ] = await Promise.allSettled([
      checkSanityHealth(),
      checkEmailHealth(emailManager),
      checkNotificationHealth(),
      getScheduledTasksStatus(cronManager),
      getSystemMetrics()
    ]);

    // Compile system status
    const systemStatus: SystemStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.APP_VERSION || '1.0.0',
      uptime: process.uptime(),
      services: {
        sanity: getResultValue(sanityStatus, { status: 'down', lastCheck: new Date().toISOString() }),
        database: { status: 'healthy', lastCheck: new Date().toISOString() }, // Same as Sanity for now
        email: getResultValue(emailStatus, { status: 'down', lastCheck: new Date().toISOString() }),
        notifications: getResultValue(notificationStatus, { status: 'down', lastCheck: new Date().toISOString() }),
        webhooks: { status: 'healthy', lastCheck: new Date().toISOString() }, // Webhook manager is always available
        analytics: { status: 'healthy', lastCheck: new Date().toISOString() }, // Analytics manager is always available
        scheduler: { status: 'healthy', lastCheck: new Date().toISOString() }, // Scheduler is always available
        sentry: { status: 'healthy', lastCheck: new Date().toISOString() }, // Sentry is always available
      },
      metrics: getResultValue(systemMetrics, {
        totalUsers: 0,
        activeAuctions: 0,
        activeGiveaways: 0,
        pendingOrders: 0,
        systemLoad: 0,
        memoryUsage: 0,
      }),
      scheduledTasks: getResultValue(scheduledTasks, []),
    };

    // Determine overall system status
    const serviceStatuses = Object.values(systemStatus.services).map(service => service.status);
    if (serviceStatuses.includes('down')) {
      systemStatus.status = 'down';
    } else if (serviceStatuses.includes('degraded')) {
      systemStatus.status = 'degraded';
    }

    const responseTime = Date.now() - startTime;
    console.log(`✅ System health check completed in ${responseTime}ms - Status: ${systemStatus.status}`);

    // Log to analytics
    analyticsManager.track('system_health_check', {
      status: systemStatus.status,
      responseTime,
      serviceCount: Object.keys(systemStatus.services).length,
    });

    return NextResponse.json({
      data: systemStatus,
      success: true,
      responseTime,
    });

  } catch (error) {
    console.error('❌ System health check failed:', error);
    
    ErrorTracker.captureException(error as Error, {
      context: 'system_health_check',
      responseTime: Date.now() - startTime,
    });

    return NextResponse.json(
      {
        error: {
          message: 'System health check failed',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
        success: false,
        responseTime: Date.now() - startTime,
      },
      { status: 500 }
    );
  }
}

// Check Sanity health
async function checkSanityHealth(): Promise<ServiceStatus> {
  const startTime = Date.now();
  
  try {
    await client.fetch('*[_type == "product"][0]');
    
    return {
      status: 'healthy',
      responseTime: Date.now() - startTime,
      lastCheck: new Date().toISOString(),
    };
  } catch (error) {
    return {
      status: 'down',
      responseTime: Date.now() - startTime,
      lastCheck: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Check email service health
async function checkEmailHealth(emailManager: EmailManager): Promise<ServiceStatus> {
  const startTime = Date.now();
  
  try {
    const isConnected = await emailManager.verifyConnection();
    
    return {
      status: isConnected ? 'healthy' : 'down',
      responseTime: Date.now() - startTime,
      lastCheck: new Date().toISOString(),
      error: isConnected ? undefined : 'Email server connection failed',
    };
  } catch (error) {
    return {
      status: 'down',
      responseTime: Date.now() - startTime,
      lastCheck: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Check notification service health
async function checkNotificationHealth(): Promise<ServiceStatus> {
  const startTime = Date.now();
  
  try {
    // Check if FCM is properly configured
    const fcmConfigured = !!(
      process.env.NEXT_PUBLIC_FIREBASE_API_KEY &&
      process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID &&
      process.env.FIREBASE_SERVER_KEY
    );
    
    return {
      status: fcmConfigured ? 'healthy' : 'degraded',
      responseTime: Date.now() - startTime,
      lastCheck: new Date().toISOString(),
      error: fcmConfigured ? undefined : 'Firebase configuration incomplete',
    };
  } catch (error) {
    return {
      status: 'down',
      responseTime: Date.now() - startTime,
      lastCheck: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Get scheduled tasks status
async function getScheduledTasksStatus(cronManager: CronManager) {
  try {
    return cronManager.getTaskInfo();
  } catch (error) {
    console.error('Failed to get scheduled tasks status:', error);
    return [];
  }
}

// Get system metrics
async function getSystemMetrics() {
  try {
    const [userCount, auctionCount, giveawayCount, orderCount] = await Promise.all([
      client.fetch('count(*[_type == "user"])'),
      client.fetch('count(*[_type == "auction" && status == "active"])'),
      client.fetch('count(*[_type == "giveaway" && status == "active"])'),
      client.fetch('count(*[_type == "order" && orderStatus == "pending"])'),
    ]);

    // Get system resource usage
    const memoryUsage = process.memoryUsage();
    const memoryUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;

    return {
      totalUsers: userCount || 0,
      activeAuctions: auctionCount || 0,
      activeGiveaways: giveawayCount || 0,
      pendingOrders: orderCount || 0,
      systemLoad: 0, // Would need OS-specific implementation
      memoryUsage: Math.round(memoryUsagePercent),
    };
  } catch (error) {
    console.error('Failed to get system metrics:', error);
    return {
      totalUsers: 0,
      activeAuctions: 0,
      activeGiveaways: 0,
      pendingOrders: 0,
      systemLoad: 0,
      memoryUsage: 0,
    };
  }
}

// Helper function to extract value from PromiseSettledResult
function getResultValue<T>(result: PromiseSettledResult<T>, defaultValue: T): T {
  return result.status === 'fulfilled' ? result.value : defaultValue;
}

// System initialization endpoint
export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Initializing system components...');

    // Initialize all managers
    const cronManager = CronManager.getInstance();
    const webhookManager = WebhookManager.getInstance();
    const analyticsManager = AnalyticsManager.getInstance();
    const emailManager = EmailManager.getInstance();

    // Initialize scheduled tasks
    await cronManager.initialize();

    // Verify email connection
    await emailManager.verifyConnection();

    console.log('✅ System initialization completed');

    return NextResponse.json({
      message: 'System initialized successfully',
      success: true,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('❌ System initialization failed:', error);
    
    ErrorTracker.captureException(error as Error, {
      context: 'system_initialization',
    });

    return NextResponse.json(
      {
        error: {
          message: 'System initialization failed',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
        success: false,
      },
      { status: 500 }
    );
  }
}
