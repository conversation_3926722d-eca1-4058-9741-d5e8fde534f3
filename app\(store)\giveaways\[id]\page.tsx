import { notFound } from 'next/navigation';
import Image from 'next/image';
import { getGiveawayById } from '@/sanity/lib/giveaways/getGiveaways';
// import { urlForImage } from '@/sanity/lib/image';
import { FiGift, FiUsers, FiClock, FiCalendar, FiAward } from 'react-icons/fi';
import GiveawayTicketPurchase from '@/components/GiveawayTicketPurchase';
import { currentUser } from '@clerk/nextjs/server';

interface GiveawayPageProps {
  params: {
    id: string;
  };
}

export default async function GiveawayPage({ params }: GiveawayPageProps) {
  const { id } = await params;
  const giveaway = await getGiveawayById(id);

  if (!giveaway) {
    notFound();
  }

  // Get current user
  const user = await currentUser();
  const currentUserId = user?.id;

  // Find user's tickets for this giveaway
  let userTickets: string[] = [];
  if (currentUserId && giveaway.participants) {
    const participant = giveaway.participants.find((p: any) => p.user?._ref === currentUserId);
    if (participant && participant.tickets) {
      userTickets = participant.tickets.map((t: any) => t.ticketNumber);
    }
  }

  // Helper to normalize ticket numbers (trim only, do not remove leading zeros)
  function normalizeTicketNumber(num: any) {
    return String(num).trim();
  }

  // Check if user is a winner (robust string comparison, no zero removal)
  const kazandiniz = giveaway.winners?.some(
    (w: any) =>
      w.user?._ref === currentUserId &&
      userTickets.map(normalizeTicketNumber).includes(normalizeTicketNumber(w.ticketNumber))
  );

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const calculateProgress = () => {
    if (!giveaway.maxTickets) return 0;
    return Math.min(((giveaway.ticketsSold || 0) / giveaway.maxTickets) * 100, 100);
  };

  const isActive = giveaway.status === 'active';
  const isEnded = giveaway.status === 'completed' || 
    (giveaway.endDate && new Date(giveaway.endDate) < new Date());

  return (
    <div className="container mx-auto p-4 max-w-6xl">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-4xl font-bold text-gray-800">
            {typeof giveaway.title === 'string'
              ? giveaway.title
              : (giveaway.title?.tr || giveaway.title?.en || 'Çekiliş')
            }
          </h1>
          <div className="flex items-center space-x-2">
            <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
              isActive ? 'bg-green-100 text-green-800' :
              isEnded ? 'bg-red-100 text-red-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {isActive ? 'Aktif' : isEnded ? 'Sona Erdi' : 'Beklemede'}
            </span>
            {typeof kazandiniz === 'boolean' && (
              kazandiniz ? (
                <span className="ml-2 px-3 py-1 rounded-full bg-green-200 text-green-800 font-semibold">Kazandınız</span>
              ) : (
                <span className="ml-2 px-3 py-1 rounded-full bg-red-200 text-red-800 font-semibold">Kazanamadınız</span>
              )
            )}
          </div>
        </div>
        
        {giveaway.description && (
          <p className="text-lg text-gray-600">
            {typeof giveaway.description === 'string'
              ? giveaway.description
              : (giveaway.description?.tr || giveaway.description?.en || '')
            }
          </p>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Giveaway Image */}
        <div className="relative h-96 bg-gray-100 rounded-lg overflow-hidden">
          {giveaway.image && giveaway.image.asset && giveaway.image.asset.url ? (
            <img
              src={giveaway.image.asset.url}
              alt={typeof giveaway.title === 'string'
                ? giveaway.title
                : (giveaway.title?.tr || giveaway.title?.en || 'Çekiliş Görseli')
              }
              className="object-cover w-full h-full"
            />
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-gray-400">
              <FiGift size={64} />
              <span className="mt-2">Çekiliş Görseli</span>
            </div>
          )}
        </div>

        {/* Giveaway Info */}
        <div className="space-y-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
              <div className="flex items-center text-green-600 mb-2">
                <FiGift className="mr-2" />
                <span className="font-semibold">Bilet Fiyatı</span>
              </div>
              <div className="text-2xl font-bold text-green-700">
                ₺{giveaway.ticketPrice || 0}
              </div>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <div className="flex items-center text-blue-600 mb-2">
                <FiUsers className="mr-2" />
                <span className="font-semibold">Katılımcı</span>
              </div>
              <div className="text-2xl font-bold text-blue-700">
                {giveaway.participants?.length || 0}
              </div>
            </div>
          </div>

          {/* Progress Bar */}
          {giveaway.maxTickets && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex justify-between items-center mb-2">
                <span className="font-semibold text-gray-700">Satış Durumu</span>
                <span className="text-sm text-gray-600">
                  {giveaway.ticketsSold || 0} / {giveaway.maxTickets}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div 
                  className="bg-gradient-to-r from-green-500 to-blue-600 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${calculateProgress()}%` }}
                ></div>
              </div>
              <div className="text-sm text-gray-600 mt-1">
                %{calculateProgress().toFixed(1)} tamamlandı
              </div>
            </div>
          )}

          {/* Dates */}
          <div className="space-y-3">
            {giveaway.startDate && (
              <div className="flex items-center text-purple-600">
                <FiCalendar className="mr-3" />
                <div>
                  <span className="font-semibold">Başlangıç: </span>
                  <span>{formatDate(giveaway.startDate)}</span>
                </div>
              </div>
            )}
            
            {giveaway.endDate && (
              <div className="flex items-center text-orange-600">
                <FiClock className="mr-3" />
                <div>
                  <span className="font-semibold">Bitiş: </span>
                  <span>{formatDate(giveaway.endDate)}</span>
                </div>
              </div>
            )}

            {giveaway.drawDate && (
              <div className="flex items-center text-red-600">
                <FiAward className="mr-3" />
                <div>
                  <span className="font-semibold">Çekiliş Tarihi: </span>
                  <span>{formatDate(giveaway.drawDate)}</span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Prizes Section */}
      {giveaway.prizes && giveaway.prizes.length > 0 && (
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Ödüller</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {giveaway.prizes.map((prize, index) => (
              <div key={index} className="bg-gradient-to-br from-yellow-50 to-orange-50 p-4 rounded-lg border border-yellow-200">
                <div className="flex items-center mb-2">
                  <FiAward className="text-yellow-600 mr-2" />
                  <span className="font-semibold text-yellow-800">
                    {prize.rank}. Ödül
                  </span>
                </div>
                <h3 className="font-bold text-gray-800 mb-1">{prize.title}</h3>
                {prize.description && (
                  <p className="text-sm text-gray-600 mb-2">{prize.description}</p>
                )}
                {prize.value && (
                  <div className="text-lg font-bold text-yellow-700">
                    ₺{prize.value.toLocaleString()}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Rules */}
      {giveaway.rules && (
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Çekiliş Kuralları</h2>
          <div className="bg-gray-50 p-6 rounded-lg">
            <p className="text-gray-700 whitespace-pre-line">{giveaway.rules}</p>
          </div>
        </div>
      )}

      {/* Ticket Purchase Section */}
      {isActive && (
        <div className="bg-white border rounded-lg shadow-lg p-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Çekilişe Katıl</h2>
          <GiveawayTicketPurchase
            giveawayId={giveaway._id}
            ticketPrice={giveaway.ticketPrice}
          />
        </div>
      )}

      {/* Winners Section */}
      {giveaway.winners && giveaway.winners.length > 0 && (
        <div className="mt-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Kazananlar</h2>
          <div className="bg-gradient-to-r from-green-50 to-blue-50 p-6 rounded-lg">
            <div className="space-y-3">
              {giveaway.winners.map((winner, index) => (
                <div key={index} className="flex items-center justify-between bg-white p-3 rounded-lg">
                  <div className="flex items-center">
                    <FiAward className="text-yellow-500 mr-3" />
                    <div>
                      <span className="font-semibold">{winner.rank}. Sıra</span>
                      <div className="text-sm text-gray-600">
                        Bilet: {winner.ticketNumber}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold text-gray-800">{winner.prize}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Kazananlar Listesi */}
      {giveaway.winners && giveaway.winners.length > 0 && (
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Kazananlar</h2>
          <ul className="space-y-2">
            {giveaway.winners.map((w: any, i: number) => (
              <li key={i} className="flex items-center gap-4 bg-green-50 rounded p-2">
                <span className="font-semibold text-green-800">{w.user?.name || w.user?._id || 'Kullanıcı'}</span>
                <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded font-mono">{w.ticketNumber}</span>
                <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded">{w.prize}</span>
                <span className="text-xs text-gray-500">Sıra: {w.rank}</span>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
