// Utility functions for handling multilingual content
import { MultilingualText, MultilingualTextArea } from '@/types/sanity';

/**
 * Get text in preferred language with fallback
 * @param text - Multilingual text object
 * @param language - Preferred language ('tr' | 'en')
 * @param fallback - Fallback text if no translation found
 * @returns Localized text string
 */
export function getLocalizedText(
  text: MultilingualText | MultilingualTextArea | string | undefined | null,
  language: 'tr' | 'en' = 'tr',
  fallback: string = ''
): string {
  // Handle null/undefined
  if (!text) return fallback;
  
  // Handle string (legacy support)
  if (typeof text === 'string') return text;
  
  // Handle multilingual object
  if (typeof text === 'object') {
    // Try preferred language first
    if (language === 'tr' && text.tr) return text.tr;
    if (language === 'en' && text.en) return text.en;
    
    // Fallback to other language
    if (text.tr) return text.tr;
    if (text.en) return text.en;
  }
  
  return fallback;
}

/**
 * Get Turkish text with English fallback
 * @param text - Multilingual text object
 * @param fallback - Fallback text if no translation found
 * @returns Turkish text or fallback
 */
export function getTurkishText(
  text: MultilingualText | MultilingualTextArea | string | undefined | null,
  fallback: string = ''
): string {
  return getLocalizedText(text, 'tr', fallback);
}

/**
 * Get English text with Turkish fallback
 * @param text - Multilingual text object
 * @param fallback - Fallback text if no translation found
 * @returns English text or fallback
 */
export function getEnglishText(
  text: MultilingualText | MultilingualTextArea | string | undefined | null,
  fallback: string = ''
): string {
  return getLocalizedText(text, 'en', fallback);
}

/**
 * Check if multilingual text has content in any language
 * @param text - Multilingual text object
 * @returns True if text has content
 */
export function hasContent(
  text: MultilingualText | MultilingualTextArea | string | undefined | null
): boolean {
  if (!text) return false;
  if (typeof text === 'string') return text.length > 0;
  if (typeof text === 'object') {
    return !!(text.tr || text.en);
  }
  return false;
}

/**
 * Get all available languages for a text
 * @param text - Multilingual text object
 * @returns Array of available language codes
 */
export function getAvailableLanguages(
  text: MultilingualText | MultilingualTextArea | string | undefined | null
): string[] {
  if (!text) return [];
  if (typeof text === 'string') return ['default'];
  if (typeof text === 'object') {
    const languages: string[] = [];
    if (text.tr) languages.push('tr');
    if (text.en) languages.push('en');
    return languages;
  }
  return [];
}

/**
 * Create a multilingual text object
 * @param tr - Turkish text
 * @param en - English text (optional)
 * @returns Multilingual text object
 */
export function createMultilingualText(tr: string, en?: string): MultilingualText {
  return { tr, en };
}

/**
 * Truncate multilingual text
 * @param text - Multilingual text object
 * @param maxLength - Maximum length
 * @param language - Preferred language
 * @param suffix - Suffix to add when truncated
 * @returns Truncated text
 */
export function truncateText(
  text: MultilingualText | MultilingualTextArea | string | undefined | null,
  maxLength: number = 100,
  language: 'tr' | 'en' = 'tr',
  suffix: string = '...'
): string {
  const localizedText = getLocalizedText(text, language);
  
  if (localizedText.length <= maxLength) {
    return localizedText;
  }
  
  return localizedText.substring(0, maxLength - suffix.length) + suffix;
}

/**
 * Format multilingual text for display
 * @param text - Multilingual text object
 * @param language - Preferred language
 * @param options - Formatting options
 * @returns Formatted text
 */
export function formatText(
  text: MultilingualText | MultilingualTextArea | string | undefined | null,
  language: 'tr' | 'en' = 'tr',
  options: {
    capitalize?: boolean;
    uppercase?: boolean;
    lowercase?: boolean;
    trim?: boolean;
  } = {}
): string {
  let result = getLocalizedText(text, language);
  
  if (options.trim) result = result.trim();
  if (options.lowercase) result = result.toLowerCase();
  if (options.uppercase) result = result.toUpperCase();
  if (options.capitalize) result = result.charAt(0).toUpperCase() + result.slice(1).toLowerCase();
  
  return result;
}
