import { NextRequest, NextResponse } from 'next/server';
import { client } from '@/sanity/lib/client';
import { Product } from '@/types/sanity';

const PRODUCT_BY_SLUG_QUERY = `
  *[_type == "product" && slug.current == $slug][0] {
    _id,
    _type,
    _createdAt,
    _updatedAt,
    _rev,
    id,
    name,
    slug,
    image {
      asset->{
        _id,
        url
      },
      alt,
      hotspot,
      crop
    },
    description,
    category->{
      _id,
      title,
      slug,
      image {
        asset->{
          _id,
          url
        },
        alt
      }
    },
    tags,
    price,
    currency,
    discount,
    sku,
    stock,
    isAvailable,
    variants,
    flags,
    seoTitle,
    seoDescription
  }
`;

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;

    if (!slug) {
      return NextResponse.json(
        { 
          error: { message: 'Product slug is required' },
          success: false 
        },
        { status: 400 }
      );
    }

    const product = await client.fetch<Product | null>(PRODUCT_BY_SLUG_QUERY, { slug });

    if (!product) {
      return NextResponse.json(
        { 
          error: { message: 'Product not found' },
          success: false 
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      data: product,
      success: true,
    });

  } catch (error) {
    console.error('Error fetching product by slug:', error);
    return NextResponse.json(
      { 
        error: { 
          message: 'Failed to fetch product',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        success: false 
      },
      { status: 500 }
    );
  }
}
