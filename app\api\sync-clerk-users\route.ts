import { NextRequest, NextResponse } from 'next/server'
import { client } from '@/sanity/lib/client'
import { createClerkClient } from '@clerk/nextjs/server'

const clerkClient = createClerkClient({
  secretKey: process.env.CLERK_SECRET_KEY,
})

export async function POST(req: NextRequest) {
  try {
    console.log('🔄 Starting Clerk users sync')
    
    // Get all users from Clerk
    const clerkUsers = await clerkClient.users.getUserList({
      limit: 100, // Adjust as needed
    })
    
    console.log(`👥 Found ${clerkUsers.data.length} users in Clerk`)
    
    const syncResults = []
    
    for (const clerkUser of clerkUsers.data) {
      try {
        const userData = {
          _type: 'user',
          _id: clerkUser.id,
          clerkId: clerkUser.id,
          name: `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim() || 
                clerkUser.emailAddresses[0]?.emailAddress || 'Unknown User',
          email: clerkUser.emailAddresses[0]?.emailAddress || '',
          isAdminApproved: false,
          imageUrl: clerkUser.imageUrl || null,
        }
        
        console.log(`👤 Syncing user: ${userData.name} (${userData.email})`)
        
        // Create or update user in Sanity
        const result = await client.createIfNotExists(userData)
        
        syncResults.push({
          clerkId: clerkUser.id,
          name: userData.name,
          email: userData.email,
          success: true,
          action: result._createdAt === result._updatedAt ? 'created' : 'updated'
        })
        
        console.log(`✅ Synced user: ${userData.name}`)
        
      } catch (error) {
        console.error(`❌ Failed to sync user ${clerkUser.id}:`, error)
        syncResults.push({
          clerkId: clerkUser.id,
          name: clerkUser.firstName || 'Unknown',
          email: clerkUser.emailAddresses[0]?.emailAddress || '',
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }
    
    const successCount = syncResults.filter(r => r.success).length
    const failureCount = syncResults.filter(r => !r.success).length
    
    console.log(`✅ Sync completed: ${successCount} success, ${failureCount} failures`)
    
    return NextResponse.json({
      success: true,
      message: `Synced ${successCount} users from Clerk to Sanity`,
      totalClerkUsers: clerkUsers.data.length,
      successCount: successCount,
      failureCount: failureCount,
      results: syncResults
    })
    
  } catch (error) {
    console.error('❌ Clerk sync error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function GET() {
  try {
    console.log('📊 Checking Clerk vs Sanity user count')
    
    // Get user counts
    const clerkUsers = await clerkClient.users.getUserList({ limit: 1 })
    const clerkUserCount = clerkUsers.totalCount
    
    const sanityUserCount = await client.fetch(`count(*[_type == "user"])`)
    const sanityUsersWithClerkId = await client.fetch(`count(*[_type == "user" && defined(clerkId)])`)
    
    return NextResponse.json({
      success: true,
      counts: {
        clerkUsers: clerkUserCount,
        sanityUsers: sanityUserCount,
        sanityUsersWithClerkId: sanityUsersWithClerkId,
        needsSync: clerkUserCount > sanityUsersWithClerkId
      }
    })
    
  } catch (error) {
    console.error('❌ Count check error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
