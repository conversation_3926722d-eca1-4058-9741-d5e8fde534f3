"use client";

import { Product } from "@/sanity.types";
import { useState } from "react";
import { useEffect } from "react";
import { useBasketStore } from "@/app/(store)/store";
import { Button } from "./ui/button";
import { MinusIcon } from "lucide-react";


interface AddToBasketButtonProps {
    product: Product;
    disabled: boolean;
}

function AddToBasketButton({ product, disabled }: AddToBasketButtonProps) {
    const {addItem , removeItem , getItemCount} = useBasketStore();

    const itemCount = getItemCount(product._id);
    const [isClient, setIsClient] = useState(false);

    useEffect(() => {
        setIsClient(true);
    }, []);

    if (!isClient) return null;

  return (
    <div
    className="flex items-center justify-center space-x-2">
        <Button
        onClick={() => removeItem(product._id)}
        className={`w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 ${
            itemCount === 0 

            ? 'bg-gray-400 cursor-not-allowed' 
            : 'bg-purple-300 hover:bg-purple-600'
        }

            ? 'bg-gray-100 cursor-not-allowed' 
            : 'bg-gray-300 hover:bg-gray-400'

        }`}
        disabled={itemCount === 0}
        >
        <span className={`${itemCount === 0 ? 'text-gray-600' : 'text-gray-800'}`}>
            {itemCount === 0 ? <MinusIcon className="w-4 h-4" /> : <MinusIcon className="w-4 h-4" />}
        
        </span>
        </Button>
        <span className="w-8 text-center font-semibold">{itemCount}</span>
        <Button
        onClick={() => addItem(product)}
        className={`w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 ${
            disabled 
            ? 'bg-gray-400 cursor-not-allowed' 
            : 'bg-purple-600 hover:bg-purple-900'
        }
            ? 'bg-gray-100 cursor-not-allowed' 
            : 'bg-blue-300 hover:bg-blue-400'

        }`}
        >
        <span className="w-8 h-8 text-lg font-semibold text-white-900">+</span>
        </Button>
    </div>
  )
}

export default AddToBasketButton