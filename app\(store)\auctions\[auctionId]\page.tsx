'use client';

import Image from 'next/image';
import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { getAuctionById } from '@/sanity/lib/auctions/getAuctions';
import { urlForImage } from '@/sanity/lib/image';
import { Auction, User } from '@/sanity.types';

export default function AuctionDetailPage() {
  const params = useParams();
  const auctionId = params?.auctionId as string;
  const [auction, setAuction] = useState<Auction | null>(null);
  const [loading, setLoading] = useState(true);
  const [newBidAmount, setNewBidAmount] = useState<number>(0);
  const [timeLeft, setTimeLeft] = useState<string>('');

  useEffect(() => {
    if (!auctionId) return; // Don't fetch if auctionId is not available yet

    const fetchAuction = async () => {
      setLoading(true);
      const fetchedAuction = await getAuctionById(auctionId);
      setAuction(fetchedAuction);
      const currentBid = fetchedAuction?.currentBid || 0;
      const startingBid = fetchedAuction?.startingBid || 0;
      const increment = fetchedAuction?.bidIncrementAmount || 1;

      if (currentBid > 0) {
        setNewBidAmount(currentBid + increment);
      } else {
        // If no bids yet, the next bid should be at least the starting bid.
        // The input will default to the starting bid.
        setNewBidAmount(startingBid);
      }
      setLoading(false);
    };

    fetchAuction();

    const interval = setInterval(() => {
      if (auction?.endTime) {
        const endTime = new Date(auction.endTime).getTime();
        const now = Date.now();
        const difference = endTime - now;

        if (difference > 0) {
          const days = Math.floor(difference / (1000 * 60 * 60 * 24));
          const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
          const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
          const seconds = Math.floor((difference % (1000 * 60)) / 1000);
          setTimeLeft(`${days}d ${hours}h ${minutes}m ${seconds}s`);
        } else {
          setTimeLeft('Bitti!');
          clearInterval(interval);
        }
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [auctionId, auction?.endTime]);

  if (loading) {
    return <div className="text-center">Yükleniyor...</div>;
  }

  if (!auction) {
    return <div className="text-center text-red-500">Açık artırma bulunamadı.</div>;
  }

  let highestBidder: User | null = null;
  if (auction.bidHistory && Array.isArray(auction.bidHistory) && auction.bidHistory.length > 0) {
    const sortedBidHistory = [...auction.bidHistory].sort((a, b) => {
      const bidTimeA = new Date(a.bidTime || 0).getTime();
      const bidTimeB = new Date(b.bidTime || 0).getTime();
      return bidTimeB - bidTimeA;
    });
    // The bidder object is expanded from a reference, so it's not a full User object.
    // We'll cast it to a simpler type for now.
    const bidderInfo = sortedBidHistory[0].bidder as { name?: string };
    if (bidderInfo?.name) {
        highestBidder = { name: bidderInfo.name } as User; // Partial user for display
    }
  }
  
  const handlePlaceBid = async () => {
    if (!auction) {
      alert('Açık artırma bilgileri yüklenemedi.');
      return;
    }
    const baseBid = auction.currentBid && auction.currentBid > 0 ? auction.currentBid : auction.startingBid;
    if (newBidAmount <= (baseBid || 0)) {
      const message = auction.currentBid && auction.currentBid > 0
        ? `Teklifiniz mevcut tekliften (₺${auction.currentBid}) yüksek olmalıdır.`
        : `Teklifiniz başlangıç fiyatından (₺${auction.startingBid || 0}) yüksek olmalıdır.`;
      alert(message);
      return;
    }

    try {
      const response = await fetch('/api/auctions/bid', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          auctionId: auction?._id,
          bidAmount: newBidAmount,
          // userId: Add logic to get current user ID (e.g., from Clerk)
        }),
      });

      const data = await response.json();

      if (response.ok) {
        alert('Teklifiniz başarıyla gönderildi!');
        // Refetch auction data to update UI with new bid and history
        const updatedAuction = await getAuctionById(auctionId);
        setAuction(updatedAuction);
        if (updatedAuction?.currentBid && updatedAuction?.bidIncrementAmount) {
          setNewBidAmount(updatedAuction.currentBid + updatedAuction.bidIncrementAmount);
        } else if (updatedAuction?.currentBid) {
          setNewBidAmount(updatedAuction.currentBid + 1);
        }
      } else {
        alert(`Teklif gönderilemedi: ${data.error}`);
      }
    } catch (error) {
      console.error('Teklif gönderme hatası:', error);
      alert('Teklif gönderilirken bir hata oluştu.');
    }
  };

  return (
    <div className="container mx-auto p-4 flex flex-col md:flex-row">
      {/* Left Section: Product Image */}
      <div className="md:w-1/3 p-4">
        {auction.image && (
          <Image
            src={urlForImage(auction.image).url()}
            alt={auction.name || 'Ürün Görseli'}
            width={400}
            height={300}
            className="rounded shadow-lg"
          />
        )}
      </div>

      {/* Right Section: Auction Details */}
      <div className="md:w-2/3 p-4">
        <h1 className="text-3xl font-bold mb-4">{auction.name}</h1>
        <p className="text-xl text-blue-600 mb-2">Mevcut Teklif: ₺{auction.currentBid}</p>
        <p className="text-md text-gray-700 mb-2">En Yüksek Teklif Sahibi: {highestBidder?.name || 'N/A'}</p>
        <p className="text-md text-gray-700 mb-4">Kalan Süre: {timeLeft}</p>
        
        <div className="mt-6">
          <h2 className="text-2xl font-semibold mb-3">Teklif Geçmişi</h2>
          <div className="max-h-60 overflow-y-auto border rounded-lg p-2 bg-gray-50">
            {auction.bidHistory && Array.isArray(auction.bidHistory) && auction.bidHistory.length > 0 ? (
              <ul className="space-y-2">
                {auction.bidHistory.sort((a, b) => (b.bidAmount || 0) - (a.bidAmount || 0))
                .map((bid, index) => (
                  <li key={bid._key} className={`p-2 rounded-md flex justify-between items-center ${index === 0 ? 'bg-green-100 border-l-4 border-green-500' : 'bg-white'}`}>
                    <div>
                      <p className="font-semibold text-gray-800">
                        {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
                        {(bid.bidder as any)?.name || 'Bilinmeyen Kullanıcı'}
                      </p>
                      <p className="text-sm text-gray-500">
                        {new Date(bid.bidTime || 0).toLocaleString('tr-TR', { day: '2-digit', month: 'long', year: 'numeric', hour: '2-digit', minute: '2-digit' })}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-lg text-green-600">₺{bid.bidAmount}</p>
                      {index === 0 && (
                        <span className="text-xs font-semibold text-white bg-green-500 px-2 py-1 rounded-full">En Yüksek Teklif</span>
                      )}
                    </div>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-center text-gray-500 py-4">Henüz teklif yok.</p>
            )}
          </div>
        </div>

        {/* Bid input and button */}
        <div className="mt-6">
          <input
            type="number"
            placeholder="Teklifinizi Girin"
            className="border p-2 rounded mr-2"
            step={auction.bidIncrementAmount || 1}
            value={newBidAmount}
            onChange={(e) => setNewBidAmount(Number(e.target.value))}
          />
          <button className="bg-green-500 text-white px-4 py-2 rounded" onClick={handlePlaceBid}>
            Teklif Yap
          </button>
        </div>
      </div>
    </div>
  );
} 