import { useQuery } from '@tanstack/react-query';
import { api } from '../lib/api';
import { 
  Giveaway, 
  GiveawayWithDetails,
  ApiResponse 
} from '../types/sanity';

// Query parameters interface
export interface GiveawayQueryParams {
  status?: 'upcoming' | 'active' | 'draw_ready' | 'completed' | 'cancelled';
  minPrice?: number;
  maxPrice?: number;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: 'startDate' | 'endDate' | 'ticketPrice' | 'ticketsSold';
  sortOrder?: 'asc' | 'desc';
  language?: 'tr' | 'en';
}

// API functions
const fetchGiveaways = async (params?: GiveawayQueryParams): Promise<Giveaway[]> => {
  const response = await api.get('/giveaways', params);
  return response.data;
};

const fetchGiveawayById = async (id: string): Promise<Giveaway | null> => {
  const response = await api.get(`/giveaways/${id}`);
  return response.data;
};

const fetchActiveGiveaways = async (): Promise<Giveaway[]> => {
  const response = await api.get('/giveaways/active');
  return response.data;
};

const fetchUpcomingGiveaways = async (): Promise<Giveaway[]> => {
  const response = await api.get('/giveaways/upcoming');
  return response.data;
};

const fetchCompletedGiveaways = async (): Promise<Giveaway[]> => {
  const response = await api.get('/giveaways/completed');
  return response.data;
};

const fetchUserGiveaways = async (userId: string): Promise<Giveaway[]> => {
  const response = await api.get(`/giveaways/user/${userId}`);
  return response.data;
};

const purchaseTickets = async (giveawayId: string, ticketCount: number, userId: string): Promise<{ success: boolean; tickets?: string[]; message?: string }> => {
  const response = await api.post(`/giveaways/${giveawayId}/purchase`, { ticketCount, userId });
  return response.data;
};

const drawWinners = async (giveawayId: string): Promise<{ success: boolean; winners?: any[]; message?: string }> => {
  const response = await api.post(`/giveaways/${giveawayId}/draw`);
  return response.data;
};

// React Query hooks
export const useGiveaways = (params?: GiveawayQueryParams) => {
  return useQuery<Giveaway[], Error>({
    queryKey: ['giveaways', params],
    queryFn: () => fetchGiveaways(params),
    staleTime: 3 * 60 * 1000, // 3 minutes
    retry: 2,
    refetchOnWindowFocus: false,
  });
};

export const useGiveaway = (id: string) => {
  return useQuery<Giveaway | null, Error>({
    queryKey: ['giveaway', id],
    queryFn: () => fetchGiveawayById(id),
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
    enabled: !!id,
    refetchInterval: 2 * 60 * 1000, // Auto-refresh every 2 minutes
  });
};

export const useActiveGiveaways = () => {
  return useQuery<Giveaway[], Error>({
    queryKey: ['giveaways', 'active'],
    queryFn: fetchActiveGiveaways,
    staleTime: 2 * 60 * 1000,
    retry: 2,
    refetchInterval: 2 * 60 * 1000,
  });
};

export const useUpcomingGiveaways = () => {
  return useQuery<Giveaway[], Error>({
    queryKey: ['giveaways', 'upcoming'],
    queryFn: fetchUpcomingGiveaways,
    staleTime: 5 * 60 * 1000,
    retry: 2,
  });
};

export const useCompletedGiveaways = () => {
  return useQuery<Giveaway[], Error>({
    queryKey: ['giveaways', 'completed'],
    queryFn: fetchCompletedGiveaways,
    staleTime: 10 * 60 * 1000,
    retry: 2,
  });
};

export const useUserGiveaways = (userId: string) => {
  return useQuery<Giveaway[], Error>({
    queryKey: ['giveaways', 'user', userId],
    queryFn: () => fetchUserGiveaways(userId),
    staleTime: 3 * 60 * 1000,
    retry: 2,
    enabled: !!userId,
  });
};

// Utility hooks
export const useGiveawayStatuses = () => {
  const statuses = [
    { value: 'upcoming', label: 'Yaklaşan', color: 'blue' },
    { value: 'active', label: 'Aktif', color: 'green' },
    { value: 'draw_ready', label: 'Çekiliş Hazır', color: 'orange' },
    { value: 'completed', label: 'Tamamlandı', color: 'gray' },
    { value: 'cancelled', label: 'İptal Edildi', color: 'red' },
  ] as const;

  return statuses;
};

// Helper functions
export const getLocalizedGiveawayTitle = (
  title: { tr: string; en?: string } | undefined,
  language: 'tr' | 'en' = 'tr'
): string => {
  if (!title) return '';
  return language === 'en' && title.en ? title.en : title.tr;
};

export const getLocalizedGiveawayDescription = (
  description: { tr?: string; en?: string } | undefined,
  language: 'tr' | 'en' = 'tr'
): string => {
  if (!description) return '';
  return language === 'en' && description.en ? description.en : description.tr || '';
};

export const isGiveawayActive = (giveaway: Giveaway): boolean => {
  const now = new Date();
  const startDate = new Date(giveaway.startDate);
  const endDate = new Date(giveaway.endDate);
  
  return giveaway.status === 'active' && now >= startDate && now <= endDate;
};

export const isGiveawayUpcoming = (giveaway: Giveaway): boolean => {
  const now = new Date();
  const startDate = new Date(giveaway.startDate);
  
  return giveaway.status === 'upcoming' && now < startDate;
};

export const isGiveawayCompleted = (giveaway: Giveaway): boolean => {
  return giveaway.status === 'completed';
};

export const isDrawReady = (giveaway: Giveaway): boolean => {
  const salePercentage = (giveaway.ticketsSold / giveaway.totalTickets) * 100;
  return salePercentage >= giveaway.ticketSalePercentageForDraw;
};

export const getGiveawayTimeRemaining = (giveaway: Giveaway): { days: number; hours: number; minutes: number; seconds: number } => {
  const now = new Date();
  const endDate = new Date(giveaway.endDate);
  const diff = endDate.getTime() - now.getTime();
  
  if (diff <= 0) {
    return { days: 0, hours: 0, minutes: 0, seconds: 0 };
  }
  
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((diff % (1000 * 60)) / 1000);
  
  return { days, hours, minutes, seconds };
};

export const formatTicketPrice = (price: number, currency: string = 'TRY'): string => {
  const symbols = { TRY: '₺', USD: '$', EUR: '€' };
  const symbol = symbols[currency as keyof typeof symbols] || currency;
  
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: currency,
    currencyDisplay: 'symbol',
  }).format(price).replace(currency, symbol);
};

export const getSalePercentage = (giveaway: Giveaway): number => {
  return (giveaway.ticketsSold / giveaway.totalTickets) * 100;
};

export const getRemainingTickets = (giveaway: Giveaway): number => {
  return giveaway.totalTickets - giveaway.ticketsSold;
};

export const canPurchaseTickets = (giveaway: Giveaway, ticketCount: number, userId?: string): { canPurchase: boolean; reason?: string } => {
  if (!isGiveawayActive(giveaway)) {
    return { canPurchase: false, reason: 'Çekiliş aktif değil' };
  }
  
  if (ticketCount <= 0) {
    return { canPurchase: false, reason: 'Geçersiz bilet sayısı' };
  }
  
  if (ticketCount > getRemainingTickets(giveaway)) {
    return { canPurchase: false, reason: 'Yeterli bilet kalmamış' };
  }
  
  if (ticketCount > giveaway.maxTicketsPerUser) {
    return { canPurchase: false, reason: `Maksimum ${giveaway.maxTicketsPerUser} bilet alabilirsiniz` };
  }
  
  // Check user's current ticket count
  if (userId && giveaway.participants) {
    const userParticipation = giveaway.participants.find(p => p.user._ref === userId);
    const currentTicketCount = userParticipation?.tickets.length || 0;
    
    if (currentTicketCount + ticketCount > giveaway.maxTicketsPerUser) {
      return { canPurchase: false, reason: `Toplam ${giveaway.maxTicketsPerUser} bilet sınırını aşamazsınız` };
    }
  }
  
  return { canPurchase: true };
};

export const getUserTicketCount = (giveaway: Giveaway, userId: string): number => {
  if (!giveaway.participants) return 0;
  const userParticipation = giveaway.participants.find(p => p.user._ref === userId);
  return userParticipation?.tickets.length || 0;
};

export const getUserTickets = (giveaway: Giveaway, userId: string): string[] => {
  if (!giveaway.participants) return [];
  const userParticipation = giveaway.participants.find(p => p.user._ref === userId);
  return userParticipation?.tickets.map(t => t.ticketNumber) || [];
};

export const isUserWinner = (giveaway: Giveaway, userId: string): boolean => {
  if (!giveaway.winners) return false;
  return giveaway.winners.some(winner => winner.user._ref === userId);
};

export const getUserWinnings = (giveaway: Giveaway, userId: string) => {
  if (!giveaway.winners) return [];
  return giveaway.winners.filter(winner => winner.user._ref === userId);
};

export const getGiveawayStatusColor = (status: string): string => {
  const statusColors: Record<string, string> = {
    upcoming: '#3b82f6',
    active: '#10b981',
    draw_ready: '#f59e0b',
    completed: '#6b7280',
    cancelled: '#ef4444',
  };
  return statusColors[status] || '#6b7280';
};

export const generateTicketNumber = (digitLength: number): string => {
  const max = Math.pow(10, digitLength) - 1;
  const min = Math.pow(10, digitLength - 1);
  return String(Math.floor(Math.random() * (max - min + 1)) + min).padStart(digitLength, '0');
};

// Filter and sort helpers
export const filterGiveawaysByStatus = (giveaways: Giveaway[], status: string): Giveaway[] => {
  return giveaways.filter(giveaway => giveaway.status === status);
};

export const sortGiveaways = (
  giveaways: Giveaway[], 
  sortBy: 'startDate' | 'endDate' | 'ticketPrice' | 'ticketsSold' = 'endDate',
  sortOrder: 'asc' | 'desc' = 'asc'
): Giveaway[] => {
  return [...giveaways].sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'startDate':
        comparison = new Date(a.startDate).getTime() - new Date(b.startDate).getTime();
        break;
      case 'endDate':
        comparison = new Date(a.endDate).getTime() - new Date(b.endDate).getTime();
        break;
      case 'ticketPrice':
        comparison = a.ticketPrice - b.ticketPrice;
        break;
      case 'ticketsSold':
        comparison = a.ticketsSold - b.ticketsSold;
        break;
    }
    
    return sortOrder === 'desc' ? -comparison : comparison;
  });
};
